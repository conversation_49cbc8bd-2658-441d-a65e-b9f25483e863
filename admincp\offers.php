<?php
ob_start();
$Title_page = 'طلبات التجارى' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
?>
<style>
	.off1 b{
		color: #FF9800;
	}
	.off2 b{
		color: #01a8fe;
	}
	.panel-body p{
		color: #ddd;
	}
	.panel-title i{
		margin-left : 5px; 
		color: #FF9800;
	}
	.colr1{
		font-size: 12px;
    margin-right: 10px;
    color: #FFC107;
	}
	.colr2{
		font-size: 12px;
    margin-right: 10px;
    color: #4CAF50;
	}
	.colr3{
		font-size: 12px;
    margin-right: 10px;
    color: #F44336;
	}
	textarea{
		width : 100%;
		max-width : 100%;
		min-width : 100%;
	}
</style>
<?php
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['paid'] >= 0){
			UpdateTable('offers' , 'paid' ,($ch[0]['paid']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['paid'] > 0){
			UpdateTable('offers' , 'paid' ,($ch[0]['paid']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('offers' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('offers' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'offers', 'WHERE id = '.$_GET['id'] );
			DeleteColum( 'offers_comments', 'WHERE offerid = '.$_GET['id'] );
			
			header('Location: offers.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'approve' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		UpdateTable('offers' , 'status' ,1, 'WHERE id = '.$_GET['id'] );
		header('Location: offers.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'close' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		UpdateTable('offers' , 'status' ,2 , 'WHERE id = '.$_GET['id'] );
		header('Location: offers.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
if (count($ch) > 0 ){
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body text-center">

			 <h3>هل انت متأكد من انك تريد الحذف ؟</h3>
			 <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>
			 <center>
				 <a class="btn btn-danger btn-lg" href="offers.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
				 <a class="btn btn-success btn-lg" href="offers.php">رجوع</a>
			 </center>

</div>	
</div>
</div>
</div>
<?php
}else{
		header('Location: offers.php'); exit();
} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'close' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
if (count($ch) > 0 ){
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body text-center">

			 <h3>هل انت متأكد من انك تريد إغلاق هذا العرض ؟</h3>
			 <p>برجاء العلم انه سيتم الإغلاق بشكل نهائى.</p>
			 <center>
				 <a class="btn btn-danger btn-lg" href="offers.php?action=close&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
				 <a class="btn btn-success btn-lg" href="offers.php">رجوع</a>
			 </center>

</div>	
</div>
</div>
</div>
<?php
}else{
		header('Location: offers.php'); exit();
} 
//---------------------------------------------------	
}elseif (isset($_GET['action']) && $_GET['action'] == 'show' && isset($_GET['id'])){
	$chx = getAllFrom('*' , 'offers' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($chx) > 0 ){
		?>
		<div class="row">
			<div class="col-md-12 col-lg-12">
					<?php
								$us = getAllFrom('*' , 'users' , 'WHERE id = "'.$chx[0]['userid'].'"', '');
								if (count($us) > 0){
									$cname = $us[0]['cname'];
									$cityid = $us[0]['cityid'];
									$phone = $us[0]['phone'];
								}else{
									$cname = 'غير معروف';
									$cityid = 0;
									$phone = 'غير معروف';
								}
								$cty = getAllFrom('*' , 'cities' , 'WHERE id = "'.$cityid.'"', '');
								if (count($cty) > 0){
									$cityname = $cty[0]['name'];
								}else{
									$cityname = 'غير معروف';
								}
								
								$co = 0 ;
								$cof = getAllFrom('id' , 'offers_comments' , 'WHERE offerid = "'.$chx[0]['id'].'"', '');
								if (count($cof) > 0){
									$co = count($cof);
								}

								if ($chx[0]['status'] == 0 ){
									$stus = '<span class="colr1"> فى إنتظار موافقة الإدارة </span>';
								}elseif($chx[0]['status'] == 1 ){
									$stus = '<span class="colr2"> تلقى العروض </span>';	
								}elseif($chx[0]['status'] == 2 ){
									$stus = '<span class="colr3"> انتهى او مغلق </span>';	
								}else{
									$stus = '<span class="colr3"> غير معروف </span>';	
								}

								echo '<div class="panel panel-card recent-activites"> 
										<div class="panel-heading">
											<h5 class="panel-title"><a href=""><i class="fa fa-fighter-jet"></i> '.$chx[0]['offer'].'</a></h5>
										</div>
										<div class="panel-body off1 pad-0">
												<p><b>إسم الشركة : </b> '.$cname.'</p>
												<p><b>المحافظة : </b> '.$cityname.'</p>
												<p><b>رقم الموبايل : </b> '.$phone.'</p>
												<p><b>الوقت والتاريخ : </b> <i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$chx[0]['datee']).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$chx[0]['datee']).'</p>
												<p><b>عدد العروض : </b> '.$co.' عرض</p>
												<p><b>حالة الطلب : </b> '.$stus.'</p>
										</div>
								</div>';
					?>
			</div>
		</div>
		<?php if ($chx[0]['status'] == 1 && $chx[0]['userid'] != $User_ID && $User_Type < 2 ){ ?>
		<div class="row">
			<div class="col-md-12 col-lg-12">
					<h3>إضافة عرض لهذا الطلب</h3>
					<form method="post">
						<div class="col-md-12">
								<div class="form-group">
									<label class="form-control-label">تفاصيل العرض</label>
									<textarea style="min-height: 50px;" class="form-control" name="var1" placeholder="مثلا هذا الجهاز متوفر بسعر كذا ,,,"></textarea>
								</div>
						</div>
						
						
							<div class="col-md-12">
								<div class="form-group">       
									<input type="submit" value="إضافة العرض" name="add_new" class="btn btn-lg btn-primary">
								</div>
						</div>    
					</form>
				<?php
	        if (isset($_POST['add_new'])){
						$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						if (empty($var1)){
							echo '<div class="col-md-12">';
					    echo  Show_Alert('danger' , 'يجب عليك كتابة تفاصيل العرض');
					    echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO offers_comments ( userid , offerid , offer , datee ) 
							 VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4  )");  
							$stmt->execute(array(
					          'user_1' => $User_ID , 'user_2' =>  $chx[0]['id'] , 'user_3' =>  $var1 , 'user_4' => time())) ;
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة العرض الجديد بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة العرض خلال 2 ثانيه. ');
					        echo '</div>';  		 	
					        redirect_home ('back' , 2);  exit();
					        exit();
						}
	        }
	       ?>
			</div>
		</div>
				<?php } ?>
		<div class="row">
			<div class="col-md-12 col-lg-12"><br><hr>
					<h3>العروض التى تم تقديمها</h3><br>
							<?php
									$offers = getAllFrom('*' ,'offers_comments','WHERE offerid = "'.$chx[0]['id'].'"', 'ORDER BY id ASC');
									if (count($offers) > 0){
											for ($i=0; $i <= count($offers)-1 ; $i++) { 
												$us = getAllFrom('*' , 'users' , 'WHERE id = "'.$offers[$i]['userid'].'"', '');
												if (count($us) > 0){
													$cname = $us[0]['cname'];
													$cityid = $us[0]['cityid'];
													$phone = $us[0]['phone'];
												}else{
													$cname = 'غير معروف';
													$cityid = 0;
													$phone = 'غير معروف';
												}
												$cty = getAllFrom('*' , 'cities' , 'WHERE id = "'.$cityid.'"', '');
												if (count($cty) > 0){
													$cityname = $cty[0]['name'];
												}else{
													$cityname = 'غير معروف';
												}
												
												echo '<div class="panel panel-card recent-activites"> 
														<div class="panel-heading">
															<h5 class="panel-title"><i style="color: #01a8fe;" class="fa fa-comments-o"></i> '.$offers[$i]['offer'].'</h5>
														</div>
														<div class="panel-body off2 pad-0">
																<p><b>إسم الشركة : </b> '.$cname.'</p>
																<p><b>المحافظة : </b> '.$cityname.'</p>
																<p><b>رقم الموبايل : </b> '.$phone.'</p>
																<p><b>الوقت والتاريخ : </b> <i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$offers[$i]['datee']).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$offers[$i]['datee']).'</p>
														</div>
												</div>';
											}
									}else{
										echo  Show_Alert('info' , 'لم يتم تقديم اى عروض حتى الآن');
									}
							?>
					<br><br>
			</div>
		</div>

		<?php
	}else{
			header('Location: offers.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة طلب جهاز تجارى</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	        if (isset($_POST['add_new'])){
	          $var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;

						$cc = getAllFrom('*' , 'offers' , 'WHERE userid = "'.$User_ID.'" AND  offer = "'.$var1.'" AND status = 0 ', '');
						
						if (empty($var1)){
							echo '<div class="col-md-12">';
					    echo  Show_Alert('danger' , 'يجب عليك كتابة تفاصيل الطلب');
					    echo '</div>'; 
						}elseif (count($cc) > 0){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'عفوا . لديك نفس الطلب فى إنتظار موافقة الإدارة');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO offers ( userid , offer , datee ) 
							 VALUES (:user_1 ,:user_2 ,:user_3  )");  
							$stmt->execute(array(
					          'user_1' => $User_ID , 'user_2' =>  $var1 , 'user_3' => time())) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة طلب جديد بنجاح ،وهو الآن فى إنتظار موافقة الإدارة. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الطلبات خلال 2 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:2;url=offers.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">تفاصيل الطلب</label>
													<textarea style="min-height: 150px;" class="form-control" name="var1" placeholder="مثلا مطلوب جهاز شارب 1.5 حصان تربو"></textarea>
		                    </div>
	               	 	</div>
	               	 	
	               	 	
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
			<center>
				<a href="offers.php?type=0" class="btn btn-sm btn-primary">كل الطلبات</a>
				<a href="offers.php?type=1" class="btn btn-sm btn-primary">طلبات فى انتظار الموافقه</a>
				<a href="offers.php?type=2" class="btn btn-sm btn-primary">طلبات فى تلقى العروض</a>
				<a href="offers.php?type=3" class="btn btn-sm btn-primary">طلبات مغلقه</a>
			</center>
	</div>			
</div>	
<div class="row">
	<div class="col-md-12 col-lg-12"><hr>
			<?php
			$type = 0;
			if (isset($_GET['type'])){
				$type = $_GET['type'];
			}
			
			if ($type == 1){
				$pages = isset($_GET["pages"]) ? (int)$_GET["pages"] : 1 ;
				$per_page = 30 ;
				$start = ($pages > 1) ? ($pages * $per_page) - $per_page : 0 ;	
				$offers = getAllFrom('*' , 'offers' , 'WHERE status = 0 ', 'ORDER BY id DESC LIMIT '.$start.' , '.$per_page.' ');
				$total = count(getAllFrom('id' , 'offers' , 'WHERE status = 0 ', ''));
				$pagesx = ceil($total / $per_page);
			}elseif($type == 2){
				$pages = isset($_GET["pages"]) ? (int)$_GET["pages"] : 1 ;
				$per_page = 30 ;
				$start = ($pages > 1) ? ($pages * $per_page) - $per_page : 0 ;	
				$offers = getAllFrom('*' , 'offers' , 'WHERE status = 1', 'ORDER BY id DESC LIMIT '.$start.' , '.$per_page.' ');
				$total = count(getAllFrom('id' , 'offers' , 'WHERE status = 1', ''));
				$pagesx = ceil($total / $per_page);
			}elseif($type == 3){
				$pages = isset($_GET["pages"]) ? (int)$_GET["pages"] : 1 ;
				$per_page = 30 ;
				$start = ($pages > 1) ? ($pages * $per_page) - $per_page : 0 ;	
				$offers = getAllFrom('*' , 'offers' , 'WHERE status = 2', 'ORDER BY id DESC LIMIT '.$start.' , '.$per_page.' ');
				$total = count(getAllFrom('id' , 'offers' , 'WHERE status = 2', ''));
				$pagesx = ceil($total / $per_page);
			}else{
				$pages = isset($_GET["pages"]) ? (int)$_GET["pages"] : 1 ;
				$per_page = 30 ;
				$start = ($pages > 1) ? ($pages * $per_page) - $per_page : 0 ;	
				$offers = getAllFrom('*' , 'offers' , '', 'ORDER BY id DESC LIMIT '.$start.' , '.$per_page.' ');
				$total = count(getAllFrom('id' , 'offers' , '', ''));
				$pagesx = ceil($total / $per_page);
			}
			
			

			if (count($offers) > 0 ){
				for ($i=0; $i <= count($offers)-1; $i++) { 
						$us = getAllFrom('*' , 'users' , 'WHERE id = "'.$offers[$i]['userid'].'"', '');
						if (count($us) > 0){
							$cname = $us[0]['cname'];
							$cityid = $us[0]['cityid'];
							$phone = $us[0]['phone'];
						}else{
							$cname = 'غير معروف';
							$cityid = 0;
							$phone = 'غير معروف';
						}
						$cty = getAllFrom('*' , 'cities' , 'WHERE id = "'.$cityid.'"', '');
						if (count($cty) > 0){
							$cityname = $cty[0]['name'];
						}else{
							$cityname = 'غير معروف';
						}
						$action = '';
						if ($User_Type > 1 && $offers[$i]['status'] == 0 ){
							$action = $action .' <a href="offers.php?id='.$offers[$i]['id'].'&do=approve" class="btn btn-sm btn-warning"> الموافقه على الطلب </a>'; 
						}
						if ($User_Type > 1 && $offers[$i]['status'] == 1 ){
								
								$action = $action .' <a href="offers.php?id='.$offers[$i]['id'].'&action=show" class="btn btn-sm btn-primary">مشاهدة الطلب والعروض </a>'; 
								
								$action = $action .' <a href="offers.php?id='.$offers[$i]['id'].'&do=close" class="btn btn-sm btn-warning"> إغلاق الطلب </a>'; 
							
						}
						if ($User_Type > 1 ){
							$action = $action .' <a href="offers.php?id='.$offers[$i]['id'].'&do=del" class="btn btn-sm btn-danger"> حذف الطلب </a>'; 
						}

						if (!empty($action) ){
							$action = '<hr><p>'.$action.'</p>';
						}

						if ($offers[$i]['status'] == 0 ){
							$stus = '<span class="colr1"> فى إنتظار موافقة الإدارة </span>';
						}elseif($offers[$i]['status'] == 1 ){
							$stus = '<span class="colr2"> تلقى العروض </span>';	
						}elseif($offers[$i]['status'] == 2 ){
							$stus = '<span class="colr3"> انتهى او مغلق </span>';	
						}else{
							$stus = '<span class="colr3"> غير معروف </span>';	
						}

						$co = 0 ;
						$cof = getAllFrom('id' , 'offers_comments' , 'WHERE offerid = "'.$offers[$i]['id'].'"', '');
						if (count($cof) > 0){
							$co = count($cof);
						}

						echo '<div class="panel panel-card recent-activites"> 
								<div class="panel-heading">
									<h5 class="panel-title"><a href="offers.php?id='.$offers[$i]['id'].'&action=show"><i class="fa fa-fighter-jet"></i> '.$offers[$i]['offer'].'</a></h5>
								</div>
								<div class="panel-body off1 pad-0">
										<p><b>إسم الشركة : </b> '.$cname.'</p>
										<p><b>المحافظة : </b> '.$cityname.'</p>
										<p><b>رقم الموبايل : </b> '.$phone.'</p>
										<p><b>الوقت والتاريخ : </b> <i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$offers[$i]['datee']).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$offers[$i]['datee']).'</p>
										<p><b>عدد العروض : </b> '.$co.' عرض</p>
										<p><b>حالة الطلب : </b> '.$stus.'</p>
										'.$action.'
								</div>
						</div>';
				}

				if ($pagesx > 1 ){ 
					echo '<div class="col-md-12"><center><ul class="pagination pagination-sm text-center">';  
				  for ($pa=1; $pa <= $pagesx ; $pa++) {
						if ($pages == $pa ){ $cls = "active" ;}else{ $cls = "" ;} 
								echo '<li class="page-item '.$cls.'"><a class="page-link" href="offers.php?type='.$type.'&pages='.$pa.'">'.$pa.'</a></li>';		 
						}
					echo '</ul></center></div>';
				}	
			}else{
				echo  Show_Alert('info' , 'لا يوجد اى طلبات حتى الآن. ');
			}
			?>
	</div>
</div>



<?php
}
include('footer.php'); 
ob_end_flush();
?>
