<!DOCTYPE html>
<html lang="ar">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="description" content="<?php getDescr() ; ?>" />
<meta name="keywords" content="<?php getKeyWords() ; ?>" />
<meta name="author" content="shebox" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<link rel="icon" href="<?php echo $Site_URL;?>/favr.png" type="image/x-icon" />
<link rel="shortcut icon" href="<?php echo $Site_URL;?>/favr.png" type="image/x-icon" />
<meta property="fb:app_id" content="350685531728" />	
<meta property="og:url" content="<?php echo strip_fbclid($actual_link);?>" />
<meta property="og:type" content="website" />
<meta property="og:title" content="<?php getTitle() ?>">
<meta property="og:image" content="<?php getKImages() ?>" />
<meta property="og:image:alt" content="<?php getTitle() ?>" />
<meta property="og:image:type" content="image/PNG" />
<meta property="og:image:width" content="500" />
<meta property="og:image:height" content="500" />
<meta property="og:description" content="<?php getDescr() ; ?>">
<meta property="og:keywords" content="<?php getKeyWords() ; ?>">
<title><?php getTitle() ?></title>
<meta name="robots" content="INDEX,FOLLOW" />
<link href="https://fonts.googleapis.com/css?family=Cairo:400,700&display=swap" rel="stylesheet">
<!-- StyleSheets -->
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap-rtl.min.css">
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>

<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/slick-theme.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/font-awesome.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/animate.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/jquery.desoslide.min.css">
<link href="<?php echo $Site_URL;?>/layout/css/sweetalert2.min.css" type="text/css" rel="stylesheet">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/style.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/responsive.css"> 
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
<!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->

<style>
.contentz{
  width: 100%;
  display: inline-block;
  height: 25px;
  overflow: hidden;
}  
  .newdiv{
      display: inline-block;
width: 100%;
  }
.mb-2{
  margin-bottom: 20px;
}
.mb-3{
  margin-bottom: 30px;
}
.filter {
    width: 100%;
    padding: 0px 10px;
    background: #ffffff;
    border-radius: 10px;
    margin-top: 10px;
    margin-bottom: 20px;
    box-shadow: 0px 4px 20px #e3e3e3d1;
    text-align: right;
}
.filter h3{
  font-size: 18px;
  margin-bottom: 30px;
}
.filter h3 i{
  margin-left: 5px;
}
.filter .applybtn{
  font-size: 14px;
  background: #ef073d;
  color: #fff;
  padding: 7px 10px;
  border-radius: 5px;
  float: left;
  cursor: pointer;
  font-weight: normal;
}
.showhidefilterbtn{
  display: none;
}

@media screen and (max-width: 767px) {
  .showhidefilterbtn{
    display: inline-block;
    float: left;
    cursor: pointer;
    font-weight: normal;
    margin-right: 10px;
    font-size: 30px;
  }
  .hide_show_filter{
    width: 100%;
    display: inline-block;
    display: none;
  }
}
.applybtn2{
  font-size: 14px;
  background: #ef073d;
  color: #fff;
  padding: 7px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: normal;
  width: 100%;
  text-align: center;
  display: inline-block;
}
.applybtn2:active , .applybtn2:hover , .applybtn2:focus {
  color:#fff;
}
.pad7{
  padding-left: 7px;
  padding-right: 7px;
}
.checkbox_div{
  width: 100%;
  display: inline-block;
  position: relative;
}
.checkbox_div span{
  font-size: 13px;
  font-weight: bold;
  margin: 0;
  margin-right: 5px;
  position: absolute;
  top: 3px;
}
.checkbox_div input{
  width: 20px;
  height: 20px;
}
.dn{
  display: none !important;
}
.confirm-box{
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 0 10px #0000001a;
  margin-bottom: 16px;
  padding: 16px;
}
.confirm-box h3{
  font-size: 16px;
  margin: 10px 0px 20px 0px;
}
.confirm-box h4{
  font-size: 14px;
  margin-bottom: 20px;
}
.confirm-box h4 span{
  float: left;
}
.address-box{
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 0 10px #0000001a;
  margin-bottom: 16px; 
}
.address-box .panel-heading{
  background: #ffc107;
  border: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom: 1px solid #ccc;
}
.address-box .panel-heading span{
  float: left;
  font-size: 22px;
}
.address-box h4{
  font-size: 16px;
}
.fz12{
  font-size: 14px !important;
}
/* start input-range */
.input-range {
    padding-top: 15px;
    padding-bottom: 15px;
    position: relative;
    direction: rtl;
}
.input-range input[type="range"].multirange {
	width: 100%;
}
.input-range .range {
	display: flex;
	justify-content: space-between;
	font-family: Helvetica;
	font-size: 13px; 
    color: #777;
    font-size: 12px;
}
.input-range input[type="range"].multirange {
	padding: 0;
	--range-color: #ff9800;
	margin: 0;
	display: inline-block;
	vertical-align: top;
	-webkit-appearance: none;
	appearance: none;
	outline: none;
    cursor: pointer;
}
.input-range input[type="range"].multirange.original {
	position: absolute;
}
.input-range input[type="range"].multirange::-webkit-slider-runnable-track {
	background: linear-gradient(to left, #ddd, #ddd) no-repeat 0 50% / 100% 20%;
}
.input-range input[type="range"].multirange.original::-webkit-slider-thumb {
	position: relative;
	z-index: 2;
}
.input-range input[type="range"].multirange::-webkit-slider-thumb {
	position: relative;
	z-index: 2;
	-webkit-appearance: none;
	border-radius: 15px;
	border: none;
	display: block;
	background: var(--range-color);
	height: 15px;
	width: 15px;
}
.input-range input[type="range"].multirange.original::-moz-range-thumb {
	transform: scale(1);
	z-index: 1;
}
.input-range input[type="range"].multirange::-moz-range-track {
	border-color: transparent;
}
.input-range input[type="range"].multirange.ghost {
	position: relative;
	background: linear-gradient(to left,
        transparent var(--low),
        var(--range-color) 0,
        var(--range-color) var(--high),
        transparent 0 ) no-repeat 0 50% / 100% 20%;
}
.input-range input[type="range"].multirange.ghost::-webkit-slider-runnable-track {
	background: linear-gradient(to left,
        transparent var(--low),
        var(--range-color) 0,
        var(--range-color) var(--high),
        transparent 0 ) no-repeat 0 50% / 100% 20%;
}
.input-range input[type="range"].multirange.ghost::-moz-range-track {
	background: linear-gradient(to left,
        transparent var(--low),
        var(--range-color) 0,
        var(--range-color) var(--high),
        transparent 0 ) no-repeat 0 45% / 100% 40%;
}
.usbox span{
  display: none;
}
.cartimg{
  width: 50px;
  height: 50px;
  display: inline-block;
  margin-left: 10px;
  border-radius: 5px;
}
.form-control{
  height: 45px;
}

.TriSea-technologies-Switch > input[type="checkbox"] {
    display: none;   
}

.TriSea-technologies-Switch > label {
    cursor: pointer;
    height: 0px;
    position: relative; 
    width: 40px;  
}

.TriSea-technologies-Switch > label::before {
    background: rgb(0, 0, 0);
    box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    content: '';
    height: 16px;
    margin-top: -8px;
    position:absolute;
    opacity: 0.3;
    transition: all 0.4s ease-in-out;
    width: 40px;
}
.TriSea-technologies-Switch > label::after {
    background: rgb(255, 255, 255);
    border-radius: 16px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
    content: '';
    height: 24px;
    left: -4px;
    margin-top: -8px;
    position: absolute;
    top: -4px;
    transition: all 0.3s ease-in-out;
    width: 24px;
}
.TriSea-technologies-Switch > input[type="checkbox"]:checked + label::before {
    background: inherit;
    opacity: 0.5;
}
.TriSea-technologies-Switch > input[type="checkbox"]:checked + label::after {
    background: inherit;
    left: 20px;
}
.ml-20{
  margin-left: 20px;
}
.cart-h3{
  font-size: 13px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #3F51B5;
    width: 100%;
    background: #86e5c963;
    padding: 6px;
    margin-top: 1px;
    border-radius: 13px;
}
.cart-h3 img {
  max-width: 100%;
  width: 30px;
  height: 30px;
  padding: 3px;
}


.radio{
  width: 100%;
    display: inline-block;
    padding: 7px;
    border: 1px solid #ddd;
    border-radius: 15px;
}
.srb{
  width: 100%;
  display: inline-block;
}
.radio span{
  float: left;
    background: #5f2281a6;
    padding: 5px;
    color: #fff;
    border-radius: 9px;
    margin: 0px;
}
#r .radio{
  margin-top: -5px;
}

.fb-like {
  display: inline-block;
    padding: 0px 0px;
    color: white;
    border-radius: 35px;
    font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;
    font-size: 14px;
    border: 1px dashed #ddd;
}

.fb-like button {
  background: none;
    border: none;
    color: #303339;
    font-size: 16px;
    cursor: pointer;
    margin-left: 5px;
}

.fb-like button i {
  margin-right: 5px;
}
.fb-like button.active i {
  color: #f00;
}
/* end input-range */


.field-list {
  background-color: #f0f0f0;
    border-radius: 5px;
    padding: 13px;
    margin-top: 8px;
}
.field-list>div {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.field-list>div img {
    opacity: .05;
    width: 80px;
}
.field-list>div ul {
    align-items: flex-start;
    display: flex;
    flex: 1 0 auto;
    flex-direction: column;
    justify-content: flex-start;
    list-style: none;
    margin: 0;
    padding: 0 20px 0 0;
}
.field-list>div ul li {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    margin: 0 0 5px;
    width: 100%;
}
.field-list>div ul li div {
        background-color: #ffffff;
        margin: 0 0 5px;
        padding: 0;
        width: 100%;
        border: 1px solid #dcdcdc;
        text-align: center;
    }
.field-list>div ul li div:last-child {
    margin-left: 0;
}
.field-list>div ul li span {
    color: #696464;
    margin-left: 10px;
}
.field-list>div ul li b, .field-list>div ul li span {
    color: #4b4646;
    font-size: 14px;
} 

@media only screen and (max-width: 767px){
.field-list>div {
    flex-direction: column;
}
.field-list>div img {
    display: none;
}
.field-list>div ul {
    margin: 0;
    padding: 0;
    width: 100%;
}
.field-list>div ul li {
    flex-direction: column;
    margin: 0;
}
.field-list>div ul li div {
       background-color: #ffffff;
        margin: 0 0 5px;
        padding: 0;
        width: 100%;
        border-radius: 16px;

    }
.field-list>div ul li b, .field-list>div ul li span {
    display: block;
}



}
 
 
.profile-photo-div {
  position: relative;
  margin: 0;
  width: 250px;
  height: auto;
  overflow: hidden;
  border-radius: 10px;
  -webkit-transition: ease 0.3s;
  -o-transition: ease 0.3s;
  transition: ease 0.3s;
}

.profile-img-div {
  display: block;
  position: relative;
  overflow: hidden;
}

#loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: #00cccf;
  z-index: 10;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

#profile-img {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

#change-photo {
  display: none;
}

.profile-buttons-div {
  position: relative;
  display: block;
}

.button {
  position: relative;
  display: block;
  font-size: 15px;
  padding: 20px;
  text-align: center;
  color: white;
  background-color: #00cccf;
  cursor: pointer;
  -webkit-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.button:hover {
  letter-spacing: 1px;
}

.button:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  opacity: 0;
  -webkit-transition: 0.9s;
  -o-transition: 0.9s;
  transition: 0.9s;
}

.button:hover:after {
  -webkit-transform: scale(50);
  -ms-transform: scale(50);
  transform: scale(50);
  opacity: 1;
}

.button.half {
  width: 50%;
}

.green {
  background-color: #15ae6b;
}

.red {
  background-color: #ae0000;
}

#x-position {
  position: absolute;
  bottom: 5px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  display: none;
}

#y-position {
  position: absolute;
  right: -50px;
  top: 50%;
  -webkit-transform: translateY(-50%) rotate(90deg);
  -ms-transform: translateY(-50%) rotate(90deg);
  transform: translateY(-50%) rotate(90deg);
  display: none;
}

canvas {
  position: fixed;
  top: -2000px;
  left: -2000px;
  z-index: -1;
}

.profile-img-confirm {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.error {
  font-size: 13px;
  color: red;
  text-align: center;
  display: none;
}
#fixdaddcart , .navbarlink{
  display: none;
}
@media only screen and (max-width: 767px){
  #fixdaddcart {
    display: block;
    position: fixed;
      bottom: 0;
      z-index: 100;
      right: 0;
      left: 0;
      margin: 0px 20px auto;
      width: unset;
  }
  .navbarlink{
    width: 100%;
    display: inline-block;
    padding: 10px 0px;
  }
  .navbarlink a , .navbarlink a:active , .navbarlink a:hover , .navbarlink a:focus {
    color: #172232;
    font-weight: bold;
    padding: 2px 10px;
    border-radius: 5px;
    font-size: 14px;
  }
}

.iconss {
    display: inline-block;
    background: #ffffff;
    width: 100%;
    border: 1px solid #f5f5f5;
    box-shadow: 0px 0px 4px -1px #d5d5d5;
    padding: 0px;
    cursor: pointer;
    position: relative;
}
.iconss span{
  display: none;
}
.iconss.active{
  border: 1px solid #ddd;
}
.iconss.active span{
  display: block;
    position: absolute;
    left: 10px;
    top: 11px;
    font-size: 20px;
    color: #fe3434;
}
.iconss_data_1 , .iconss_data_2 , .iconss_data , .dno{
  display: none;
}
.sg{
  display: block;
}
.hidemorecontent {
  width: 100%;
  display: inline-block;
  height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}
.show_more_btn , .show_more_btn:focus , .show_more_btn:hover , .show_more_btn:active{
  color: #9f9f9f;
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 5px;
  margin-bottom: 5px;
  display: inline-block;
}
.ht34{
  height: 34px !important;
}
.coupon-nav {
    background: #142133;
    color: #fff;
    font-size: 14px;
    width: 100%;
    display: block;
    text-align: center;
    padding: 5px 10px;
}
.coupon-nav i{
  margin: 0px 5px;
  color: #FFC107;
}
.someoneeles{display: none;}
</style>
