<html>
<head>
<title>Element Index</title>
<link rel="stylesheet" type="text/css" href="media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top"></td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                      	    [ <a href="elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<a name="top"></a>
<h1>Index of all elements</h1>
	[ <a href="elementindex.html#a">a</a> ]
	[ <a href="elementindex.html#b">b</a> ]
	[ <a href="elementindex.html#c">c</a> ]
	[ <a href="elementindex.html#d">d</a> ]
	[ <a href="elementindex.html#e">e</a> ]
	[ <a href="elementindex.html#f">f</a> ]
	[ <a href="elementindex.html#g">g</a> ]
	[ <a href="elementindex.html#h">h</a> ]
	[ <a href="elementindex.html#i">i</a> ]
	[ <a href="elementindex.html#j">j</a> ]
	[ <a href="elementindex.html#k">k</a> ]
	[ <a href="elementindex.html#l">l</a> ]
	[ <a href="elementindex.html#m">m</a> ]
	[ <a href="elementindex.html#n">n</a> ]
	[ <a href="elementindex.html#p">p</a> ]
	[ <a href="elementindex.html#q">q</a> ]
	[ <a href="elementindex.html#r">r</a> ]
	[ <a href="elementindex.html#s">s</a> ]
	[ <a href="elementindex.html#t">t</a> ]
	[ <a href="elementindex.html#u">u</a> ]
	[ <a href="elementindex.html#w">w</a> ]
	[ <a href="elementindex.html#_">_</a> ]

  <hr />
	<a name="a"></a>
	<div>
		<h2>a</h2>
		<dl>
							<dt><b>a4Lines</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methoda4Lines">I18N_Arabic_Glyphs::a4Lines()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate the lines number of given Arabic text and font size that will  fit in A4 page size</dd>
							<dt><b>a4MaxChars</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methoda4MaxChars">I18N_Arabic_Glyphs::a4MaxChars()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Regression analysis calculate roughly the max number of character fit in  one A4 page line for a given font size.</dd>
							<dt><b>acceptedWord</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodacceptedWord">I18N_Arabic_AutoSummarize::acceptedWord()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Check some conditions to know if a given string is a formal valid word or not</dd>
							<dt><b>allForms</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodallForms">I18N_Arabic_Query::allForms()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get most possible Arabic lexical forms of user search keywords</dd>
							<dt><b>allWordForms</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodallWordForms">I18N_Arabic_Query::allWordForms()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get most possible Arabic lexical forms for a given word</dd>
							<dt><b>ar2en</b></dt>
				<dd>in file Transliteration.php, method <a href="I18N_Arabic/I18N_Arabic_Transliteration.html#methodar2en">I18N_Arabic_Transliteration::ar2en()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Transliterate Arabic string into English by render them in the  orthography of the English language</dd>
							<dt><b>AutoSummarize.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---AutoSummarize.php.html">AutoSummarize.php</a></dd>
							<dt><b>Arabic.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic.php.html">Arabic.php</a></dd>
							<dt><b>ArabicException</b></dt>
				<dd>in file Arabic.php, class <a href="I18N_Arabic/ArabicException.html">ArabicException</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Arabic Exception class defined by extending the built-in Exception class.</dd>
							<dt><b>arabicMonths</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodarabicMonths">I18N_Arabic_Date::arabicMonths()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Add Arabic month names to the replacement array</dd>
							<dt><b>arNum</b></dt>
				<dd>in file Transliteration.php, method <a href="I18N_Arabic/I18N_Arabic_Transliteration.html#methodarNum">I18N_Arabic_Transliteration::arNum()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Render numbers in given string using HTML entities that will show them as  Indian digits (i.e. ١, ٢, ٣, etc.) whatever browser language settings are  (if browser supports UTF-8 character set).</dd>
							<dt><b>autoload</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodautoload">I18N_Arabic::autoload()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Include file that include requested class</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="b"></a>
	<div>
		<h2>b</h2>
		<dl>
							<dt><b>bits2hex</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodbits2hex">I18N_Arabic_CompressStr::bits2hex()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert binary string into hexadecimal string</dd>
							<dt><b>bits2str</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodbits2str">I18N_Arabic_CompressStr::bits2str()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert binary string into textual string</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="c"></a>
	<div>
		<h2>c</h2>
		<dl>
							<dt><b>CharsetD.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---CharsetD.php.html">CharsetD.php</a></dd>
							<dt><b>CompressStr.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---CompressStr.php.html">CompressStr.php</a></dd>
							<dt><b>charName</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodcharName">I18N_Arabic_Normalise::charName()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return Arabic letter name in arabic.</dd>
							<dt><b>checkAr</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodcheckAr">I18N_Arabic_KeySwap::checkAr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate the log odd probability that inserted string from keyboard  is in Arabic language</dd>
							<dt><b>checkEn</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodcheckEn">I18N_Arabic_KeySwap::checkEn()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate the log odd probability that inserted string from keyboard  is in English language</dd>
							<dt><b>cleanCommon</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodcleanCommon">I18N_Arabic_AutoSummarize::cleanCommon()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extracting common Arabic words (roughly)  from input Arabic string (document content)</dd>
							<dt><b>compress</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodcompress">I18N_Arabic_CompressStr::compress()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compress the given string using the Huffman-like coding</dd>
							<dt><b>convertDate</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodconvertDate">I18N_Arabic_Mktime::convertDate()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This will convert given Hijri date (Islamic calendar) into Gregorian date</dd>
							<dt><b>coordinate2deg</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodcoordinate2deg">I18N_Arabic_Salat::coordinate2deg()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert coordinates presented in degrees, minutes and seconds  (i.e. 12°34'56&quot;S formula) into usual float number in degree unit scale  (i.e. -12.5822 value)</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="d"></a>
	<div>
		<h2>d</h2>
		<dl>
							<dt><b>Date.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Date.php.html">Date.php</a></dd>
							<dt><b>date</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methoddate">I18N_Arabic_Date::date()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Format a local time/date in Arabic string</dd>
							<dt><b>dateCorrection</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methoddateCorrection">I18N_Arabic_Date::dateCorrection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate Hijri calendar correction using Um-Al-Qura calendar information</dd>
							<dt><b>decodeEntities</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methoddecodeEntities">I18N_Arabic_Glyphs::decodeEntities()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Decode all HTML entities (including numerical ones) to regular UTF-8 bytes.</dd>
							<dt><b>decodeEntities2</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methoddecodeEntities2">I18N_Arabic_Glyphs::decodeEntities2()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Helper function for decodeEntities</dd>
							<dt><b>decompress</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methoddecompress">I18N_Arabic_CompressStr::decompress()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Uncompress a compressed string</dd>
							<dt><b>doNormalize</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoNormalize">I18N_Arabic_AutoSummarize::doNormalize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Normalized Arabic document</dd>
							<dt><b>doRateSummarize</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoRateSummarize">I18N_Arabic_AutoSummarize::doRateSummarize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Summarize percentage of the input Arabic string (document content) into output</dd>
							<dt><b>doSummarize</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddoSummarize">I18N_Arabic_AutoSummarize::doSummarize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Summarize input Arabic string (document content) into specific number of  sentences in the output</dd>
							<dt><b>draftStem</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methoddraftStem">I18N_Arabic_AutoSummarize::draftStem()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove less significant Arabic letter from given string (document content).</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="e"></a>
	<div>
		<h2>e</h2>
		<dl>
							<dt><b>en2ar</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methoden2ar">I18N_Arabic_Date::en2ar()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate English date/time terms into Arabic langauge</dd>
							<dt><b>en2ar</b></dt>
				<dd>in file Transliteration.php, method <a href="I18N_Arabic/I18N_Arabic_Transliteration.html#methoden2ar">I18N_Arabic_Transliteration::en2ar()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Transliterate English string into Arabic by render them in the  orthography of the Arabic language</dd>
							<dt><b>enNum</b></dt>
				<dd>in file Transliteration.php, method <a href="I18N_Arabic/I18N_Arabic_Transliteration.html#methodenNum">I18N_Arabic_Transliteration::enNum()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Render numbers in given string using HTML entities that will show them as  Arabic digits (i.e. 1, 2, 3, etc.) whatever browser language settings are  (if browser supports UTF-8 character set).</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="f"></a>
	<div>
		<h2>f</h2>
		<dl>
							<dt><b>fixKeyboardLang</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodfixKeyboardLang">I18N_Arabic_KeySwap::fixKeyboardLang()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method will automatically detect the language of content supplied  in the input string. It will return the suggestion of correct inserted text.</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="g"></a>
	<div>
		<h2>g</h2>
		<dl>
							<dt><b>Gender.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Gender.php.html">Gender.php</a></dd>
							<dt><b>Glyphs.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Glyphs.php.html">Glyphs.php</a></dd>
							<dt><b>getArrFields</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetArrFields">I18N_Arabic_Query::getArrFields()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Getting values of $_fields Array in array format</dd>
							<dt><b>getBrowserLang</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodgetBrowserLang">I18N_Arabic::getBrowserLang()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get web browser chosen/default language using ISO 639-1 codes (2-letter)</dd>
							<dt><b>getCharset</b></dt>
				<dd>in file CharsetD.php, method <a href="I18N_Arabic/I18N_Arabic_CharsetD.html#methodgetCharset">I18N_Arabic_CharsetD::getCharset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Find the most possible character set for given Arabic string in unknown  format</dd>
							<dt><b>getClassFile</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodgetClassFile">I18N_Arabic::getClassFile()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get sub class file path to be included (mapping between class name and  file name/path become independent now)</dd>
							<dt><b>getCode</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodgetCode">I18N_Arabic_Soundex::getCode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the soundex key calculation method used now</dd>
							<dt><b>getFeminine</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodgetFeminine">I18N_Arabic_Numbers::getFeminine()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the feminine flag of counted object</dd>
							<dt><b>getFormat</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodgetFormat">I18N_Arabic_Numbers::getFormat()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the grammer position flag of counted object</dd>
							<dt><b>getGlyphs</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methodgetGlyphs">I18N_Arabic_Glyphs::getGlyphs()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get glyphs</dd>
							<dt><b>getInputCharset</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodgetInputCharset">I18N_Arabic::getInputCharset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the charset used in the input Arabic strings</dd>
							<dt><b>getLang</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodgetLang">I18N_Arabic_Soundex::getLang()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the soundex key language used now</dd>
							<dt><b>getLanguage</b></dt>
				<dd>in file Hiero.php, method <a href="I18N_Arabic/I18N_Arabic_Hiero.html#methodgetLanguage">I18N_Arabic_Hiero::getLanguage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the output language</dd>
							<dt><b>getLen</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodgetLen">I18N_Arabic_Soundex::getLen()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the soundex key length used now</dd>
							<dt><b>getMetaKeywords</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodgetMetaKeywords">I18N_Arabic_AutoSummarize::getMetaKeywords()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Extract keywords from a given Arabic string (document content)</dd>
							<dt><b>getMode</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodgetMode">I18N_Arabic_Date::getMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Getting $mode value that refer to output mode format</dd>
							<dt><b>getMode</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetMode">I18N_Arabic_Query::getMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Getting $mode propority value that refer to search mode  [0 for OR logic | 1 for AND logic]</dd>
							<dt><b>getOrder</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodgetOrder">I18N_Arabic_Numbers::getOrder()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the ordering flag value</dd>
							<dt><b>getOrderBy</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetOrderBy">I18N_Arabic_Query::getOrderBy()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get more relevant order by section related to the user search keywords</dd>
							<dt><b>getOutputCharset</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodgetOutputCharset">I18N_Arabic::getOutputCharset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the charset used in the output Arabic strings</dd>
							<dt><b>getPrayTime</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime">I18N_Arabic_Salat::getPrayTime()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Alias for getPrayTime2 method</dd>
							<dt><b>getPrayTime2</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime2">I18N_Arabic_Salat::getPrayTime2()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate Salat times for the date set in setSalatDate methode, and  location set in setSalatLocation.</dd>
							<dt><b>getQibla</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodgetQibla">I18N_Arabic_Salat::getQibla()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine Qibla direction using basic spherical trigonometric formula</dd>
							<dt><b>getStrFields</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetStrFields">I18N_Arabic_Query::getStrFields()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Getting values of $_fields array in String format (comma delimated)</dd>
							<dt><b>getWhereCondition</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetWhereCondition">I18N_Arabic_Query::getWhereCondition()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Build WHERE section of the SQL statement using defind lex's rules, search  mode [AND | OR], and handle also phrases (inclosed by &quot;&quot;) using normal  LIKE condition to match it as it is.</dd>
							<dt><b>getWordLike</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetWordLike">I18N_Arabic_Query::getWordLike()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Search condition in SQL format for one word in all defind fields using  normal LIKE clause</dd>
							<dt><b>getWordRegExp</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodgetWordRegExp">I18N_Arabic_Query::getWordRegExp()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Search condition in SQL format for one word in all defind fields using  REGEXP clause and lex's rules</dd>
							<dt><b>gregToJd</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodgregToJd">I18N_Arabic_Date::gregToJd()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts a Gregorian date to Julian Day Count</dd>
							<dt><b>guess</b></dt>
				<dd>in file CharsetD.php, method <a href="I18N_Arabic/I18N_Arabic_CharsetD.html#methodguess">I18N_Arabic_CharsetD::guess()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Count number of hits for the most frequented letters in Arabic language  (Alef, Lam and Yaa), then calculate association ratio with each of  possible character set (UTF-8, Windows-1256 and ISO-8859-6)</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="h"></a>
	<div>
		<h2>h</h2>
		<dl>
							<dt><b>Hiero.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Hiero.php.html">Hiero.php</a></dd>
							<dt><b>header</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodheader">I18N_Arabic::header()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Send/set output charset in several output media in a proper way</dd>
							<dt><b>hex2bits</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodhex2bits">I18N_Arabic_CompressStr::hex2bits()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert hexadecimal string into binary string</dd>
							<dt><b>highlightRateSummary</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodhighlightRateSummary">I18N_Arabic_AutoSummarize::highlightRateSummary()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Highlight key sentences (summary) as percentage of the input string  (document content) using CSS and send the result back as an output.</dd>
							<dt><b>highlightSummary</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodhighlightSummary">I18N_Arabic_AutoSummarize::highlightSummary()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Highlight key sentences (summary) of the input string (document content)  using CSS and send the result back as an output</dd>
							<dt><b>highlightText</b></dt>
				<dd>in file WordTag.php, method <a href="I18N_Arabic/I18N_Arabic_WordTag.html#methodhighlightText">I18N_Arabic_WordTag::highlightText()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Highlighted all nouns in a given Arabic string</dd>
							<dt><b>hijriMonthDays</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodhijriMonthDays">I18N_Arabic_Mktime::hijriMonthDays()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate how many days in a given Hijri month</dd>
							<dt><b>hjConvert</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodhjConvert">I18N_Arabic_Date::hjConvert()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert given Gregorian date into Hijri date</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="i"></a>
	<div>
		<h2>i</h2>
		<dl>
							<dt><b>Identifier.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Identifier.php.html">Identifier.php</a></dd>
							<dt><b>I18N_Arabic</b></dt>
				<dd>in file Arabic.php, class <a href="I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Core PHP and Arabic language class</dd>
							<dt><b>I18N_Arabic_AutoSummarize</b></dt>
				<dd>in file AutoSummarize.php, class <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class do automatic keyphrase extraction to provide a quick  mini-summary for a long Arabic document</dd>
							<dt><b>I18N_Arabic_CharsetD</b></dt>
				<dd>in file CharsetD.php, class <a href="I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class detect Arabic string character set</dd>
							<dt><b>I18N_Arabic_CompressStr</b></dt>
				<dd>in file CompressStr.php, class <a href="I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class compress Arabic string using Huffman-like coding</dd>
							<dt><b>I18N_Arabic_Date</b></dt>
				<dd>in file Date.php, class <a href="I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class is an Arabic customization for PHP date function</dd>
							<dt><b>I18N_Arabic_Gender</b></dt>
				<dd>in file Gender.php, class <a href="I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class attempts to guess the gender of Arabic names</dd>
							<dt><b>I18N_Arabic_Glyphs</b></dt>
				<dd>in file Glyphs.php, class <a href="I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class render Arabic text by performs Arabic glyph joining on it</dd>
							<dt><b>I18N_Arabic_Hiero</b></dt>
				<dd>in file Hiero.php, class <a href="I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate English word into Hieroglyphics</dd>
							<dt><b>I18N_Arabic_Identifier</b></dt>
				<dd>in file Identifier.php, class <a href="I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class identify Arabic text segments</dd>
							<dt><b>I18N_Arabic_KeySwap</b></dt>
				<dd>in file KeySwap.php, class <a href="I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class convert keyboard language programmatically (English - Arabic)</dd>
							<dt><b>I18N_Arabic_Mktime</b></dt>
				<dd>in file Mktime.php, class <a href="I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class is an Arabic customization for PHP mktime function</dd>
							<dt><b>I18N_Arabic_Normalise</b></dt>
				<dd>in file Normalise.php, class <a href="I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This class provides various functions to manipulate arabic text and   normalise it by applying filters, for example, to strip tatweel and   tashkeel, to normalise hamza and lamalephs, and to unshape   a joined Arabic text back into its normalised form.</dd>
							<dt><b>I18N_Arabic_Numbers</b></dt>
				<dd>in file Numbers.php, class <a href="I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class spell numbers in the Arabic idiom</dd>
							<dt><b>I18N_Arabic_Query</b></dt>
				<dd>in file Query.php, class <a href="I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class build WHERE condition for SQL statement using MySQL REGEXP and  Arabic lexical  rules</dd>
							<dt><b>I18N_Arabic_Salat</b></dt>
				<dd>in file Salat.php, class <a href="I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class calculate the time of Muslim prayer according to the geographic  location.</dd>
							<dt><b>I18N_Arabic_Soundex</b></dt>
				<dd>in file Soundex.php, class <a href="I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class implement Arabic soundex algorithm</dd>
							<dt><b>I18N_Arabic_Standard</b></dt>
				<dd>in file Standard.php, class <a href="I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class standardize Arabic text</dd>
							<dt><b>I18N_Arabic_Stemmer</b></dt>
				<dd>in file Stemmer.php, class <a href="I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class get stem of an Arabic word</dd>
							<dt><b>I18N_Arabic_StrToTime</b></dt>
				<dd>in file StrToTime.php, class <a href="I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class parse about any Arabic textual datetime description into a  Unix timestamp</dd>
							<dt><b>I18N_Arabic_Transliteration</b></dt>
				<dd>in file Transliteration.php, class <a href="I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class transliterate English words into Arabic</dd>
							<dt><b>I18N_Arabic_WordTag</b></dt>
				<dd>in file WordTag.php, class <a href="I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This PHP class to tagging Arabic Word</dd>
							<dt><b>identify</b></dt>
				<dd>in file Identifier.php, method <a href="I18N_Arabic/I18N_Arabic_Identifier.html#methodidentify">I18N_Arabic_Identifier::identify()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Identify Arabic text in a given UTF-8 multi language string</dd>
							<dt><b>int2indic</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodint2indic">I18N_Arabic_Numbers::int2indic()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Represent integer number in Arabic-Indic digits using HTML entities</dd>
							<dt><b>int2str</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodint2str">I18N_Arabic_Numbers::int2str()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Spell integer number in Arabic idiom</dd>
							<dt><b>isAlef</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisAlef">I18N_Arabic_Normalise::isAlef()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Alef forms (i.e. ALEF, ALEF MADDA, ALEF HAMZA ABOVE,  ALEF HAMZA BELOW,ALEF WASLA, ALEF MAKSURA).</dd>
							<dt><b>isArabic</b></dt>
				<dd>in file Identifier.php, method <a href="I18N_Arabic/I18N_Arabic_Identifier.html#methodisArabic">I18N_Arabic_Identifier::isArabic()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Find out if given string is Arabic text or not</dd>
							<dt><b>isFemale</b></dt>
				<dd>in file Gender.php, method <a href="I18N_Arabic/I18N_Arabic_Gender.html#methodisFemale">I18N_Arabic_Gender::isFemale()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Check if Arabic word is feminine</dd>
							<dt><b>isForum</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodisForum">I18N_Arabic::isForum()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;There is still a lack of original, localized, high-quality content and  well-structured Arabic websites; This method help in tag HTML result pages  from Arabic forum to enable filter it in/out.</dd>
							<dt><b>isHamza</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisHamza">I18N_Arabic_Normalise::isHamza()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Hamza forms (i.e. HAMZA, WAW HAMZA, YEH HAMZA, HAMZA ABOVE,  HAMZA BELOW, ALEF HAMZA BELOW, ALEF HAMZA ABOVE).</dd>
							<dt><b>isHaraka</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisHaraka">I18N_Arabic_Normalise::isHaraka()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Harakat marks (i.e. FATHA, DAMMA, KASRA, SUKUN, TANWIN).</dd>
							<dt><b>islamicToJd</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodislamicToJd">I18N_Arabic_Date::islamicToJd()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert given Hijri date into Julian day</dd>
							<dt><b>islamicToJd</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodislamicToJd">I18N_Arabic_Mktime::islamicToJd()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This will convert given Hijri date (Islamic calendar) into Julian day</dd>
							<dt><b>isLigature</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisLigature">I18N_Arabic_Normalise::isLigature()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Ligatures like LamAlef (i.e. LAM ALEF, LAM ALEF HAMZA  ABOVE, LAM ALEF HAMZA BELOW, LAM ALEF MADDA ABOVE).</dd>
							<dt><b>isMoon</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisMoon">I18N_Arabic_Normalise::isMoon()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Moon letters.</dd>
							<dt><b>isNoun</b></dt>
				<dd>in file WordTag.php, method <a href="I18N_Arabic/I18N_Arabic_WordTag.html#methodisNoun">I18N_Arabic_WordTag::isNoun()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Check if given rabic word is noun or not</dd>
							<dt><b>isShortharaka</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisShortharaka">I18N_Arabic_Normalise::isShortharaka()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic short Harakat marks (i.e. FATHA, DAMMA, KASRA, SUKUN).</dd>
							<dt><b>isSmall</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisSmall">I18N_Arabic_Normalise::isSmall()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Small letters (i.e. SMALL ALEF, SMALL WAW, SMALL YEH).</dd>
							<dt><b>isSun</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisSun">I18N_Arabic_Normalise::isSun()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Sun letters.</dd>
							<dt><b>isTanwin</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisTanwin">I18N_Arabic_Normalise::isTanwin()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Tanwin marks (i.e. FATHATAN, DAMMATAN, KASRATAN).</dd>
							<dt><b>isTashkeel</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisTashkeel">I18N_Arabic_Normalise::isTashkeel()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Tashkeel marks (i.e. FATHA, DAMMA, KASRA, SUKUN,  SHADDA, FATHATAN, DAMMATAN, KASRATAN).</dd>
							<dt><b>isTehlike</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisTehlike">I18N_Arabic_Normalise::isTehlike()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Teh forms (i.e. TEH, TEH MARBUTA).</dd>
							<dt><b>isWawlike</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisWawlike">I18N_Arabic_Normalise::isWawlike()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Waw like forms (i.e. WAW, WAW HAMZA, SMALL WAW).</dd>
							<dt><b>isWeak</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisWeak">I18N_Arabic_Normalise::isWeak()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Weak letters (i.e. ALEF, WAW, YEH, ALEF_MAKSURA).</dd>
							<dt><b>isYehlike</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodisYehlike">I18N_Arabic_Normalise::isYehlike()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Checks for Arabic Yeh forms (i.e. YEH, YEH HAMZA, SMALL YEH, ALEF MAKSURA).</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="j"></a>
	<div>
		<h2>j</h2>
		<dl>
							<dt><b>jdToGreg</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodjdToGreg">I18N_Arabic_Mktime::jdToGreg()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Converts Julian Day Count to Gregorian date</dd>
							<dt><b>jdToIslamic</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodjdToIslamic">I18N_Arabic_Date::jdToIslamic()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert given Julian day into Hijri date</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="k"></a>
	<div>
		<h2>k</h2>
		<dl>
							<dt><b>KeySwap.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---KeySwap.php.html">KeySwap.php</a></dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="l"></a>
	<div>
		<h2>l</h2>
		<dl>
							<dt><b>length</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodlength">I18N_Arabic_CompressStr::length()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Retrieve the original string length</dd>
							<dt><b>lex</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodlex">I18N_Arabic_Query::lex()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method will implement various regular expressin rules based on  pre-defined Arabic lexical rules</dd>
							<dt><b>load</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodload">I18N_Arabic::load()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Load selected Arabic library and create an instance of its class</dd>
							<dt><b>loadExtra</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodloadExtra">I18N_Arabic_AutoSummarize::loadExtra()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Load enhanced Arabic stop words list</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="m"></a>
	<div>
		<h2>m</h2>
		<dl>
							<dt><b>Mktime.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Mktime.php.html">Mktime.php</a></dd>
							<dt><b>mapCode</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodmapCode">I18N_Arabic_Soundex::mapCode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Methode to get soundex/phonix numric code for given word</dd>
							<dt><b>minAcceptedRank</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodminAcceptedRank">I18N_Arabic_AutoSummarize::minAcceptedRank()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate minimum rank for sentences which will be including in the summary</dd>
							<dt><b>mktime</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodmktime">I18N_Arabic_Mktime::mktime()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This will return current Unix timestamp  for given Hijri date (Islamic calendar)</dd>
							<dt><b>mktimeCorrection</b></dt>
				<dd>in file Mktime.php, method <a href="I18N_Arabic/I18N_Arabic_Mktime.html#methodmktimeCorrection">I18N_Arabic_Mktime::mktimeCorrection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Calculate Hijri calendar correction using Um-Al-Qura calendar information</dd>
							<dt><b>money2str</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodmoney2str">I18N_Arabic_Numbers::money2str()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Spell number in Arabic idiom as money</dd>
							<dt><b>myErrorHandler</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodmyErrorHandler">I18N_Arabic::myErrorHandler()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Error handler function</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="n"></a>
	<div>
		<h2>n</h2>
		<dl>
							<dt><b>Normalise.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Normalise.php.html">Normalise.php</a></dd>
							<dt><b>Numbers.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Numbers.php.html">Numbers.php</a></dd>
							<dt><b>normalise</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodnormalise">I18N_Arabic_Normalise::normalise()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Takes a string, it applies the various filters in this class  to return a unicode normalised string suitable for activities  such as searching, indexing, etc.</dd>
							<dt><b>normaliseHamza</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodnormaliseHamza">I18N_Arabic_Normalise::normaliseHamza()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Normalise all Hamza characters to their corresponding aleph  character in an Arabic text.</dd>
							<dt><b>normaliseLamaleph</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodnormaliseLamaleph">I18N_Arabic_Normalise::normaliseLamaleph()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Unicode uses some special characters where the lamaleph and any  hamza above them are combined into one code point. Some input  system use them. This function expands these characters.</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="p"></a>
	<div>
		<h2>p</h2>
		<dl>
							<dt><b>preConvert</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methodpreConvert">I18N_Arabic_Glyphs::preConvert()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert Arabic Windows-1256 charset string into glyph joining in UTF-8  hexadecimals stream</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="q"></a>
	<div>
		<h2>q</h2>
		<dl>
							<dt><b>Query.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Query.php.html">Query.php</a></dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="r"></a>
	<div>
		<h2>r</h2>
		<dl>
							<dt><b>rankSentences</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodrankSentences">I18N_Arabic_AutoSummarize::rankSentences()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Ranks sentences in a given Arabic string (document content).</dd>
							<dt><b>rankWords</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodrankWords">I18N_Arabic_AutoSummarize::rankWords()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Ranks words in a given Arabic string (document content). That rank refers  to the frequency of that word appears in that given document.</dd>
							<dt><b>roughStem</b></dt>
				<dd>in file Stemmer.php, method <a href="I18N_Arabic/I18N_Arabic_Stemmer.html#methodroughStem">I18N_Arabic_Stemmer::roughStem()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get rough stem of the given Arabic word (under specific rules)</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="s"></a>
	<div>
		<h2>s</h2>
		<dl>
							<dt><b>Salat.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Salat.php.html">Salat.php</a></dd>
							<dt><b>Soundex.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Soundex.php.html">Soundex.php</a></dd>
							<dt><b>Standard.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Standard.php.html">Standard.php</a></dd>
							<dt><b>Stemmer.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Stemmer.php.html">Stemmer.php</a></dd>
							<dt><b>StrToTime.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---StrToTime.php.html">StrToTime.php</a></dd>
							<dt><b>search</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodsearch">I18N_Arabic_CompressStr::search()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Search a compressed string for a given word</dd>
							<dt><b>setArrFields</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodsetArrFields">I18N_Arabic_Query::setArrFields()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting value for $_fields array</dd>
							<dt><b>setCode</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodsetCode">I18N_Arabic_Soundex::setCode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the mapping code of the soundex key (default value is &quot;soundex&quot;)</dd>
							<dt><b>setConf</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodsetConf">I18N_Arabic_Salat::setConf()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting rest of Salat calculation configuration</dd>
							<dt><b>setDate</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodsetDate">I18N_Arabic_Salat::setDate()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting date of day for Salat calculation</dd>
							<dt><b>setFeminine</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodsetFeminine">I18N_Arabic_Numbers::setFeminine()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set feminine flag of the counted object</dd>
							<dt><b>setFormat</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodsetFormat">I18N_Arabic_Numbers::setFormat()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the grammar position flag of the counted object</dd>
							<dt><b>setInputCharset</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodsetInputCharset">I18N_Arabic::setInputCharset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set charset used in class input Arabic strings</dd>
							<dt><b>setLang</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodsetLang">I18N_Arabic_Soundex::setLang()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the language of the soundex key (default value is &quot;en&quot;)</dd>
							<dt><b>setLang</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodsetLang">I18N_Arabic_CompressStr::setLang()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set required encode and binary hash of most probably character in  selected language</dd>
							<dt><b>setLanguage</b></dt>
				<dd>in file Hiero.php, method <a href="I18N_Arabic/I18N_Arabic_Hiero.html#methodsetLanguage">I18N_Arabic_Hiero::setLanguage()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the output language</dd>
							<dt><b>setLen</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodsetLen">I18N_Arabic_Soundex::setLen()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the length of soundex key (default value is 4)</dd>
							<dt><b>setLocation</b></dt>
				<dd>in file Salat.php, method <a href="I18N_Arabic/I18N_Arabic_Salat.html#methodsetLocation">I18N_Arabic_Salat::setLocation()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting location information for Salat calculation</dd>
							<dt><b>setMode</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodsetMode">I18N_Arabic_Query::setMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting $mode propority value that refer to search mode  [0 for OR logic | 1 for AND logic]</dd>
							<dt><b>setMode</b></dt>
				<dd>in file Date.php, method <a href="I18N_Arabic/I18N_Arabic_Date.html#methodsetMode">I18N_Arabic_Date::setMode()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting value for $mode scalar</dd>
							<dt><b>setOrder</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodsetOrder">I18N_Arabic_Numbers::setOrder()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the ordering flag, is it normal number or ordering number</dd>
							<dt><b>setOutputCharset</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#methodsetOutputCharset">I18N_Arabic::setOutputCharset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set charset used in class output Arabic strings</dd>
							<dt><b>setStrFields</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#methodsetStrFields">I18N_Arabic_Query::setStrFields()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Setting value for $_fields array</dd>
							<dt><b>soundex</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodsoundex">I18N_Arabic_Soundex::soundex()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Arabic soundex algorithm takes Arabic word as an input and produces a  character string which identifies a set words that are (roughly)  phonetically alike.</dd>
							<dt><b>standard</b></dt>
				<dd>in file Standard.php, method <a href="I18N_Arabic/I18N_Arabic_Standard.html#methodstandard">I18N_Arabic_Standard::standard()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method will standardize Arabic text to follow writing standards  (just like magazine rules)</dd>
							<dt><b>stem</b></dt>
				<dd>in file Stemmer.php, method <a href="I18N_Arabic/I18N_Arabic_Stemmer.html#methodstem">I18N_Arabic_Stemmer::stem()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get rough stem of the given Arabic word</dd>
							<dt><b>str2bits</b></dt>
				<dd>in file CompressStr.php, method <a href="I18N_Arabic/I18N_Arabic_CompressStr.html#methodstr2bits">I18N_Arabic_CompressStr::str2bits()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert textual string into binary string</dd>
							<dt><b>str2graph</b></dt>
				<dd>in file Hiero.php, method <a href="I18N_Arabic/I18N_Arabic_Hiero.html#methodstr2graph">I18N_Arabic_Hiero::str2graph()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Translate Arabic or English word into Hieroglyphics</dd>
							<dt><b>str2int</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodstr2int">I18N_Arabic_Numbers::str2int()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert Arabic idiom number string into Integer</dd>
							<dt><b>stripTashkeel</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodstripTashkeel">I18N_Arabic_Normalise::stripTashkeel()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Strip all tashkeel characters from an Arabic text.</dd>
							<dt><b>stripTatweel</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodstripTatweel">I18N_Arabic_Normalise::stripTatweel()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Strip all tatweel characters from an Arabic text.</dd>
							<dt><b>strtotime</b></dt>
				<dd>in file StrToTime.php, method <a href="I18N_Arabic/I18N_Arabic_StrToTime.html#methodstrtotime">I18N_Arabic_StrToTime::strtotime()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;This method will parse about any Arabic textual datetime description into  a Unix timestamp</dd>
							<dt><b>subInt2str</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodsubInt2str">I18N_Arabic_Numbers::subInt2str()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Spell integer number in Arabic idiom</dd>
							<dt><b>summarize</b></dt>
				<dd>in file AutoSummarize.php, method <a href="I18N_Arabic/I18N_Arabic_AutoSummarize.html#methodsummarize">I18N_Arabic_AutoSummarize::summarize()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Core summarize function that implement required steps in the algorithm</dd>
							<dt><b>swapAe</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodswapAe">I18N_Arabic_KeySwap::swapAe()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Make conversion to swap that odd Arabic text by original English sentence  you meant when you type on your keyboard (if keyboard language was  incorrect)</dd>
							<dt><b>swapAf</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodswapAf">I18N_Arabic_KeySwap::swapAf()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Make conversion to swap that odd Arabic text by original French sentence  you meant when you type on your keyboard (if keyboard language was  incorrect)</dd>
							<dt><b>swapEa</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodswapEa">I18N_Arabic_KeySwap::swapEa()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Make conversion to swap that odd English text by original Arabic sentence  you meant when you type on your keyboard (if keyboard language was  incorrect)</dd>
							<dt><b>swapFa</b></dt>
				<dd>in file KeySwap.php, method <a href="I18N_Arabic/I18N_Arabic_KeySwap.html#methodswapFa">I18N_Arabic_KeySwap::swapFa()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Make conversion to swap that odd French text by original Arabic sentence  you meant when you type on your keyboard (if keyboard language was  incorrect)</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="t"></a>
	<div>
		<h2>t</h2>
		<dl>
							<dt><b>Transliteration.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---Transliteration.php.html">Transliteration.php</a></dd>
							<dt><b>tagText</b></dt>
				<dd>in file WordTag.php, method <a href="I18N_Arabic/I18N_Arabic_WordTag.html#methodtagText">I18N_Arabic_WordTag::tagText()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Tag all words in a given Arabic string if they are nouns or not</dd>
							<dt><b>trimRep</b></dt>
				<dd>in file Soundex.php, method <a href="I18N_Arabic/I18N_Arabic_Soundex.html#methodtrimRep">I18N_Arabic_Soundex::trimRep()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove any characters replicates</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="u"></a>
	<div>
		<h2>u</h2>
		<dl>
							<dt><b>unichr</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodunichr">I18N_Arabic_Normalise::unichr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return unicode char by its code point.</dd>
							<dt><b>unshape</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodunshape">I18N_Arabic_Normalise::unshape()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Takes Arabic text in its joined form, it untangles the characters  and  unshapes them.</dd>
							<dt><b>utf8Glyphs</b></dt>
				<dd>in file Glyphs.php, method <a href="I18N_Arabic/I18N_Arabic_Glyphs.html#methodutf8Glyphs">I18N_Arabic_Glyphs::utf8Glyphs()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert Arabic Windows-1256 charset string into glyph joining in UTF-8  hexadecimals stream (take care of whole the document including English  sections as well as numbers and arcs etc...)</dd>
							<dt><b>utf8Strrev</b></dt>
				<dd>in file Normalise.php, method <a href="I18N_Arabic/I18N_Arabic_Normalise.html#methodutf8Strrev">I18N_Arabic_Normalise::utf8Strrev()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Take a UTF8 string and reverse it.</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="w"></a>
	<div>
		<h2>w</h2>
		<dl>
							<dt><b>WordTag.php</b></dt>
				<dd>procedural page <a href="I18N_Arabic/_Arabic---WordTag.php.html">WordTag.php</a></dd>
							<dt><b>writtenBlock</b></dt>
				<dd>in file Numbers.php, method <a href="I18N_Arabic/I18N_Arabic_Numbers.html#methodwrittenBlock">I18N_Arabic_Numbers::writtenBlock()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Spell sub block number of three digits max in Arabic idiom</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
  <hr />
	<a name="_"></a>
	<div>
		<h2>_</h2>
		<dl>
							<dt><b>__call</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#method__call">I18N_Arabic::__call()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Magic method __call() allows to capture invocation of non existing methods.</dd>
							<dt><b>__construct</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/ArabicException.html#method__construct">ArabicException::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Make sure everything is assigned properly</dd>
							<dt><b>__construct</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#method__construct">I18N_Arabic::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Load selected library/class you would like to use its functionality</dd>
							<dt><b>__construct</b></dt>
				<dd>in file Query.php, method <a href="I18N_Arabic/I18N_Arabic_Query.html#method__construct">I18N_Arabic_Query::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Loads initialize values</dd>
							<dt><b>__destruct</b></dt>
				<dd>in file Arabic.php, method <a href="I18N_Arabic/I18N_Arabic.html#method__destruct">I18N_Arabic::__destruct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Garbage collection, release child objects directly</dd>
					</dl>
	</div>
	<a href="elementindex.html#top">top</a><br>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:46 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>