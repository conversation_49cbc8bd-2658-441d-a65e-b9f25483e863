<?php

ob_start();

$Title_page = 'الإعلانات';

include('../webset.php');

include('../session.php'); 

include('header.php'); 

include('navbar.php'); 



//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){

	$ch = getAllFrom('*' , 'ads' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['status'] == 0){

			UpdateTable('ads' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );

		}else{

			UpdateTable('ads' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'ads' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] >= 0){

			UpdateTable('ads' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'ads' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] > 0){

			UpdateTable('ads' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){

	$ch = getAllFrom('*' , 'ads' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		

			DeleteColum( 'ads', 'WHERE id = '.$_GET['id'] );

			

			header('Location: ads.php');	exit();

	}else{

		redirect_home ('back' , 0); exit();

	} 

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'ads' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

?>

<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body text-center">

		     

			     <h3>هل انت متأكد من انك تريد الحذف ؟</h3>

			     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

			     <br>

			     <center>

			     	<a class="btn btn-danger btn-lg" href="ads.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>

			     	<a class="btn btn-success btn-lg" href="ads.php">رجوع</a>

			     </center>

		 

			</div>	

		</div>

	</div>

</div>

<?php

	}else{

		header('Location: ads.php'); exit();

	} 

//---------------------------------------------------

}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){

	?>

	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">

					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة إعلان جديد  </h4>

				</div>

			</div>

		</div>

	</div>



	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">				

					<div class="col-md-6 col-md-offset-3">
						<?php

	                 if (isset($_POST['add_new'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = $_POST['var2'] ;

						if (empty($var1) || empty($var2)){
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول');
						}else{

							$stmt = $db->prepare("INSERT INTO ads ( type , code ) 
							 VALUES (:user_1 ,:user_2 )");  
							$stmt->execute(array(

					          'user_1' => $var1  , 'user_2' => $var2 )) ;

					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة الإعلان بنجاح');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الأعلانات خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=ads.php");
					        exit();
					    }
	                 }
	                 ?>
						<form method="post">

						

	                    <div class="form-group">
	                      <label class="form-control-label">نوع الأعلان</label>
	                      <select class="form-control" name="var1">
	                      	<option value="h">إعلان فى الهيدر</option>
	                      	<option value="c">إعلان فى الأقسام</option>
	                      	<?php
	                      		$cat = getAllFrom('*' , 'category' , 'WHERE parent = 0 ', 'ORDER BY orders DESC , id DESC');
								if (count($cat) > 0){
									for ($i=0; $i <= count($cat)-1 ; $i++) { 
										echo '<option value="'.$cat[$i]['id'].'">إعلان أسفل قسم   '.$cat[$i]['name'].'</option>';
									}
								}
	                      	?>
	                      </select>
	                    </div>

	                    <div class="form-group">
	                      <label class="form-control-label">كود الإعلان </label>
	                      <textarea class="form-control ltr" style="width: 100%; max-width: 100%; min-height: 100%; height: 150px; max-height: 150px; min-height: 150px;" name="var2"></textarea>
	                    </div>

	                    <div class="form-group">       
	                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
	                    </div>


	                 </form>
	                 	
	                 </div>	
				</div>

			</div>

		</div>

	</div>		

<?php	

//---------------------------------------------------	

}else{

?>

<div class="row">

	<div class="col-md-12 col-sm-12">

		<div class="card">

			<div class="card-body">

				<a href="ads.php?do=add_new" class="btn btn-lg btn-primary">إضافة  إعلان جديد</a>

			</div>

		</div>
		<hr>
	</div>			

</div>





	<?php

	$ch = getAllFrom('type' , 'ads' , '', 'GROUP BY type ORDER BY id ASC');
	if (count($ch) > 0){
		for ($x=0; $x <= count($ch)-1 ; $x++) { 
			echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card"><div class="card-body">';
				$check = getAllFrom('*' , 'ads' , 'WHERE type = "'.$ch[$x]['type'].'"', 'ORDER BY orders DESC ,id DESC');
				if (count($check) > 0){
					if ($ch[$x]['type'] == 'h'){
						echo '<h3>إعلانات فى الهيدر</h3>';
					}elseif ($ch[$x]['type'] == 'c'){
						echo '<h3>إعلانات فى  الأقسام</h3>';	
					}else{
						$cat = getAllFrom('*' , 'category' , 'WHERE id = "'.$ch[$x]['type'].'"', '');
						if (count($cat) > 0){
							$catname = $cat[0]['name'];
						}else{
							$catname = 'غير معروف';
						}
						echo '<h3>إعلانات إسفل قسم '.$catname.'</h3>';
					}
					for ($i=0; $i <= count($check)-1 ; $i++) { 
						if ($check[$i]['status'] == 0 ){ $tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
						}else{ $tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	 }
						echo '<div class="card"><div class="card-body"><br>';

						echo ' <a href="ads.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';

						echo ' <a href="ads.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';

						echo '<a href="ads.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-up"></i></a>  <a href="ads.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-down"></i></a>';
						echo '</div></div>';
					}
				}
			echo '<hr></div></div></div></div>';
		}
	}else{
		echo  Show_Alert('warning' , 'لا يوجد أى   إعلانات. ');
	}


}


include('footer.php'); 

ob_end_flush();

?>