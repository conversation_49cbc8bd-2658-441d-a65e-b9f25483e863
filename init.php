<?php
function getTitle() {
	global $Title_page ;
	global $Site_Name ;
	if (isset($Title_page)) {
		echo $Title_page .' | ' .$Site_Name;
	}else {
		echo $Site_Name ;
	}
}
//------------------------------------------------
function getDescr() {
  global $Page_Description ;
  global $Description ;
  if (isset($Page_Description)) {
    echo $Page_Description;
  }else {
    echo $Description ;
  }
}
//------------------------------------------------
function getKeyWords() {
  global $Page_KeyWords ;
  global $Keywords ;
  if (isset($Page_KeyWords)) {
    echo $Page_KeyWords;
  }else {
    echo  $Keywords ;
  }
}
//------------------------------------------------
function getKImages() {
  global $Page_images ;
  global $Site_URL ;
  global $Defphoto;
  if (isset($Page_images)) {
    echo $Page_images;
  }else {
    echo  $Site_URL.'/'. $Defphoto;
  }
}
//------------------------------------------------
function GetTableSet ($name) {
   include('config.php');
   $AllSettings = getAllFrom('*' , 'settings' , 'WHERE name = "'.$name.'" ', 'ORDER BY id DESC'); 
   if (count($AllSettings) > 0 ){ 
    return $AllSettings[0]['setis'] ;
   }
}
//------------------------------------------------  
function sec_to_time($seconds) { 
    $hours = floor($seconds / 3600); 
    $minutes = floor($seconds % 3600 / 60); 
    $seconds = $seconds % 60; 

    return sprintf("%d:%02d:%02d", $hours, $minutes, $seconds); 
} 
//-------------------------------------------
function sec_to_times($seconds) { 
    $hours = floor($seconds / 3600); 
    $minutes = floor($seconds % 3600 / 60); 
    $seconds = $seconds % 60; 

    return sprintf("%02d:%02d",  $minutes, $seconds); 
} 
//-------------------------------------------
function GetUserName($id){
  $user =  getAllFrom('*' , 'users' , 'WHERE id = "'.$id.'" ', '');
  if (count($user) > 0 ){
      return $user[0]['fullname'];
  }else{
    return 'غير معروف';
  }
}    
//-------------------------------------------
function get_client_ip() {
    $ipaddress = '';
    if (isset($_SERVER['HTTP_CLIENT_IP']))
        $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
    else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_X_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
    else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
        $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
    else if(isset($_SERVER['HTTP_FORWARDED']))
        $ipaddress = $_SERVER['HTTP_FORWARDED'];
    else if(isset($_SERVER['REMOTE_ADDR']))
        $ipaddress = $_SERVER['REMOTE_ADDR'];
    else
        $ipaddress = 'UNKNOWN';
    return $ipaddress;
}
//-----------------------------------------------------
function Show_Alert($cls , $mes){
  return '<div class="index_main">
          <div class="alert alert-'.$cls.' alert-dismissible" role="alert">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              </button>'.$mes.'</div></div>';
}
//-----------------------------------------------------
function generate_key($length = 50, $letters = true, $numbers = true, $case = 'i'){
  $chars = array();
   if ($numbers){
      $chars = array_merge($chars, range(48, 57));
   }
   if ($letters OR !$numbers){
      $chars = array_merge($chars, range(65, 90), range(97, 122));
   }

  for ($string = ''; strlen($string) < $length; $string .= chr($chars[array_rand($chars)]));
  switch ($case){
  case 'i': default: return $string;
  case 'u': return strtoupper($string);
  case 'l': return strtolower($string);
  }
}
//-----------------------------------------------------
function Generate_Hash(){
  $hash = generate_key(5 , false);
  $ch = getAllFrom('*' , 'orders' , 'WHERE orderhash = "'.$hash.'" ', '');
  if (count($ch) > 0 ){
    Generate_Hash();
  }else{
    return $hash ;
  }
}
//-----------------------------------------------------
function Share_Box(){
global $actual_link ;
  echo '
    <div class="share_box">
        <p>شارك التقرير</p>
        <a href="http://www.facebook.com/sharer/sharer.php?u='.$actual_link.'" class="ico ico-fb-share" title="فيس بوك" target="_blank">فيس بوك</a>
        <a href="https://twitter.com/share?url='.$actual_link.'" class="ico ico-tw-share" title="تويتر" target="_blank">تويتر</a>
        <a href="https://plus.google.com/share?url='.$actual_link.'" class="ico ico-gp-share" title="جوجل بلس" target="_blank">جوجل بلس</a>
      </div>
  ';
}
//-----------------------------------------------------

function GetOrderStatus($hash){
  $order = getAllFrom('*','orders' ,'WHERE orderhash = "'.$hash.'" ','');
  if (count($order) > 0){
      $st = $order[0]['status'];
      if ($st == 2){
        return '<p class="ostf"> الأوردر رقم <b>'.$hash.'</b> تم تسليمه بنجاح.</p>';
      }elseif($st == 3){
        return '<p class="ostf"> الأوردر رقم <b>'.$hash.'</b> ملغى.</p>';
      }else{
        return '<p class="ostf"> الأوردر رقم <b>'.$hash.'</b> فى الإنتظار.</p>';
      }
  }else{
    return '<p class="ostf"> رقم الأوردر <b>'.$hash.'</b> غير صحيح.<br>تأكد من رقم الأوردر وأعد المحاولة مره أخرى</p>';
  }
}
//-----------------------------------------------------

function checkItem ($select , $from , $value ){
include('config.php'); 
  $ststement = $db->prepare("SELECT $select FROM $from WHERE $select = ? ");
  $ststement -> execute(array($value)) ;
  $count = $ststement -> rowCount();
  return $count ;
}
//------------------------------------------------
function getAllFrom($select , $table , $where = NULL , $and = NULL){
include('config.php');
  $ststement = $db->prepare("SELECT $select FROM $table $where $and");
  $ststement -> execute() ;
  $all = $ststement -> fetchAll();
  return $all ;
}
function UpdateTable($table , $colum ,$value , $where = NULL){
  include('config.php'); 
  $stmt = $db->prepare("UPDATE ".$table." SET ".$colum." =  ".$value."  ".$where."  ");
  $stmt -> execute();  
}      

function DeleteColum($table , $where = NULL ){
  include('config.php');
  $stmt = $db -> prepare("DELETE FROM ".$table."  ".$where." ");
  $stmt->execute();
}   

function Update($name , $val){
  include('config.php');
  $AllSettings = getAllFrom('*' , 'settings' , 'WHERE name = "'.$name.'" ', 'ORDER BY id DESC'); 
   if (count($AllSettings) > 0 ){ 
     $stmt = $db->prepare("UPDATE  settings SET  setis = :edt1  WHERE  name = :edt5 ");  
     $stmt->execute(array('edt1' => $val , 'edt5' => $name ));
   }
}
//-----------------------------------------------------------------------

function redirect_home($URl = null , $SEconds = 0){

    if ($URl === null){
        $URl = 'index.php';

    }elseif ( isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '' ) {

      $URl = $_SERVER['HTTP_REFERER'];  

  

    }elseif ($URl == 'error') {
        $URl = 'error.php';
   
    }else {

    $URl = 'index.php';

}
header("refresh:$SEconds;url=$URl");
exit();
}
//------------------------------------------------------------
function randomGen($min, $max, $quantity) {
    $numbers = range($min, $max);
    shuffle($numbers);
    return array_slice($numbers, 0, $quantity);
}
//------------------------------------------------------------
 function makeLinks($s) {
    return preg_replace('@(https?://([-\w\.]+[-\w])+(:\d+)?(/([\w/_\.#-]*(\?\S+)?[^\.\s])?)?)@', '<a style="color:#132ba9" href="$1" target="_blank">$1</a>', $s);
}
//------------------------------------------------------------
function dateDiff($time1, $time2 = '' , $precision = 6) {
    // If not numeric then convert texts to unix timestamps
    if (!is_int($time1)) {
      $time1 = strtotime($time1);
    }
     if (!is_int($time2)) {
       $time2 = strtotime($time2);
     }
     if($time2 == ''){$time2=time();}
    
    // If time1 is bigger than time2
    // Then swap time1 and time2
    if ($time1 > $time2) {
      $ttime = $time1;
      $time1 = $time2;
      $time2 = $ttime;
    }

    // Set up intervals and diffs arrays
    
   
    $intervals = array('Year','Month','Day','Hour','Minute','Second');
    $diffs = array();

    // Loop thru all intervals
    foreach ($intervals as $interval) {
      // Create temp time from time1 and interval
      $ttime = strtotime('+1 ' . $interval, $time1);
      // Set initial values
      $add = 1;
      $looped = 0;
      // Loop until temp time is smaller than time2
      while ($time2 >= $ttime) {
        // Create new temp time from time1 and interval
        $add++;
        $ttime = strtotime("+" . $add . " " . $interval, $time1);
        $looped++;
      }
 
      $time1 = strtotime("+" . $looped . " " . $interval, $time1);
      $diffs[$interval] = $looped;
    }
    
    $count = 0;
    $times = array();
    // Loop thru all diffs
    foreach ($diffs as $interval => $value) {
      // Break if we have needed precission
      if ($count >= $precision) {
        break;
      }
      // Add value and interval 
      // if value is bigger than 0
      if ($value > 0) {
        // Add s if value is not 1
        if ($value != 1) {
          $interval .= "s";
        }
        // Add value and interval to times array
        $times[] = $value . " " . $interval;
        $count++;
      }
    }

    // Return string with times
    return implode(" - ", $times);
  }
//-----------------------------------------------------------------------
function Get_Date_String($date){
  $str_to_time = strtotime($date);
  $m = date('m' , $str_to_time);
  $d = date('d' , $str_to_time);
  $y = date('Y' , $str_to_time);
  $d_s = date('l' , $str_to_time);
  if ($m == 1){$m_s = 'يناير' ;
 }elseif ($m == 2){$m_s = 'فبراير' ;
 }elseif ($m == 3){$m_s = 'مارس' ;
 }elseif ($m == 4){$m_s = 'أبريل' ;
 }elseif ($m == 5){$m_s = 'مايو' ;
 }elseif ($m == 6){$m_s = 'يونيو' ;
 }elseif ($m == 7){$m_s = 'يوليو' ;
 }elseif ($m == 8){$m_s = 'أغسطس' ;
 }elseif ($m == 9){$m_s = 'سبتمبر' ;
 }elseif ($m == 10){$m_s = 'أكتوبر' ;
 }elseif ($m == 11){$m_s = 'نوفمبر' ;
 }elseif ($m == 12){$m_s = 'ديسمبر' ;
 }else{$m_s = 'غير معروف' ;}

 if ($d_s == 'Saturday' ){$d_s = 'السبت' ; 
}elseif ($d_s == 'Sunday' ){$d_s = 'الأحد' ;
}elseif ($d_s == 'Monday' ){$d_s = 'الإثنين' ;
}elseif ($d_s == 'Tuesday' ){$d_s = 'الثلاثاء' ;
}elseif ($d_s == 'Wednesday' ){$d_s = 'الأربعاء' ;
}elseif ($d_s == 'Thursday' ){$d_s = 'الخميس' ;
}elseif ($d_s == 'Friday' ){$d_s = 'الجمعه' ;
}else{$d_s = 'غير معروف';}

$apm = date('a' , $str_to_time);
if ($apm == 'am'){$apm = 'صباحاً' ;}elseif($apm == 'pm'){$apm = 'مساءً' ;}else{$apm = 'غير معروف';}

$tm = date('h:i' , $str_to_time);
 
  return ' <b>'.$d_s.'</b> '.$d.' '.$m_s.' '.$y.' <b> الساعه</b> '.$tm.' '.$apm.' ' ;
}

//-------------------------------------------------------------
function getExtensions($str) {
  $i = strrpos($str,".");
  if (!$i) { return ""; } 
  $l = strlen($str) - $i;
  $ext = substr($str,$i+1,$l);
  return $ext;
 }
//-------------------------------------------------------------
function Image_Create($img) {
    $ext = getExtensions($img);
    if ($ext == 'png'){
        return imagecreatefrompng($img);
    }elseif ($ext == 'jpeg' || $ext == 'jpg'){
        return imagecreatefromjpeg($img);   
    }elseif ($ext == 'gif'){
        return imagecreatefromgif($img);      
    }else{
        return false;
    }
} 
//-------------------------------------------------------------
//-------------------------------------------------------------
function Make_watermark($img1 , $img2 , $out  ){
    $stamp = Image_Create($img1);
    $im    = Image_Create($img2);
    $dest_sx = imagesx($stamp);
    $dest_sy = imagesy($stamp);
    $src_sx = imagesx($im);
    $src_sy = imagesy($im);
    $marge_right = abs($src_sx - $dest_sx);
    $marge_bottom = abs($src_sy - $dest_sy);
    imagecopy($im, $stamp, $marge_right , $marge_bottom , 0 , 0 , imagesx($stamp), imagesy($stamp));
    //imagecopy($im, $stamp,  ($src_sx-$dest_sx)/2 , ($src_sy-$dest_sy)/2  , 0 , 0 , imagesx($stamp), imagesy($stamp));
    imagepng($im, $out);
    return $out ;
    imagedestroy($im);
}
//-------------------------------------------------------------
function SetTextOnImage($TextContent , $FontFamily , $FontSize , $ImgSource , $FontColor , $ImgOut ){
    require('I18N/Arabic.php'); 
    $Arabic = new I18N_Arabic('Glyphs'); 
    $text = $Arabic->utf8Glyphs($TextContent);
    $im = imagecreatetruecolor(600, 300);
    $c = explode(',' , $FontColor); $c1 = 255; $c2 = 255; $c3 = 255; 
    try{ $c1 = $c[0]; $c2 = $c[1]; $c3 = $c[2]; }catch(Exception $e){}
    $white = imagecolorallocate($im, $c1 , $c2 , $c3);
    $im = imagecreatefromjpeg($ImgSource);
    $data = getimagesize($ImgSource);
    $centerX = $data[0] / 2;
    $centerY = $data[1] / 2;
    list($left, $bottom, $right, , , $top) = imageftbbox($FontSize, 0, $FontFamily, $text);
    $left_offset = ($right - $left) / 2;
    $top_offset = ($bottom - $top) / 2;
    $x = $centerX - $left_offset;
    $y = $centerY - $top_offset;
    // photo , font size , angle , x , y , color , font , text;
    imagettftext($im, $FontSize, 0, $x, $y+20 , $white, $FontFamily , $text);
    // Using imagepng() results in clearer text compared with imagejpeg()
    imagepng($im, $ImgOut);
    imagedestroy($im); 
}
//-------------------------------------------------------------
  function compress($source, $destination, $quality) {

    $info = getimagesize($source);

    if ($info['mime'] == 'image/jpeg') 
        $image = imagecreatefromjpeg($source);

    elseif ($info['mime'] == 'image/gif') 
        $image = imagecreatefromgif($source);

    elseif ($info['mime'] == 'image/png') 
        $image = imagecreatefrompng($source);

    imagejpeg($image, $destination, $quality);

    return $destination;
}

//-------------------------------------------------------------
function strip_fbclid($url) {
        $patterns = array(
                '/(\?|&)fbclid=[^&]*$/' => '',
                '/\?fbclid=[^&]*&/' => '?',
                '/&fbclid=[^&]*&/' => '&'
        );

        $search = array_keys($patterns);
        $replace = array_values($patterns);

        return preg_replace($search, $replace, $url);
}
