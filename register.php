<?php
ob_start();
$Title_page = 'تسجيل حساب جديد' ;
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');
if (isset($_SESSION['userData'])){ header("Location:".$Site_URL.'/'); exit();}
echo '<div class="container-fluid products-content">';
?>
<div class="row"> 
        <div class="contact">
        	<div class="col-md-4 col-md-offset-4">
        		<div class="signal">
                <h5 class="text-center" style="margin-bottom: 20px;"><?php echo $Title_page;?></h5>
                <form method="post">
                <div class="col-md-12 pading5">
    	                <div class="form-group">
    	                  <label class="form-control-label">الأسم</label>
	                  <input type="text" name="var1" class="form-control ltr">
	                </div>
	            </div>    
    			<div class="col-md-12 pading5">
    	                <div class="form-group">
    	                  <label class="form-control-label">رقم الجوال</label>
	                  <input type="text" name="var2" class="form-control ltr">
	                </div>
	            </div>
	            <div class="col-md-12 pading5">    
    	                <div class="form-group">
    	                  <label class="form-control-label">كلمة المرور</label>
	                  <input type="password" name="var3" class="form-control ltr">
	                </div>
                </div>
                
                <div class="col-md-12 pading5 dn">
    	                <div class="form-group">
    	                  <label class="form-control-label">العنوان</label>
	                  <input type="text" name="var5" class="form-control ltr">
	                </div>
	            </div>
	            <div class="col-md-12 pading5">    
    	                <div class="form-group">
    	                    <button type="submit" name="register" class="btn btn-danger btn-block ">تسجيل حساب جديد</button>
	                </div> 
	                <div class="form-group">
    	                 <a href="<?php echo $Site_URL.'/login.php' ;?>">لدى حساب بالفعل !</a>   
                    </div> 
                    <div class="form-group">
                        <?php
                        if (isset($_POST['register'])){
                            $var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
                            $var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;
                            $var3  = filter_var($_POST['var3']   , FILTER_SANITIZE_STRING) ; 
                            $var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;
    
                            $ch1 = getAllFrom('*' , 'users' , 'WHERE phone = "'.$var2.'" ', '');
                            if (count($ch1) > 0){
                                echo '<div class="col-md-12">';
                                echo  Show_Alert('danger' , 'رقم الموبايل موجود من قبل. ');
                                echo '</div>';  
                            }elseif(empty($var1) || empty($var2) || empty($var3) ){
                                echo '<div class="col-md-12">';
                                echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
                                echo '</div>';  
                            }else{
    
                                $stmt = $db->prepare("INSERT INTO users ( fullname , phone ,password   , adress  , datetime  ) 
                                 VALUES (:user_1 ,:user_2 ,:user_3 ,:user_5 ,:user_6 )");  
                                $stmt->execute(array(
                                  'user_1' => $var1  , 'user_2' => $var2 , 'user_3' => $var3  , 'user_5' => $var5 ,'user_6' => time() )) ;
                                  
                                $ruserid = $db->lastInsertId();  

                                $stmt = $db->prepare("UPDATE users SET ip = :var1 WHERE  id = :var0 ");  
                                $stmt->execute(array('var1' => get_client_ip() , 'var0' => $ruserid )); 
                                $_SESSION['userData'] = $ruserid;
                                echo  Show_Alert('info' , 'تم تسجيل الدخول بنجاح . سيتم تحويلك خلال 1 ثواني. ');
                                header('Location: '.$Site_URL.'/'); exit();
    
                             }
                         }
                        ?>
	                </div>   
                </div>
                </form>
    		</div>	
    	</div>
    </div>
</div>
<?php
    echo ' </div>';
    include('footer.php'); 
    ob_end_flush();
?>
