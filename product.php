<?php

use function PHPSTORM_META\type;

$product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$productid.'" ', '');
if (count($product) > 0){  

    $stmt = $db->prepare("UPDATE  products_title SET  views = :edt1  WHERE  id = :edt5 ");  
    $stmt->execute(array('edt1' => ($product[0]['views']+1), 'edt5' => $product[0]['id'] ));

    $photos = array();
    $def = GetTableSet ('DefaultImage');
    if (empty($product[0]['photo'])){
    $photos = array($def);
    }else{
    $p = explode('&&&', $product[0]['photo']);
    if(count($p) >0){
        for ($i=0; $i <= count($p)-1 ; $i++) { 
        if ($p[$i] != ""){
            $l = getAllFrom('*' , 'photos' , 'WHERE id ="'.$p[$i].'" ', '');
            if (count($l) >0){
            array_push($photos, $l[0]['link']);
            }
        }
        }
    }
    }

    $fromDate = date('Y-m-d', strtotime('+3 days'));
    $toDate = date('Y-m-d', strtotime('+5 days'));
    


  $catname = '';
  $canm = getAllFrom('*' , 'category' , 'WHERE id ="'.$product[0]['catid'].'" ', '');
  if (count($canm) > 0){
    $catname = '<a href="'.$Site_URL.'/category/'.$canm[0]['link'].'">'.$canm[0]['name'].'</a>';
  }else{
    $catname = 'غير معروف';
  }

  $seller = $product[0]['seller'] != "" ? $product[0]['seller'] :  ('شي بوكس');

  $ship = getAllFrom('*' , 'ship' , '', '');
  echo '<section class="products-content">';
  GetBreadcrumb(array($product[0]['title']));
  //-----------------------------
  //---
  echo '<div class="container no-center-mobile">';
  echo '<div class="col-md-6">';//photo section
  
  echo'</span></h2>';
  echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
    if (count($photos) > 0){
      for ($i=0; $i <=count($photos)-1 ; $i++) { 
        echo '<li><a href="'.$Site_URL.'/'.$photos[$i].'"><img src="'.$Site_URL.'/'.$photos[$i].'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
      }
    }else{
      echo '<li><a href="'.$Site_URL.'/'.$def.'"><img src="'.$Site_URL.'/'.$def.'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
      
    }
  echo '</ul></article></div>';
  echo '</div>';
  //---
  
  //---
  
  echo '<div class="col-md-6"><div class="row">
  
    <h1>'.$product[0]['title'].'</h1>

    <div class="simpel-price">
        <span class="current-price"> '.number_format($product[0]["price"]).' ريال</span>
        <span class="old-price">بدلاً من '.number_format($product[0]["oldprice"]).' ريال</span>
          </div>


    <div class="product-info">
             <div class="product-info-wrp">
          <span class="label"><span class="label"><img src="img/bars-code.png"><b>الموديل</b>:</span>
          </b>:</span>
          <span class="value">SH-B'.$product[0]['id'].'#</span>
        </div>
             <div class="product-info-wrp">
          <span class="label"><span class="label"><img src="img/Seller.png"><b>البائع</b>:</span>
          </b>:</span>
          <span class="value">'.$seller.'</span>
             </div>     
                         
    </div>


    <div class="col-md-12">
        <input type="hidden" value="0" id="product_ship">
        <input type="hidden" value="" id="product_ship_city">
    </div>
    ';

    if($product[0]['video'] != ''){
    echo '
      <div class="col-md-12">
        <video width="320" height="240" style="max-width:100%" controls>
          <source src="'.$Site_URL.'/'.$product[0]['video'].'" type="video/mp4">  
        </video>
      </div>
    ';
    }
    echo '
    <!--
<div class="product-price-box">
    <div class="prices">
        <span class="current-price">السعر '.number_format($product[0]["price"]).' ريال</span>
        <span class="old-price">بدلاً من '.number_format($product[0]["oldprice"]).' ريال</span>
    </div>
</div>
-->
<!--
<div class="product-price-box">

    <div class="save-box">
        تخفيض '.number_format($product[0]["oldprice"] - $product[0]["price"]).' ريال
    </div>
    <div class="prices">
        <span class="current-price">عند استخدام كود خصم SHE30</span>
    </div>
</div>
-->




<div class="product-info">
  <div class="shipping-box-content">
    <div class="shipping-box-icon">
      <img src="/img/tag.png" alt="Shipping Icon">
    </div>
    <div class="shipping-box-text">
      <div>قسيمه خصم بقيمة 30 ريال  عند استخدام كود خصم  <strong> SHE30 </strong></div>
    </div>
  </div>

</div>


<div class="product-info">

<div style="font-weight:bold; font-size:13px;  text-align: justify;">
  <span style="color:#2e7d32;">الشحن داخل</span>
  <span style="color:#000; margin-left:5px;">المملكة</span>
</div>
  <div class="shipping-box-content">
    <div class="shipping-box-icon">
      <img src="/img/shipping-protection.png" alt="Shipping Icon">
    </div>
    <div class="shipping-box-text">
      <div>شحن سريع من خلال   <strong>SMSA Express </strong><strong>تكلفة 40 ريال </strong></div>
      <div>تاريخ التوصيل المحتمل في <strong>'.$fromDate.' - '.$toDate.'</strong>.</div>
    </div>
  </div>

</div>

<div class="product-info">
    <div class="info-header" onclick="toggleSizeChart()">
        <span class="payment-icon"><img src="/img/sizsa.png" alt="Size Icon"></span>
        <span class="info-text">جدول المقاسات</span>
<span class="fa fa-angle-left" style="color: rgb(149, 149, 149);"></span>
    </div>
    <div class="info-details size-details" style="display: none;">
        <table class="size-table">
            <thead>
                <tr>
                    <th>المقاس</th>
                    <th>قياس الورك (سم/إنش)</th>
                    <th>محيط الخصر (سم/إنش)</th>
                    <th>محيط الصدر (سم/إنش)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>S</td>
                    <td>92-88 / 35-34</td>
                    <td>67-63 / 25-24</td>
                    <td>85-81 / 33-32</td>
                </tr>
                <tr>
                    <td>M</td>
                    <td>97-93 / 37-36</td>
                    <td>72-68 / 27-26</td>
                    <td>90-86 / 35-34</td>
                </tr>
                <tr>
                    <td>L</td>
                    <td>102-98 / 39-38</td>
                    <td>77-73 / 29-28</td>
                    <td>95-91 / 37-36</td>
                </tr>
                <tr>
                    <td>XL</td>
                    <td>107-103 / 41-40</td>
                    <td>82-78 / 31-30</td>
                    <td>96-100 / 39-38</td>
                </tr>
                <tr>
                    <td>2XL</td>
                    <td>108 / 43-42</td>
                    <td>83-87 / 32-33</td>
                    <td>101-104 / 41-40</td>
                </tr>
            </tbody>
        </table>
    </div>
    </div>


    <div class="product-info">
    <div class="info-header" onclick="toggleExchangePolicy()">
        <span class="payment-icon"><img src="img/replacement.png" alt="Exchange Icon"></span> 
 <span class="info-text">سياسة الاستبدال والاسترجاع</span> 
<span class="fa fa-angle-left" style="color: rgb(149, 149, 149);"></span>
    </div>
<div class="info-details exchange-details" style="display: none; text-align: right;">
    <h4>سياسة الاستبدال والاسترجاع</h4>

    <h5>أولًا: الاسترجاع</h5>
    <p>
        يحق للعميل طلب استرجاع المنتج خلال <strong>يومين فقط</strong> من تاريخ استلام الطلب، بشرط أن يكون المنتج في حالته الأصلية ولم يتم استخدامه.
    </p>
    <ul>
        <li>يتم خصم <strong>تكلفة الشحن الأولى</strong> (من المؤسسة إلى العميل).</li>
        <li>يتحمّل العميل <strong>تكلفة الشحن الثانية</strong> (من العميل إلى المؤسسة) بنفس قيمة الشحن الأولى.</li>
    </ul>

    <h5>ثانيًا: الاستبدال</h5>
    <p>
        يحق للعميل طلب استبدال المنتج خلال <strong>يومين فقط</strong> من تاريخ استلام الطلب، بشرط أن يكون المنتج في حالته الأصلية ولم يتم استخدامه.
    </p>
    <ul>
        <li>يتحمّل العميل <strong>تكلفة الشحن من العميل إلى المؤسسة</strong>.</li>
        <li>يتحمّل العميل أيضًا <strong>تكلفة الشحن من المؤسسة إلى العميل</strong> للمنتج الجديد.</li>
    </ul>

    <p>
        نحن نلتزم بإتمام عملية الاستبدال خلال <strong>أسبوع أو أقل</strong> من تاريخ استلام المنتج المرتجع من قبل المؤسسة.
    </p>
</div>



<!--

<div class="product-info">

  <div class="product-info-wrp">
    <span class="label">
      <b>عدد الإعجابات</b>:
    </span>
    <span class="value">'.GetProductLikes($product[0]).'</span>
  </div>

  <div class="product-info-wrp">
    <span class="label">
      <a href="https://api.whatsapp.com/send?text='.urlencode($product[0]["title"].' - السعر: '.number_format($product[0]["price"]).' ريال. شاهد المنتج هنا: '.$Site_URL.'/'.$product[0]["link"]).'" 
         target="_blank" 
         title="مشاركة على واتساب"
         style="color:#25D366; font-size: 13px; display: inline-flex; align-items: center; gap: 5px;">
        <i class="fa fa-whatsapp" style="margin-left:5px;"></i>
        <b>مشاركة واتساب</b>
      </a>
    </span>
  </div>

</div>

-->



            <div class="col-md-12">

                <input type="hidden" value="0" id="product_ship">
                <input type="hidden" value="" id="product_ship_city">
            </div>
        </div>
    
    <div class="siza col-md-12">
    ';
    $AllFilter = array();
    if($product[0]['filters'] != ''){
        $filter = explode(',' , $product[0]['filters']);
        for($i=0; $i<=count($filter)-1; $i++){
            if(trim($filter[$i]) != ''){
                $chf = getAllFrom('*' , 'filter' , 'WHERE value LIKE "%'.$filter[$i].'%" AND show_in_product = 1 ', '');
                if(count($chf) > 0){
                    $fnd = false;
                    for($z=0; $z<=count($AllFilter)-1; $z++){
                        if($AllFilter[$z]['name'] == $chf[0]['name']){
                            $fnd = true; 
                            array_push($AllFilter[$z]['value'] ,$filter[$i]);
                        }
                    }
                    if(!$fnd){
                        array_push($AllFilter , array(
                            'name' => $chf[0]['name'],
                            'value' =>  array($filter[$i])
                        ));
                    }
                }
            }
        } 
    }
 
if(count($AllFilter) > 0){
    for($i=0; $i<=count($AllFilter)-1; $i++){
        echo '<div class="filterbox">';
        echo '<h4 style="font-size: 14px; font-weight: 700; margin: 13px 0 6px;">'.$AllFilter[$i]['name'].'</h4>';
        $fvalue = $AllFilter[$i]['value'];
        echo '<select id="selectsize" class="form-control"><option value="">أختيار المقاس</option>';
        for($x = 0; $x <= count($fvalue)-1; $x++){
            echo '<option value="'.$fvalue[$x].'">'.$fvalue[$x].'</option>';
        }
        echo '</select>';
        echo '</div>';
    }
}
 

    echo '
    </div>

  
  



    
       
    <div class="col-md-12 text-center pad0">
      ';
        if($product[0]['Quantity'] != '' && $product[0]['Quantity'] > 0){
echo '<a onclick="AddProductToCart('.$product[0]['id'].')" id="cart" style="margin-top: 10px;" class="btn btn-block btn-danger btn-md"><i class="icon-cart"></i><span>&nbsp; إضافة للسلة </span></a><a onclick="AddProductToCart('.$product[0]['id'].')" id="fixdaddcart" class="btn btn-block btn-danger btn-md"><i class="icon-cart"></i><span>&nbsp; إضافة للسلة </span></a>'; 
        }else{
          echo '<a  style="margin-top: 10px;" class="btn btn-block btn-danger btn-md"><i class="icon-cart"></i><span>&nbsp; غير متوفر حاليا </span></a>';
        }

      echo '
    </div> 
 
    <div class="col-md-12 pad0">

    <div class="col-md-6 col-sm-12 col-xs-6 padding5"> 
   
   </div> 
   <div class="col-md-6 col-sm-12 col-xs-6 padding5"> 


  </div> 
    </div> 
    </div> 
    </div>';
  
  echo '</div>';
  echo '</div>';


  echo '<div class="col-md-12">';//desc section
  echo '<h3>معلومات اضافية</h3><hr>';
  echo $product[0]['descr'];
  
  
  $information = array();
  if ($product[0]['infoid'] != ""){
          $inf = explode(',' , $product[0]['infoid']);
          if (count($inf) > 0){
              for ($u=0; $u <= count($inf)-1 ; $u++) { 
                  if ($inf[$u] != ""){
                      $chg = getAllFrom('*' , 'info' , 'WHERE id = "'.$inf[$u].'"', '');
                      if (count($chg) > 0){
                          array_push($information , array($chg[0]['name'] , $chg[0]['descr']) );
                      }
                  }
              }
          }
  }     
      
      if (count($information) > 0){
          for ($z=0; $z <= count($information)-1 ; $z++) { 
              echo '<div class="col-md-12 padding0">
                      <div class="acou">
                          <a class="infobtn" data-toggle="collapse" data-target="#info'.$z.'">'.$information[$z][0].' <i class="fa fa-angle-double-down" aria-hidden="true"></i></a>
                          <div id="info'.$z.'" class="collapse infodata">'.$information[$z][1].'</div>
                      </div>
              </div>';
          }
      }
      

  echo '</div>';



  //-----------------------------
  $tit = explode(' ', $product[0]['title']);
  if (count($tit) > 0){
    $q = ' WHERE ';
    $limit = 5;
    if ($limit > count($tit)) {$limit = count($tit);}
    $q .= ' ( ';
    for ($i=0; $i <= $limit-1 ; $i++) { 
      if ($i == $limit-1){
        $q = $q .' title LIKE "%'.$tit[$i].'%" ';
      }else{
        $q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
      }
    }
    $q.= ' ) ';

          echo '</div>';

    $more = getAllFrom('*' , 'products_title' , $q , 'AND status = 1 AND Quantity >= 1 AND id != "'.$product[0]['id'].'" AND type = 0 ORDER BY rand() LIMIT '.GetTableSet ('ProductMostViewedComp'));
    if (count($more) > 0){
      echo '<div class="container"><div class="col-md-12"><hr><h2 class="paid-title">موديلات متوفرة</h2>';//more product
      for ($i=0; $i <= count($more)-1 ; $i++) { 
        echo GetProduct($more[$i]['id']);
      }
      echo '</div>';

    }
  }
  //-----------------------------
  echo '</section>';
}else{
  header('Location: category.php'); exit();
}
?>
<script>
    function AddProductToCart(productid){
        var ship = $('#product_ship').val();
        var city = $('#product_ship_city').val();
        var filter = '';

        var filterbox = document.querySelectorAll('.filterbox');
        for (let i = 0; i < filterbox.length; i++) {
            //var checkboxes = filterbox[i].querySelectorAll('input[type=radio]:checked');
            var selectsize = $('#selectsize').val();
            var name = $(filterbox[i]).find('h4').text().trim();
            if(selectsize == ""){
                Swal({
                    title: 'خطأ',
                    text: "يجب عليك إختيار "+name,
                    type: 'error',
                    customClass: 'animated tada',
                    showCancelButton: false,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'موافق',
                    cancelButtonText: 'لا',
                });
                return;
            }else{
                filter += name +':'+selectsize + '&';
            }
        }

        $.post(site_url+"/ajax.php", { action : 'AddProductToCart' , productid : productid , ship:'' , city:'' , filter:filter  } ,function(data){ 
            //CountCart();
            window.location = site_url+'/cart'; 
        }); 
        /*
        if(ship == 0){
            Swal({
                title: 'نسيت خطوة',
                text: "أختار المحافظه لمعرفة تكلفة الشحن",
                type: 'error',
                customClass: 'animated tada',
                showCancelButton: false,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'موافق',
                cancelButtonText: 'لا',
            });
            return;
        }
        
       
        Swal({
        title: 'هيتم أضافة الطلب فى السلة - لتأكيد الطلب أذهب الى سلة الشراء',
        text: "",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ضيف الطلب',
        cancelButtonText: 'لا خلاص',
      }).then((result) => {
        if (result.value) {
            $.post(site_url+"/ajax.php", { action : 'AddProductToCart' , productid : productid , ship:ship , city:city , filter:filter  } ,function(data){ 
                CountCart();
                Swal({
                    title: 'تم بنجاح',
                    text: "تم إضافة المنتج الى السلة بنجاح - أذهب الي سلة الشراء لتاكيد الطلب",
                    type: 'success',
                    customClass: 'animated tada',
                    showCancelButton: false,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'موافق',
                    cancelButtonText: 'لا',
                });
            }); 
        }
      })
      */
		
	}

</script>

<style>
    .info-box {
 margin-top: 13px;
    background-color: #fff;
    border-top: 1px solid #e6e6e6;
}

    .info-header {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: #333;
    }

    .info-header:hover {
        color: #4CAF50;
    }

    .info-text {
        flex-grow: 1;
        text-align: right; /* النص على اليمين */
        margin-right: 6px;
    }

    .payment-icon {
        margin-right: 10px;
    }
    .payment-icon img {
    width: 20px;
    height: 20px;
}
    .info-details {
        margin-top: 15px;
        background-color: #e9e9e9;
        padding: 10px;
        border-radius: 5px;
    }

    .arrow {
        margin-left: 10px; /* السهم على اليسار */
        font-size: 15px;
        transform: rotate(0deg);
        transition: transform 0.3s ease;
    }

    .info-header.open .arrow {
        transform: rotate(180deg); /* لتدوير السهم عند الفتح */
    }

    /* تنسيق الجدول */
    .size-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    .size-table th, .size-table td {
        padding: 10px;
        text-align: center;
        border: 1px solid #ddd;
    }

    .size-table th {
        background-color: #4CAF50;
        color: white;
    }

    .size-table tr:nth-child(even) {
        background-color: #f2f2f2;
    }

    .size-table tr:hover {
        background-color: #ddd;
    }
</style>

<script>
    function toggleSizeChart() {
        var details = document.querySelector('.size-details');
        details.style.display = (details.style.display === 'none' || details.style.display === '') ? 'block' : 'none';
    }

    function toggleReturnPolicy() {
        var details = document.querySelector('.return-details');
        details.style.display = (details.style.display === 'none' || details.style.display === '') ? 'block' : 'none';
    }

    function toggleExchangePolicy() {
        var details = document.querySelector('.exchange-details');
        details.style.display = (details.style.display === 'none' || details.style.display === '') ? 'block' : 'none';
    }

    function toggleShippingDuration() {
        var details = document.querySelector('.shipping-details');
        details.style.display = (details.style.display === 'none' || details.style.display === '') ? 'block' : 'none';
    }
</script>





<?php

$users = array("ممتازة جدا" , "رائعه حقا" , "سعر جميل اوى" , "اول حاجه هشتريها ان شاء الله" , "شكرا لكم على العرض الرائع" , "ممتاز" , "جميله جدا" , "سعرها كويس اوى" , "اريد الشراء");

$res = '
      <script type="application/ld+json">
            {
            "@context": "https://schema.org/", 
            "@type": "Product", 
            "name": '.json_encode($product[0]['title']).',
            "image": '.json_encode($Site_URL.'/'.$photos[0]).',
            "description": '.json_encode(strip_tags($product[0]['descr'])).',
            "brand": {
                "@type": "Brand",
               "name" :  "'.$Site_Name.'"
            },
            "sku": '.json_encode('#'.$product[0]['id']).',
            "mpn": '.json_encode('#'.$product[0]['id']).',
            "offers": {
                "@type": "Offer",
                "url": '.json_encode($Site_URL .'/' .$product[0]['link'] ).',
                "priceCurrency": "SAR",
                "price": '.json_encode($product[0]['price']).',
                "priceValidUntil": "'.date("Y", strtotime('+3 year')).'-12-31",
                "availability": "https://schema.org/InStock",
                "itemCondition": "https://schema.org/NewCondition"
            },
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "10",
                "bestRating": "10",
                "worstRating": "1",
                "ratingCount": '.json_encode($product[0]['views']).'
            },
            "review": [
                ';
                $reviews = '';
                for ($i=0; $i <= count($users)-1 ; $i++) { 
                    $reviews = $reviews . '
                    {
                        "@type": "Review",
                        "name": '.json_encode('user_'.($i+1)).',
                        "reviewBody": '.json_encode($users[$i]).',
                        "reviewRating": {
                          "@type": "Rating",
                          "ratingValue": "10",
                          "bestRating": "10",
                          "worstRating": "1"
                        },
                        "datePublished": "'.date("Y-m-d" ,$product[0]['datee'] ).'",
                        "author": {"@type": "Person", "name": '.json_encode($Site_Name).'},
                        "publisher": {"@type": "Organization", "name": '.json_encode($Site_Name).' }
                    },';
                }
                $reviews = substr_replace($reviews ,"", -1);
                $res .=   $reviews .'
              ]
            }
        </script>
      ';        


      

echo $res;







                         


