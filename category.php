<?php
include('header.php'); 
include('navbar.php');

// get cat info
$catid = $CategoryData['id'];
$cname = $CategoryData['name'];
$cname2 = $CategoryData['title'];
$parent = $CategoryData['parent'];
$descrr = $CategoryData['descr'];
$contentz = $CategoryData['content'];
$hidecontent = $CategoryData['hidecontent'];
$imgurl = $Site_URL.'/'.$CategoryData['photo'];
$perpage = 20;
$MasterPage = 1;


if($contentz == ""){
    $contentz = $descrr;
}

GetBreadcrumb(array($cname));
  

// get cat products
$allproducts =array();
$q = "  ";
$sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
if (count($sub) > 0){
    $q = " OR ";
    for ($i=0; $i <= count($sub)-1 ; $i++) { 
        if ($i == count($sub)-1){
            $q = $q .' catid = '.$sub[$i]['id'];
        }else{
            $q = $q .' catid = '.$sub[$i]['id'] . ' OR';  
        }
    }
}
//-------------------------------------------------------------------------------------------------
    /*
    $q = ' AND ( catid = "'.$catid.'" ';
	$zsub = getAllFrom('id,parent' , 'category' , 'WHERE parent = "'.$catid.'" ', 'AND status = 1');
	for($x=0; $x<=count($zsub)-1; $x++){
		$q .= ' OR catid = "'.$zsub[$x]['id'].'" ';
		$xsub = getAllFrom('id,parent' , 'category' , 'WHERE parent = "'.$zsub[$x]['id'].'" ', 'AND status = 1');
		for($z=0; $z<=count($xsub)-1; $z++){
			$q .= ' OR catid = "'.$xsub[$z]['id'].'" ';
		}
	}
	$q .= ' ) ';
    */
    $q = '';
    $alcts = explode(',' , $catid);

    
    $q = '';
    for ($j=0; $j <= count($alcts)-1 ; $j++) { 
        if(trim($alcts[$j]) != ''){
            $q = ' catid LIKE "%,'.trim($alcts[$j]).',%" OR ';
        }
    }
    $q = substr($q, 0, -3);
	$q = ' AND (' . $q . ' ) ';

    
	$f = '';
    $p = '';
    $fp = '';
    $checkedArray = array();
	if($CategoryFilter != ''){
		$filter = $CategoryFilter;
		$xf = explode('&' , $filter);
		for($x=0; $x<=count($xf)-1;$x++){
            $fnam = explode('=', $xf[$x])[0];
            $cf = explode('=', $xf[$x])[1];
            if($fnam != 'page' && $fnam != 'price'){
                if(trim($cf) != ''){
                    $iu = str_replace('-' , ' ' , trim($cf));
                    $htu = explode(',' , $iu);
                    for($z=0; $z<=count($htu)-1; $z++){
                        if(trim($htu[$z]) != ''){
                            $f .= ' filters LIKE "%,'.trim($htu[$z]).',%" OR ';
                            array_push($checkedArray , trim($htu[$z]));
                        }
                    } 
                }
            }else{
                if($fnam == 'page'){
                    if($cf > 0){
                        $MasterPage = $cf;
                    }
                }
                if($fnam == 'price'){
                    $fp = trim($cf);
                    $pcrs = explode(',' , $fp);
                    if(isset($pcrs[0]) && $pcrs[0] > 0 && isset($pcrs[1]) && $pcrs[1] > 1){
                        $p = 'AND price >= '.$pcrs[0].' AND price < '.$pcrs[1];
                    }
                }
            }
		} 
	}


	if($f !=''){
		$f = substr($f, 0, -3);
		$f = ' AND (' . $f . ' ) ';
	} 
 
	$allproducts = getAllFrom('id,title,link,price,catid' , 'products_title' , 'WHERE status = 1 '.$q.' '.$f.' '.$p.'  AND type = 0  ', '  ORDER BY id  DESC');

    $products = getAllFrom('id,title,link,price,catid' , 'products_title' , 'WHERE status = 1 '.$q.' '.$f.' '.$p.'  AND type = 0 ', '  ORDER BY id  DESC LIMIT '.(($MasterPage-1)*$perpage).','.$perpage);

    

    if(count($allproducts) > 0){
        $min_price=99999999;
        $max_price=0;
        for($i=0; $i<=count($allproducts)-1; $i++){
            if($allproducts[$i]['price'] < $min_price){
                $min_price = $allproducts[$i]['price'];
            }
            if($allproducts[$i]['price'] > $max_price){
                $max_price = $allproducts[$i]['price'];
            }
        }
        //$max_price = $max_price+1;
        if($min_price < 1){
            $min_price = 1;
        }
    }else{
        $min_price=1;
        $max_price=100;
    }


    if($fp == ''){
        $pr = $min_price.','.$max_price;
    }else{
       $pr = $fp; 
    }
    $prinval = 100-(explode(',',$pr)[0]/$min_price*100).','.(explode(',',$pr)[1]/$max_price*100);
//-------------------------------------------------------------------------------------------------
//cat page
  echo '<div class="container-fluid products-content">
            <div class="col-md-12 padding0">
                <h1 class="filter-title"><span class="cat-title"><strong>'.$cname2.'</strong></span></h1>';

                if (count($sub) > 0){
                    echo '<div class="subact"><ul>';
                        for ($c=0; $c <= count($sub)-1 ; $c++) { 
                        echo '<li><i class="fa fa-angle-double-left" aria-hidden="true"></i> <a href="'.$Site_URL.'/category/'.$sub[$c]['link'].'">'.$sub[$c]['name'].'</a></li>';
                        }
                    echo '</ul></div>';
                }
                 
            echo '
            </div>

            <div class="col-md-2 pad7">
            ';
            
            echo'
            <div class="filter">
                <h3><i class="fa fa-sliders fa-rotate-90" aria-hidden="true"></i> تصفية  <a id="showhidefilterbtn" class="showhidefilterbtn" onclick="ShowHideFilter()"><i class="fa fa-arrow-circle-down" aria-hidden="true"></i></a> <a class="applybtn" onclick="ApplyFilter()">تطبيق</a> </h3>
                <div class="hide_show_filter">
            ';
            $filtersid = $CategoryData['filters'];
            $filters = explode(',' , $filtersid);
            for($i=0; $i<=count($filters)-1; $i++){
                if(trim($filters[$i]) != ''){
                    $vch = getAllFrom('*' , 'filter' , 'WHERE id = "'.trim($filters[$i]).'" ', '');
                    if(count($vch) > 0){ 
                        echo '<div class="filterbox">';
                        echo '<h4>'.$vch[0]['name'].'</h4>';
                        $fvalue = json_decode($vch[0]['value']);
                        for($x=0; $x<=count($fvalue)-1; $x++){
                            $checked = '';
                            if(in_array($fvalue[$x] , $checkedArray)){
                                $checked = 'checked';
                            }
                            echo '<div class="checkbox_div"><input type="checkbox" name="check_list_filter[]" value="'.$fvalue[$x].'" '.$checked.' > <span>'.$fvalue[$x].'</span></div>';
                        }
                        echo '</div>';
                        echo '<div class="col-md-12"><hr></div>';
                    }
                }
            }
           
            if(count($allproducts) > 0){
                echo '
                <h4>السعر</h4>
                <input type="hidden" value="'.$min_price.'" id="min_price">
                <input type="hidden" value="'.$max_price.'" id="max_price">
                <input type="hidden" value="" id="slid_price">
                <div class="input-range">
                    <input id="slider" type="range" multiple value="'.$prinval.'"   />
                    <div class="range">
                        <div class="lowDollarAmount"></div>
                        <div class="highDollarAmount"></div>
                    </div>
                </div>
                ';
            }else{
                echo '
                <h4>السعر</h4>
                <input type="hidden" value="0" id="min_price">
                <input type="hidden" value="0" id="max_price">
                <input type="hidden" value="" id="slid_price">
                <div class="input-range dis">
                    <input id="slider" type="range" multiple value="0,0"   />
                    <div class="range">
                        <div class="lowDollarAmount"></div>
                        <div class="highDollarAmount"></div>
                    </div>
                </div>
                ';
            }

            echo '
            <div class="col-md-12"><a class="applybtn2" onclick="ApplyFilter()">تطبيق</a><br><br></div>
            </div>
            </div>
            ';
            



            echo '
            </div>
            <div class="col-md-9 mb-2 pad7">
                <div class="masterdiv" id="myproducts">
                ';
                if(count($products) > 0){
                    for($i=0; $i<=count($products)-1; $i++){
                        echo GetProduct($products[$i]['id']); 
                    }
                    if(count($allproducts) > count($products)){
                        $total_pages = ceil(count($allproducts) / $perpage);
                        echo GetPagination($MasterPage , $total_pages); 
                    }
                }else{
                    echo '<br><div class="index_main"><div class="alert alert-warning alert-dismissible" role="alert">لا يوجد اى منتجات فى الوقت الحالي</div></div>';
                }
                echo ' 
                <input type="hidden" value="'.$MasterPage.'" id="MasterPage">
                </div>
            </div>
         
            <div class="col-md-12 catdescr">
                <div class="hidemorecontent">
                    <div class="descat">'.$contentz.'</div>
                </div>
                <a class="show_more_btn" href="javascript:void(0)" onclick="ShowContent()">مشاهدة المزيد</a>
                
            </div>


        </div>
    ';

if ($hidecontent != ""){
    echo '<div class="col-md-12 catdescr" style="display:none;">'.$hidecontent.'</div>';
} 

if($parent > 0){
    $blogs = getAllFrom('*' , 'blog' , 'WHERE catid ="'.$catid.'" ', '  ORDER BY id DESC LIMIT 20');
    if(count($blogs) > 0){
        echo '<div class="col-md-12">';
        for($i=0; $i<= count($blogs)-1; $i++){
        echo Get_The_Posts($blogs[$i]['id']);
        }
        echo '</div>';
    }
}

$actual_link = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
for($i=1; $i<=3; $i++){
$shma = ""; $desc_shma = "";
if ($i==1){$shma = "اسعار ".$cname;}
if ($i==2){$shma = "عروض ".$cname;}
if ($i==3){$shma = "خصومات ".$cname;}
$desc_shma =  'تعرف على اسعار ومميزات وعيوب '.$cname;
echo '
    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "SaleEvent",
        "name": '.json_encode($shma).',
        "description":'.json_encode($desc_shma).',
        "image": '.json_encode($imgurl).',
        "url": '.json_encode($actual_link).',
        "eventStatus": "https://schema.org/EventScheduled",
        "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
        "startDate": "'.date("Y").'-01-01",
        "endDate": "'.date("Y", strtotime('+3 year')).'-12-31",
        "offers": {
            "@type": "Offer",
            "url": '.json_encode($actual_link).',
            "price": "100",
            "priceCurrency": "SAR",
            "validFrom" : "'.date("Y", strtotime('+3 year')).'-12-31",
            "availability": "http://schema.org/InStock"
        },
        "performer": {
            "@type": "Person",
            "name": '.json_encode(''.$cname).'
        },
        "organizer": {
            "@type": "Person",
            "name": '.json_encode(''.$cname).',
            "url": '.json_encode($actual_link).'
        },
        "location": {
            "@type": "Place",
            "name": '.json_encode(''.$cname).',
            "url":  '.json_encode($actual_link).',
            "address": '.json_encode(''.$cname).'
        }
    }
    </script>
';
}

  

include('footer.php');
?>
<script>
    var LIMIT = 12;
    var AllProducts = [];
    var lastid = 0;
    var ShowS = [];
    $(document).on("ready", function(e) {
        //GetCategoryProducts();
    });
    function GetCategoryProducts(){
        $('#myproducts').html('');
        var catid = "<?php echo $catid; ?>";
        var filter = "<?php echo $CategoryFilter; ?>";
        $.post(site_url+"/ajax.php", { action : 'GetCategoryProducts' , catid:catid , filter:filter } ,function(data){
            var products = JSON.parse(data);
            AllProducts  = products; 
            if(AllProducts.length > 0){
                var X = 0;
                for (let i = 0; i <= products.length-1; i++) {
                    X++; 
                    if(X > LIMIT){
                        GetNextProductPage();
                        break;
                    }
                    ShowS.push(products[i].id);
                    $.post(site_url+"/ajax.php", { action : 'GetProduct' , id:products[i].id } ,function(data){
                        $('#myproducts').append(data);
                    }); 
                }
                //------------------------------------------------------
                var year = "<?php echo date("Y");?>";
                var cname = "<?php echo $cname;?>";

                var table = 
                '<div class="col-md-12">'+
                    '<div class="pricetable">'+
                        '<div class="tabletitles">'+
                            '<h2><i class="fa fa-line-chart" aria-hidden="true"></i> قائمة أسعار '+cname+' '+ year +' <i class="fa fa-line-chart" aria-hidden="true"></i></h2>'+
                            '<h4> آخر تحديث '+"<?php echo date("Y/m/d" , strtotime(' -2 day'));?>"+'</h4>'+
                        '</div>'+
                        '<table class="table table-bordered table-hover table-striped"><thead style="display:none"><tr><th>أفضل أسعار '+cname+'</th><th>القسم</th><th>السعر</th></tr></thead><tbody>';
                for (let i = 0; i <= products.length-1; i++) {
                    table +=
                        '<tr><td scope="row">'+
                            '<a href="'+site_url+'/'+products[i].link+'">سعر '+products[i].title+' '+year+'</a>'+
                        '</td>'+

                        '<td><a href="'+site_url+'/'+products[i].link+'">'+cname+'</a></td>'+

                        '<td><a href="'+site_url+'/'+products[i].link+'"><i class="fa fa-tag" aria-hidden="true"></i> '+products[i].price+' ريال </a> </td></tr>';
                }
                table += '</tbody></table></div></div>';
                $('#price_list').html(table);
            }else{
                $('#myproducts').html('<div class="index_main"><div class="alert alert-warning alert-dismissible" role="alert">لا يوجد اى منتجات فى الوقت الحالي</div></div>');
            }
        });
    }

    function GetMoreProducts(){
       $('#willdel').remove();
        var X = 0;
        for (let i = 0; i <= AllProducts.length-1; i++) {
            if(!ShowS.includes(AllProducts[i].id)){
                X++; 
                if(X > LIMIT){
                    GetNextProductPage();
                    break;
                }
                ShowS.push(AllProducts[i].id);
                $.post(site_url+"/ajax.php", { action : 'GetProduct' , id:AllProducts[i].id } ,function(data){
                    $('#myproducts').append(data);
                }); 
                
            }
        }
    }

    function GetNextProductPage(){ 
        if(ShowS.length < AllProducts.length){
        $('#nxproducts').append('<div id="willdel" class="col-md-12"><br><div class="col-md-4 col-md-offset-4" id="btn-more"><a onclick="GetMoreProducts()" class="btn btn-md btn-danger btn-block">إظهار المزيد من المنتجات</a></div></div>');
        }
    }


    function setPage(num){
        $('#MasterPage').val(num);
        ApplyFilter();
    }

    function ShowHideFilter(){
        var btn = $('#showhidefilterbtn');
        if(btn.html() == '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>'){
            btn.html('<i class="fa fa-arrow-circle-o-up" aria-hidden="true"></i>');
            $('.hide_show_filter').show(200);
        }else{
            btn.html('<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>');
            $('.hide_show_filter').hide(200);
        }
    }

    function ApplyFilter(){
        var lasturl = site_url+'/category/'+"<?php echo $CategoryData['link'];?>";
        var def_price = "<?php echo $min_price.','.$max_price ;?>";
        var page = $('#MasterPage').val();
        var price_range = $('#slid_price').val();
        var filter = '';
        var filter_obj = {};
        var index = 0;
        $('.filterbox').each(function () {
            var name = $(this).find('h4').text().trim();
            var checkboxes = this.querySelectorAll('input[type=checkbox]:checked');
            if(checkboxes.length > 0){
                filter_obj[index] = {'name' : name , 'value' : ''};
                for (let i = 0; i <= checkboxes.length-1; i++) {
                    filter_obj[index].value = filter_obj[index].value+','+checkboxes[i].value.replace(' ' , '-').replace(',' , '-').trim();
                }
                filter_obj[index].value = filter_obj[index].value.substring(1,filter_obj[index].value.length);
                index++;
            }
        });

        if(page > 1 || Object.keys(filter_obj).length > 0 || price_range != def_price){
            lasturl += '/filter?'
            if(page > 1){
                lasturl += 'page='+page+'&'
            }
            if(Object.keys(filter_obj).length > 0){
                myobj = Object.keys(filter_obj);
                for (let i = 0; i <= Object.keys(filter_obj).length-1; i++) {
                    lasturl += filter_obj[i].name +'='+filter_obj[i].value+'&';
                }
            }
            if(price_range != def_price && price_range != '1,1'){
                lasturl += 'price='+price_range+'&';
            }
            lasturl = lasturl.substring(0,lasturl.length-1);
        }
        window.location = lasturl; 
    }

    // input range
    function inputRange(mn,mx) { 
        (function () {
            var supportsMultiple = self.HTMLInputElement && "valueLow" in HTMLInputElement.prototype,
                descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, "value");

            self.multirange = function (input) {
                if (supportsMultiple || input.classList.contains("multirange")) {
                    return;
                }

                var value = input.getAttribute("value"),
                    values = value === null ? [] : value.split(","),
                    min = +(input.min || 0),
                    max = +(input.max || 100),
                    ghost = input.cloneNode();

                input.classList.add("multirange", "original");
                ghost.classList.add("multirange", "ghost");

                input.value = values[0] || min + (max - min) / 2;
                ghost.value = values[1] || min + (max - min) / 2;

                input.parentNode.insertBefore(ghost, input.nextSibling);

                Object.defineProperty(input, "originalValue", descriptor.get ? descriptor : {
                    get: function () {
                        return this.value;
                    },
                    set: function (v) {
                        this.value = v;
                    }
                });

                Object.defineProperties(input, {
                    valueLow: {
                        get: function () {
                            return Math.min(this.originalValue, ghost.value);
                        },
                        set: function (v) {
                            this.originalValue = v;
                        },
                        enumerable: true
                    },
                    valueHigh: {
                        get: function () {
                            return Math.max(this.originalValue, ghost.value);
                        },
                        set: function (v) {
                            ghost.value = v;
                        },
                        enumerable: true
                    }
                });

                if (descriptor.get) {
                    Object.defineProperty(input, "value", {
                        get: function () {
                            return this.valueLow + "," + this.valueHigh;
                        },
                        set: function (v) {
                            var values = v.split(",");
                            this.valueLow = values[0];
                            this.valueHigh = values[1];
                            update();
                        },
                        enumerable: true
                    });
                }

                if (typeof input.oninput === "function") {
                    ghost.oninput = input.oninput.bind(input);
                }

                function update() {
                    ghost.style.setProperty("--low", 100 * ((input.valueLow - min) / (max - min)) + 1 + "%");
                    ghost.style.setProperty("--high", 100 * ((input.valueHigh - min) / (max - min)) - 1 + "%");

                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, false);
                    input.dispatchEvent(event);
                }

                input.addEventListener("input", update);
                ghost.addEventListener("input", update);

                update();
            };
            multirange.init = function () {
                [].slice.call(document.querySelectorAll("input[type=range][multiple]:not(.multirange)")).forEach(multirange);
            };

            if (document.readyState == "loading") {
                document.addEventListener("DOMContentLoaded", multirange.init);
            } else {
                multirange.init();
            }

        })();

        var slider = document.querySelector('#slider'),
            lowDollarAmount = document.querySelector('.lowDollarAmount'),
            highDollarAmount = document.querySelector('.highDollarAmount'),
            min = mn,
            max = mx;

        slider.addEventListener('change', function () {
            var lowps = formatPrice(min + ((max - min) * (slider.valueLow / 100)));
            var higps = formatPrice(min + ((max - min) * (slider.valueHigh / 100)));
            lowDollarAmount.textContent = lowps + ' ريال';
            highDollarAmount.textContent = higps+ ' ريال';
            $('#slid_price').val(lowps.trim().replace(',','')+','+higps.trim().replace(',',''));
        });

        function formatPrice(price) {
            if (price != 0) {
                var formattedPrice =  parseFloat(price, 10).toFixed(0).toString().replace(/(\d+)(?=(\d{3})+\.?)/g, '$1,');
                return formattedPrice;
            }
        }
    }
    inputRange(<?php echo $min_price ;?> , <?php echo $max_price ;?>);
    
    function ShowContent(){
        $('.show_more_btn').hide(200);
        $('.hidemorecontent').css('height' , 'auto')
    }
</script>