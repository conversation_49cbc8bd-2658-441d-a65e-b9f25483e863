<?php
ob_start();
$Title_page = 'المنتجات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

function br2nl($input) {
    return preg_replace('/<br\\s*?\/??>/i', '', $input);
}
function nlt2br($input) {
    return str_replace('<br /><br />', '<br>', $input);
}
//---------------------------------------------------
?>
<link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/4.0.1/min/dropzone.min.css" rel="stylesheet">
<style> 
	.dropzone {
	    min-height: 100px;
	    border: 1px dashed #b3b3b3;
	    background: #f3f3f33b;
	    padding: 0;
	    margin-bottom: 10px;
	}
	.dropzone h5{margin-top: 42px; color: #b3b3b3; font-weight: normal;display: inline-block;}
	#for_photos{
		width: 100%;
		display: inline-block;
		margin-bottom: 20px;
		padding: 10px;
		overflow: auto;
	}
	.postimg{
		width: 100%;
		height: 200px;
		padding: 2px;
		border: 1px solid #eee;
		border-radius: 5px;
		margin-bottom: 10px;
	}
	.md_img{
		width: 220px;
		height: 200px;
		display: inline-block;
		margin: 5px;
		border-radius: 5px;
		padding: 2px;
		border: 2px solid #484b54;
		position: relative;
	}
	.md_img a{
		position: absolute;
		top: 50px;
		left: 50px;
		color: #fff !important;
		background: #0000005c;
		width: 100px;
		font-weight: bold;
		height: 100px;
		line-height: 90px;
		font-size: 60px;
		text-align: center;
		border-radius: 50px;
		border: 1px solid #00000075;
		cursor: pointer;
		opacity: 0.3;
	}
	.md_img a:hover , .md_img a:focus , .md_img a:active{
		color: #E91E63 !important;
		background: #0000005c;
		border: 2px solid #e91e638c;
		opacity: 0.8;
	}
	.md_img img{
		width: 100%;
		height: 100%;
		max-width: 100%;
		max-height: 100%;
		border-radius: 2px;
	}
	.bwh{
		color: #fff !important;
		font-weight: bold;
		font-size: 16px;
	}
	.bwh .fa{
		margin-left: 5px;
	}
	textarea{
		min-height: 150px;
	}
	.mw200{
		max-width: 200px;
		width: 200px;
		white-space: pre-line;
	}
</style>
<div id="getallphoto" class="modal fade" role="dialog">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body text-center">
        	<form action="photo_uplpad.php" enctype="multipart/form-data" class="dropzone" id="image-upload">
			</form>
      		<div id="for_photos"></div>
      </div>
    </div>
  </div>
</div>

<?php
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('products_title' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('products_title' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'paid' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['paid'] == 0){
			UpdateTable('products_title' , 'paid' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('products_title' , 'paid' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'products_title', 'WHERE id = '.$_GET['id'] );
			
			header('Location: products.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'delimg' && isset($_GET['id']) && isset($_GET['product']) ){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['product'].'" ', '');
	if (count($ch) > 0 ){
			DeleteColum( 'photos', 'WHERE id = '.$_GET['id'] );
			$stmt = $db->prepare("UPDATE products_title SET photo = :var1 WHERE  id = :var0 ");  
            $stmt->execute(array('var1' => str_replace($_GET['id'].'&&&', '', $ch[0]['photo']) , 'var0' => $_GET['product'] )); 
	}
	redirect_home ('back' , 0); exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['title'];?> " </b> ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

		     <center>
		     	<a class="btn btn-danger btn-md" href="products.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-md" href="products.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
	}else{
		header('Location: products.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة منتج جديد</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">	
					<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم المنتج</label>
	                      <input type="text" id="p1" value="" class="form-control">
	                    </div>
               	 	</div>

					<div class="col-md-6">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم البائع</label>
	                      <input type="text" id="px1" value="" class="form-control">
	                    </div>
               	 	</div>

               	 	 
					<div class="col-md-3">	
						<div class="form-group">
	                      <label class="form-control-label">السعر</label>
	                      <input type="text" id="p9" value="0" class="form-control">
                        </div>	
                    </div>	
					<div class="col-md-3">	
                        <div class="form-group">
	                      <label class="form-control-label"> السعر قبل الخصم</label>
	                      <input type="text" id="p14" value="0" class="form-control">
                        </div>	
                    </div>	
					<div class="col-md-3">
                        <div class="form-group">
	                      <label class="form-control-label">المحزون</label>
	                      <input type="text" id="p13" value="1" class="form-control">
	                    </div>		
					</div>
					<div class="col-md-3">
                        <div class="form-group">
	                      <label class="form-control-label">قيمة الشحن * مصاريف الشحن</label>
	                      <input type="text" id="p15" value="1" class="form-control">
	                    </div>		
					</div>
					<div class="col-md-12"><hr>
						<div class="form-group">
	                      <label class="form-control-label">إختر الاقسام</label>
						  <?php 
						  	$cats = getAllFrom('*' , 'category' , 'WHERE status = 1 ', 'ORDER BY orders DESC, id DESC');
						   
							if (count($cats) > 0){
								for ($x=0; $x <= count($cats)-1 ; $x++) {
									$chk = '';
									echo '<div class="col-md-3"><input type="checkbox" name="check_list_cat[]" value="'.$cats[$x]['id'].'" '.$chk.'> '.$cats[$x]['name'].'</div>';
								}
							}
							?>
	                    </div>		
					</div>
               	 	<div class="col-md-12"><br><hr>
	                    <div class="form-group">
	                      <label class="form-control-label">وصف المنتج ( أكتب وصف شامل عن المنتج )</label>
	                      <textarea id="p4" class="form-control"></textarea>
	                    </div>
               	 	</div>

               	 	<div class="col-md-12"><hr>
	                    <div class="form-group">
							<a class="bwh btn btn-primary" onclick="AddPhoto()"><i class="fa fa-picture-o"></i> أضافة صور للمنتج</a>
							<div id="photo_res" style="margin-top: 10px;"></div>
	                    </div>
	                </div>    	
                    <div class="col-md-12"><hr>
	                    <div class="form-group"> 
	                      <a id="btn_add" class="bwh btn btn-success" onclick="AddProduct()"><i class="fa fa-cart-arrow-down"></i> إضافة المنتج</a>
							<div id="pro_res" style="margin-top: 10px;"></div>
	                    </div>
	                </div>  
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['title'];?>"</h4>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
						$var2  = ',' ;
						$var3  = nlt2br($_POST['var3']) ;//nlt2br(filter_var($_POST['var3']   , FILTER_SANITIZE_STRING)) ;
                        $var4  = filter_var($_POST['var4']   , FILTER_SANITIZE_STRING) ;
                        $var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;
						$var0  = filter_var($_POST['var0']   , FILTER_SANITIZE_STRING) ;
                        $var9  = filter_var($_POST['var9']   , FILTER_SANITIZE_STRING) ;
                        $var14  = filter_var($_POST['var14']   , FILTER_SANITIZE_STRING) ;
                        $var13  = filter_var($_POST['var13']   , FILTER_SANITIZE_STRING) ;
                        $var15  = filter_var($_POST['var15']   , FILTER_SANITIZE_STRING) ;
						$varx11 = $_POST['varx11'];

						$check_list_cats = ',';
						if (isset($_POST['check_list_cat'])){
							foreach($_POST['check_list_cat'] as $check) {
								$check_list_cats = $check_list_cats.$check.',' ;
							}
						}

						$var2 = $check_list_cats;

						$chx = getAllFrom('*' , 'products_title' , 'WHERE title = "'.$var1.'" AND id != "'.$_GET['id'].'" ', '');

						if (empty($var1) || $var1 == "0"){
							echo  Show_Alert('danger' , 'يجب عليك كتابة إسم المنتج.');
						}elseif (empty($var2) || $var2 == "0"){
							echo  Show_Alert('danger' , 'يجب عليك تحديد القسم.');
						}elseif (empty($var3) || $var3 == "0"){
							echo  Show_Alert('danger' , 'يجب عليك كتابة وصف المنتج.');
						}elseif (empty($var4) || $var4 == "0"){
							echo  Show_Alert('danger' , 'يجب عليك كتابة رابط المنتج.');
						}elseif(count($chx) > 0){
							echo  Show_Alert('danger' , 'هذا المنتج موجود من قبل فى قائمة منتجاتك.');
						}else{
							$ch3 = getAllFrom('*' , 'info' , 'ORDER BY id DESC', '');	
							$infoid = ',';
                            if (isset($_POST['check_list'])){
    							foreach($_POST['check_list'] as $check) {
    						        $infoid = $infoid.$check.',' ;
    						    }
    						}

							$check_list_filter = ',';
                            if (isset($_POST['check_list_filter'])){
    							foreach($_POST['check_list_filter'] as $check) {
    						        $check_list_filter = $check_list_filter.$check.',' ;
    						    }
    						}


							
							
							$stmt = $db->prepare("UPDATE products_title
							    SET title = :var1  ,
							    	catid = :var2  ,
							    	link = :var4  ,
							    	descr = :var3  ,
                                    delivery = :var5  ,
							    	price = :var9  ,
                                    oldprice = :var14  ,
                                    Quantity = :var13  ,
							    	infoid = :var10 ,
							    	ship = :var15 ,
							    	filters = :var16 ,
							    	seller = :var11 
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var3' => $var3 ,
                                'var4' => $var4 ,
                                'var5' => $var5 ,
                                'var9' => $var9 ,
                                'var14' => $var14 ,
                                'var13' => $var13 ,
					            'var10' =>$infoid ,
					            'var11' =>$varx11 ,
					            'var15' =>$var15 ,
					            'var16' =>$check_list_filter ,
					            'var0' => $ch[0]['id']
					          )); 	

					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

				        	redirect_home ('back' , 1); exit();
							
				        	
				    	}
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">إسم المنتج</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['title'];?>" class="form-control">
		                    </div>
	               	 	</div>

						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">إسم البائع</label>
		                      <input type="text" name="varx11" value="<?php echo $ch[0]['seller'];?>" class="form-control">
		                    </div>
	               	 	</div>
	               	 	 

	               	 	<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">رابط المنتج ( الرجاء الإنتباه ) </label>
		                      <input type="text" name="var4" value="<?php echo $ch[0]['link'];?>" class="form-control">
		                    </div>
	               	 	</div>

						<div class="col-md-4">
		                    <div class="form-group">
		                      <label class="form-control-label">السعر</label>
		                      <input type="text" name="var9" value="<?php echo $ch[0]['price'];?>" class="form-control">
                            </div>
						</div>
						<div class="col-md-4">
                            <div class="form-group">
		                      <label class="form-control-label">السعر قبل الخصم</label>
		                      <input type="text" name="var14" value="<?php echo $ch[0]['oldprice'];?>" class="form-control">
                            </div>
						</div>
						<div class="col-md-4">
                            <div class="form-group">
                            	<label class="form-control-label">التوصيل</label>
		                        <input type="text" name="var5" value="<?php echo $ch[0]['delivery'];?>" class="form-control">
		                    </div>
		                </div>
						<div class="col-md-4">
                            <div class="form-group">
		                      <label class="form-control-label">المخزون</label>
		                      <input type="text" name="var13" value="<?php echo $ch[0]['Quantity'];?>" class="form-control">
		                    </div>
	               	 	</div>
						<div class="col-md-4">
                            <div class="form-group">
		                      <label class="form-control-label">قيمة الشحن * مصاريف الشحن</label>
		                      <input type="text" name="var15" value="<?php echo $ch[0]['ship'];?>" class="form-control">
		                    </div>
	               	 	</div>

						<div class="col-md-12"><hr>
							<div class="form-group">
							<label class="form-control-label">إختر الاقسام</label>
							<?php 
								$cats = getAllFrom('*' , 'category' , 'WHERE status = 1 ', 'ORDER BY orders DESC, id DESC');
							
								if (count($cats) > 0){
									for ($x=0; $x <= count($cats)-1 ; $x++) {
										if (strpos($ch[0]['catid'],','.$cats[$x]['id'].',')!== false){
											$chk = 'checked';
										}else{
											$chk = '';
										}
										echo '<div class="col-md-3"><input type="checkbox" name="check_list_cat[]" value="'.$cats[$x]['id'].'" '.$chk.'> '.$cats[$x]['name'].'</div>';
									}
								}
								?>
							</div>		
						</div>
						<div class="col-md-12"><br><hr>
		                    <div class="form-group">
		                      <label class="form-control-label">وصف المنتج</label>
		                      <textarea name="var3" class="form-control"><?php echo br2nl($ch[0]['descr']);?></textarea>
		                    </div>
	               	 	</div>

						<?php
						echo '<div class="col-md-12">';
						$cis = explode(',' ,$ch[0]['catid']);
						$q = ' id = "'.$ch[0]['catid'].'" ';
						for ($z=0; $z <= count($cis)-1 ; $z++) { 
							if(trim($cis[$z] != '')){
								$q .= ' OR id = "'. $cis[$z].'"';
							}
						}
						$cat_filter = getAllFrom('*' , 'category' , 'WHERE '.$q.' ', '');
						if(count($cat_filter) > 0){
							$catfilter = $cat_filter[0]['filters'];
							$cf = explode(',' , $catfilter);
							for($i=0; $i<=count($cf)-1; $i++){
								if(trim($cf[$i]) != ''){
									$filter = getAllFrom('*' , 'filter' , 'WHERE id = "'.trim($cf[$i]).'"', '');
									if(count($filter) > 0){
										echo '<div class="col-md-4"><hr>';
										echo '<p><b>'.$filter[0]['name'].'</b></p>';
										$fv = json_decode($filter[0]['value']);
										for($z=0; $z<= count($fv)-1; $z++){
											if (strpos($ch[0]['filters'],','.$fv[$z].',')!== false){
                                                $chk = 'checked';
                                            }else{
                                                $chk = '';
                                            }
											echo '<div class="col-md-6"><input type="checkbox" name="check_list_filter[]" value="'.$fv[$z].'" '.$chk.'> '.$fv[$z].'</div>';
										}
										echo '</div>';
									}
								}
							}
						}
						echo '</div>';
						?>
	               	 	
	               	 	<div class="col-md-12 note-codable">
							<hr>
	               	 	    <label class="form-control-label">المعلومات</label>
		                    <br>
		                    <div class="form-group">
		                        <?php 
		                        $ch1 = getAllFrom('*' , 'info' , 'ORDER BY id DESC', '');	
                                    if (count($ch1) > 0){
                                        for ($x=0; $x <= count($ch1)-1 ; $x++) {
                                            if (strpos($ch[0]['infoid'],','.$ch1[$x]['id'].',')!== false){
                                                $chk = 'checked';
                                            }else{
                                                $chk = '';
                                            }
                                            echo '<div class="col-md-3"><input type="checkbox" name="check_list[]" value="'.$ch1[$x]['id'].'" '.$chk.'> '.$ch1[$x]['name'].'</div>';
                                        }
                                    }
		                        ?>
		                    </div><br><br>
	               	 	</div>
	               	 	

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12"><br>
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-success">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>	
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body"><hr>
					<h4>صور المنتج</h4><br>
					<div class="col-md-3">
	                 	<label class="form-control-label" style="text-align: center; width: 100%;">رفع صوره جديدة</label>
					  <center>
					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
					         
					          <input type="file" name="photo" id="photo" required style="display: none;" />
					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
					          <input type="hidden" name="Image_For" value="products">
					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">
					        </form>
					        <label for="photo" class="btn btn-primary btn-md" ><i class="fa fa-camera"></i> إختر الصوره</label>
					        <label for="Uploads" class="btn btn-primary btn-md" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
					  </center>
	                 </div>
	                 <div class="col-md-12"><br><br>
					<?php
					$pid = explode('&&&', $ch[0]['photo']);
					if (count($pid) > 0){
						for ($i=0; $i <= count($pid)-1 ; $i++) { 
							if ($pid[$i] != ""){
								$ph = getAllFrom('*' , 'photos' , 'WHERE id = "'.$pid[$i].'" ', '');
								if (count($ph) > 0){
									echo '<div class="md_img"><img src="'.$Site_URL.'/'.$ph[0]['link'].'"><a href="products.php?do=delimg&id='.$ph[0]['id'].'&product='.$ch[0]['id'].'"><i class="fa fa-trash-o" aria-hidden="true"></i></a></div>';
								}else{
									$stmt = $db->prepare("UPDATE products_title SET photo = :var1 WHERE id = :var0 ");  
            						$stmt->execute(array('var1' => str_replace($pid[$i].'&&&', '', $ch[0]['photo']) , 'var0' => $ch[0]['id'] )); 
								}
							}
						}
						if (count($pid) == 1 && trim($pid[0]) == ""){
							echo  Show_Alert('warning' , 'لا يوجد صور. ');
						}
					}else{
						echo  Show_Alert('warning' , 'لا يوجد صور. ');
					}
					?>
					</div>
				</div>
			</div>
		</div>
	</div>			
	<?php	
	}else{
		header('Location: products.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="products.php?do=add_new" class="btn btn-lg btn-success">إضافة منتج جديد</a>
			</div>
		</div>
		<hr>
	</div>			
</div>

<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all">رقم المنتج</th>
								<th class="all mw200">إسم المنتج</th>
								<th class="none">القسم الأساسي </th>
								<th class="mobile-p desktop">السعر</th>
								<th class="mobile-p desktop">القسم الفرعي </th>
								<th class="mobile-p desktop"> المشاهدات </th>
								<th class="none">تاريخ الإضافة </th>
								<th class="all">الإجراء </th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script src="//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/4.2.0/min/dropzone.min.js"></script>
<script type="text/javascript">
	var All_photo_in_product = "";
	function AddPhoto(){
		$('#for_photos').html('');
		$('#image-upload').show();
		$('#image-upload').html('<h5>إسحب او إختر الصوره</h5>');
		$('#getallphoto').modal('show');
	}

	function Get_Photo_From_DB(id){
		$.post("ajax.php", { do : 'Get_Photo_From_DB' , id : id} , function(data){ 
			if (data != ''){
		 		$('#for_photos').append(data);
		 		$('#photo_res').append(data);
			}
	    });
	}
	Dropzone.options.imageUpload = {
	    maxFilesize:1,
	    acceptedFiles: ".jpeg,.jpg,.png,.gif,.webp",
	    init: function () {
	    this.on("complete", function (file,responseText) {
	      if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0) {
	        $('#image-upload').html('<h5>إسحب او إختر الصوره</h5>');
	      }
	    });
        this.on("success", function(file, responseText) {
			Get_Photo_From_DB(responseText);
			All_photo_in_product = responseText + "&&&" + All_photo_in_product ;
        });
	  }
	};
	function GetSubCategory(){
		var p5 = $('#p2').val();
		
		if (p5 == '0'){
			$('#p3').addClass('dis');
			$('#p3').html('<option value="0">إختر القسم</option>');
		}else{
			$.post("ajax.php", { action : 'GetSubCategory' , id: p5 } , function(data){ 
				if (data != ""){
					$('#p3').html('');
					$('#p3').append(data);
					$('#p3').removeClass('dis');
				}   
	    	});
		}
	}
	function nl2br (str, is_xhtml) {
	    if (typeof str === 'undefined' || str === null) {
	        return '';
	    }
	    var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';
	    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1' + breakTag + '$2');
	}
	function AddProduct(){
		$('#pro_res').html('');
		var title = $('#p1').val();
		var catid = ',' ;
		var price = $('#p9').val() ;
        var oldprice = $('#p14').val() ;
        var delivery = $('#p5').val() ;
        var Quantity = $('#p13').val() ;
        var ship = $('#p15').val() ;
		var seller = $('#px1').val();

		$('input[name="check_list_cat[]"]:checked').each(function () {
			catid = catid+$(this).val()+',';
		});
   
		var descr = nl2br($('#p4').val());
		var photo = All_photo_in_product;
		if (title == "" || title == '0'){
			$('#pro_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة إسم المنتج.</div></div>');
		}else if (catid == "" || catid == '0'){
			$('#pro_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك تحديد القسم.</div></div>');
		}else if (descr == "" || descr == '0'){
			$('#pro_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة وصف المنتج.</div></div>');
		}else{
			$('#pro_res').html('');
			$('#btn_add').hide(200);
			$.post("ajax.php", { action : 'AddProduct' , title: title , catid : catid , descr: descr , photo : photo , price:price ,oldprice:oldprice , delivery:delivery , Quantity : Quantity , seller:seller , ship:ship } , function(data){ 
				 ///$('#pro_res').html(data);	
				 $('#btn_add').show(200);
				 window.location.href = "products.php?do=edit&id="+data;
	    	});
		}
	}
</script>

<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'products_title'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [20, 50, 100, 200, 500, 1000, -1],
                  [20, 50, 100, 200, 500, 1000, "كل  المنتجات"]
                ],
                'destroy': true,
                responsive:true,
                "order": [[ 0, "desc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن منتج",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });
</script>