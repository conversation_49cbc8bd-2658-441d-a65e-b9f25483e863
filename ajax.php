<?php
ob_start();
include('webset.php');
include('session.php');
//--------------------------------------------------------------
if (isset($_POST['action']) && $_POST['action'] == 'AddToCart' && isset($_POST['productid'])){
	$productid = filter_var($_POST['productid']  , FILTER_SANITIZE_STRING) ;
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_product = $_SESSION['UserCart'] ;
	if (!in_array( $productid, $all_product)){
		array_push($_SESSION['UserCart'] , $productid );
	}
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddProductToCart' && isset($_POST['productid'])){
	$productid = filter_var($_POST['productid']  , FILTER_SANITIZE_STRING) ;
	$ship = filter_var($_POST['ship']  , FILTER_SANITIZE_STRING) ;
	$city = filter_var($_POST['city']  , FILTER_SANITIZE_STRING) ;
	$filter = filter_var($_POST['filter']  , FILTER_SANITIZE_STRING) ;
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_cart = $_SESSION['UserCart'] ;
	$newitem = array(
		'productid' => $productid,
		'title' => $productid,
		'ship' => $ship,
		'city' => $city,
		'filter' => rtrim($filter,'&'),
		'count' => 1,
	);
	
	if (!in_array( $newitem, $all_product)){
		array_push($_SESSION['UserCart'] , $newitem );
	}
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'SaveCartOrder' && isset($_POST['cart'])){
	$_SESSION['UserCart'] = $_POST['cart'];
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'GetMoreProduct' && isset($_POST['lastproduct'])  && isset($_POST['catid']) ){
    $lastproduct = filter_var($_POST['lastproduct']  , FILTER_SANITIZE_STRING) ;
    $catid = filter_var($_POST['catid']  , FILTER_SANITIZE_STRING) ;
    
    $sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
	if (count($sub) > 0){
		$q = " OR ";
		for ($i=0; $i <= count($sub)-1 ; $i++) { 
			if ($i == count($sub)-1){
				$q = $q .' catid = '.$sub[$i]['id'];
			}else{
				$q = $q .' catid = '.$sub[$i]['id'] . ' OR';	
			}
		}
		$allproducts = getAllFrom('*' , 'products_title' , 'WHERE (catid ="'.$catid.'" '.$q.' ) ', 'AND id < '.$lastproduct.'  ORDER BY price ASC,id DESC');
		$products = getAllFrom('*' , 'products_title' , 'WHERE ( catid ="'.$catid.'" '.$q.' ) ', 'AND id < '.$lastproduct.'  ORDER BY price ASC,id DESC LIMIT 10');
		
		
		
		if (count($products) > 0){
		    $lastpid = 0;
		    for($i=0; $i<= count($products)-1; $i++){
		        GetProduct($products[$i]['id']);
		        $lastpid = $products[$i]['id'];
		    }
		    if (count($allproducts) > count($products) ){
		        echo '<div id="willdel" class="col-md-12"><br><div class="col-md-4 col-md-offset-4" id="btn-more"><a onclick="GetMoreProduct('.$lastpid.' , '.$catid.')" class="btn btn-md btn-danger btn-block">إظهار المزيد من المنتجات</a></div></div>';
		    }
		}
		
		
	}
    

//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'CountCart' ){
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_product = $_SESSION['UserCart'] ;
	echo count($all_product);
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'RemoveFromCart' && isset($_POST['productid']) ){
	//$productid = filter_var($_POST['productid']  , FILTER_SANITIZE_STRING) ;
	//$_SESSION['UserCart'] = array_diff($_SESSION['UserCart'], $productid);
	$newcart = array();
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_cart = $_SESSION['UserCart'];
	for($i=0; $i<=count($all_cart)-1; $i++){
		if($all_cart[$i]['productid'] != $_POST['productid']){
			array_push($newcart , $all_cart[$i]);
		}
	}
	$_SESSION['UserCart'] = $newcart;
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'RemoveAllFromCart' ){
	$_SESSION['UserCart'] = array();
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'ShowCart' ){
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_product = $_SESSION['UserCart'] ;
	if (count($all_product) > 0){
		$c= 0;
		$total = 0 ;
		$total_ship = 0;
		echo '<table class="table">
				<thead>
				<tr>
					<th scope="col">المنتج</th>
					<th scope="col">السعر</th>
					<th scope="col">إزالة</th>
				</tr>
				</thead>
				<tbody>';
				
				for ($i=0; $i <= count($all_product)-1 ; $i++) { 
					$product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$all_product[$i]['productid'].'" ', '');
					if (count($product) > 0){
					echo '
					<tr>
						<th scope="row">'.$product[0]['title'].'</th>
						<td>'.$product[0]['price'].'</td>
						 
						<td><a class="removefromcart" onclick="RemoveFromCart('.$all_product[$i]['productid'].')"><i class="fa fa-trash"></i></a></td>
						 
						<input type="hidden" id="price'.$c.'" value="'.$product[0]['price'].'">
					</tr>
					';
					$c = $c + 1 ;
					$total = $total + $product[0]['price'];
					$total_ship += $all_product[$i]['ship'];
					} 
				}

		echo '</tbody>
		</table>';
		echo '<input type="hidden" id="countc" value="'.$c.'">';
		 
		echo '<hr>';


		echo ' <a id="btn1" class="btn btn-md btn-success" href="'.$Site_URL.'/cart">تاكيد الشراء</a> ';
		//echo ' <a id="btn2" class="btn btn-md btn-success" onclick="ConfirmBuy()">تأكيد الشراء</a> ';
		echo ' <a class="btn btn-md btn-warning" onclick="RemoveAllFromCart()">إفراغ السلة</a> ';
		echo ' <a data-dismiss="modal" class="btn btn-md btn-danger">إغلاق</a> ';
	}else{
		echo  Show_Alert('warning' , 'سلة المشتريات فارغة');
		echo '<a data-dismiss="modal" class="btn btn-md btn-danger">إغلاق</a>';
	}
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'ShowCartxxx' ){
	if(!isset($_SESSION['UserCart'])){
		$_SESSION['UserCart'] = array();
	}
	$all_product = $_SESSION['UserCart'] ;
	if (count($all_product) > 0){
		$c= 0;
		$total = 0 ;
		echo '<table class="table">
				<thead>
				<tr>
					<th scope="col">المنتج</th>
					<th scope="col">السعر</th>
					<th scope="col">الكمية</th>
					<th scope="col">إزالة</th>
				</tr>
				</thead>
				<tbody>';
				
				for ($i=0; $i <= count($all_product)-1 ; $i++) { 
					$product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$all_product[$i].'" ', '');
					if (count($product) > 0){
					echo '
					<tr>
						<th scope="row">'.$product[0]['title'].'</th>
						<td>'.$product[0]['price'].'</td>
						<td><input onchange="calcPrice()" style="width: 60px;text-align: center;" type="number" id="productcount'.$c.'" max="100" min="1" value="1" class="form-control"></td>
						<td><a class="removefromcart" onclick="RemoveFromCart('.$all_product[$i].')"><i class="fa fa-trash"></i></a></td>
						<input type="hidden" id="product'.$c.'" value="'.$all_product[$i].'">
						<input type="hidden" id="price'.$c.'" value="'.$product[0]['price'].'">
					</tr>
					';
					$c = $c + 1 ;
					$total = $total + $product[0]['price'];
					}else{
						$_SESSION['UserCart'] = array_diff($_SESSION['UserCart'], $all_product[$i]);
					}
				}

		echo '</tbody>
		</table>';
		echo '<input type="hidden" id="countc" value="'.$c.'">';
		echo '<p> السعر الإجمالى : <b id="totalprice">'.$total.'</b> جنية</p>';		
		echo '<hr>';
		echo '<div id="userdatacart">
		<div class="col-md-6 padding5">
		<div class="form-group">
			<label class="form-control-label">الأسم بالكامل : <span class="red">(*)</span></label>
			<input type="text" id="cv1" value="'.$FullName.'" class="form-control">
		</div>
		</div>
		<div class="col-md-6 padding5">
		<div class="form-group">
			<label class="form-control-label">العنوان : <span class="red">(*)</span></label>
			<input type="text" id="cv2" value="'.$UserAdress.'" class="form-control">
		</div>
		</div>
		<div class="col-md-6 padding5">
		<div class="form-group">
			<label class="form-control-label">رقم الموبايل 1 : <span class="red">(*)</span></label>
			<input type="text" id="cv3" value="'.$UserPhone.'" class="form-control">
		</div>
		</div>
		<div class="col-md-6 padding5">
		<div class="form-group">
			<label class="form-control-label">رقم الموبايل 2 : <span class="red">(*)</span></label>
			<input type="text" id="cv4" value="" class="form-control">
		</div>
		</div>
		<div class="col-md-12 padding5" id="fRes"></div>
		</div>';
		echo ' <a id="btn1" class="btn btn-md btn-success" onclick="ShowForm()">إنهاء الشراء</a> ';
		echo ' <a id="btn2" class="btn btn-md btn-success" onclick="ConfirmBuy()">تأكيد الشراء</a> ';
		echo ' <a class="btn btn-md btn-warning" onclick="RemoveAllFromCart()">إفراغ السلة</a> ';
		echo ' <a data-dismiss="modal" class="btn btn-md btn-danger">إغلاق</a> ';
	}else{
		echo  Show_Alert('warning' , 'سلة المشتريات فارغة');
		echo '<a data-dismiss="modal" class="btn btn-md btn-danger">إغلاق</a>';
	}
//-----------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'ConfirmBuyءء' && isset($_POST['productid'])  ){
	$productid = filter_var($_POST['productid']  , FILTER_SANITIZE_STRING) ;
	$product = getAllFrom('*' , 'products' , 'WHERE id ="'.$productid.'" AND status = 1 ', '');
	echo '<div class="row">';
	if (count($product) > 0){

		$pd = getAllFrom('*' , 'products_title' , 'WHERE id ="'.$product[0]['titleid'].'" ', '');
		if (count($pd) > 0){

		echo '<p class="ctysas" style="border-top: 0px;padding-top: 0px;border-bottom: 1px solid #ddd;padding-bottom: 15px; color: #333; font-family: \'GE SS\',\'Open Sans\', Tahoma, sans-serif; text-align: center;font-size: 18px;"><i class="fa fa-cart-arrow-down" ></i> '.$pd[0]['title'].'</p>';

		echo '<input id="cv0" type="hidden" value="'.$product[0]['id'].'">';
		echo '<input id="cv9" type="hidden" value="'.$product[0]['price'].'">';
		echo '
		<div class="form-group">
			<label class="form-control-label">الأسم بالكامل : <span class="red">(*)</span></label>
			<input onchange="SaveInputs()" type="text" id="cv1" class="form-control">
		</div>
		<div class="form-group">
			<label class="form-control-label">العنوان : <span class="red">(*)</span></label>
			<input onchange="SaveInputs()" type="text" id="cv2" class="form-control">
		</div>
		<div class="form-group">
			<label class="form-control-label">رقم الموبايل 1 : <span class="red">(*)</span></label>
			<input onchange="SaveInputs()" type="text" id="cv3" class="form-control">
		</div>
		<div class="form-group">
			<label class="form-control-label">رقم الموبايل 2 : <span class="red">(*)</span></label>
			<input onchange="SaveInputs()" type="text" id="cv5" class="form-control">
		</div>	
		';

		echo '<div class="col-md-6"><br>
				<label class="form-control-label">الكمية : ( إجمالي السعر : <b id="newprice">'.$product[0]['price'].'</b> جنية ).</label>
				<div class="input-group" style="display: inline-flex;">
				  <span onclick="CountChange(1)" style="border-top-left-radius: 0px;border-bottom-left-radius: 0px;" class="input-group-addon countp"><i class="fa fa-plus"></i></span>
				  <input onchange="SaveInputs()" style="border-radius: 0;" id="cv4" type="text" class="form-control counti" value="1" min="1" max="100">
				  <span onclick="CountChange(-1)" style="border-top-right-radius: 0px;border-bottom-right-radius: 0px;"  class="input-group-addon countp"><i class="fa fa-minus"></i></span>
				</div>
			</div>';



	    echo '<div class="col-md-12 col-sm-12 col-xs-12 pad0" id="btnxa"><br><button onclick="addOrder()" class="btn btn-success btn-md" style="border-radius: 0;"> <i class="fa fa-check-square-o" aria-hidden="true"></i> تأكيد الشراء</button> <button data-dismiss="modal" class="btn btn-danger btn-md" style="border-radius: 0;"> <i class="fa fa-window-close" aria-hidden="true"></i> رجوع</button></div>';

	    echo '<div class="col-md-12 col-sm-12 col-xs-12 pad0"><br><div id="orderres"></div></div>';

	}else{
		echo '
	    <div class="col-md-12">
	        <div class="signal text-center">
	            <h5>الصفحة التى تحاول الوصول اليها غير موجوده</h5>
	            <h5><a href="<?php echo $Site_URL ;?>">العوده الى الصفحة الرئيسية</a></h5>
	        	<img style="max-width: 100%;" src="'.$Site_URL.'/img/404.png">
	        </div>
	    </div>';
	}	
	}else{
		echo '
		<div class="col-md-12">
			<div class="signal text-center">
				<h5>الصفحة التى تحاول الوصول اليها غير موجوده</h5>
				<h5><a href="<?php echo $Site_URL ;?>">العوده الى الصفحة الرئيسية</a></h5>
				<img style="max-width: 100%;" src="'.$Site_URL.'/img/404.png">
			</div>
		</div>';
	}	
	echo '</div>';
//--------------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'InsertOrder' && isset($_POST['productid'])  ){
	$productid = filter_var($_POST['productid']  , FILTER_SANITIZE_STRING) ;
	$product = getAllFrom('*' , 'products_title' , 'WHERE id ="'.$productid.'" ', '');
	if (count($product) > 0){
		$cv1 = filter_var($_POST['cv1']  , FILTER_SANITIZE_STRING) ;
		$cv2 = filter_var($_POST['cv2']  , FILTER_SANITIZE_STRING) ;
		$cv3 = filter_var($_POST['cv3']  , FILTER_SANITIZE_STRING) ;
		$cv4 = filter_var($_POST['cv4']  , FILTER_SANITIZE_STRING) ;
		$count = filter_var($_POST['count']  , FILTER_SANITIZE_STRING) ;

		$hash = Generate_Hash() ;
		$stmt = $db->prepare("INSERT INTO orders ( productid , name , address , phone1  , phone2 , county , datee , orderhash ) VALUES (:user_1 , :user_2 , :user_3 , :user_4 , :user_5 , :user_6 , :user_7 , :user_8 )");  
		$stmt->execute(array(
		'user_1' => $productid ,
		'user_2' => $cv1 ,
		'user_3' => $cv2 ,
		'user_4' => $cv3 ,
		'user_5' => $cv4 ,
		'user_6' => $count ,
		'user_7' => time() ,
		'user_8' => $hash
		)) ;
		
	}else{
		echo  Show_Alert('warning' , 'خطأ غير معروف ، اعد المحاولة مرة أخرى.');
	}
//--------------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'SelectMCat' && isset($_POST['xvar1'])  ){
	$xvar1 = filter_var($_POST['xvar1']  , FILTER_SANITIZE_STRING) ;
	$ch = getAllFrom('*' , 'category' , 'WHERE id ="'.$xvar1.'" AND parent = 0 AND status = 1  ', '');
	if (count($ch) > 0){
		$ch2 = getAllFrom('*' , 'category' , 'WHERE parent ="'.$ch[0]['id'].'" AND status = 1  ', '');
		if (count($ch2) > 0){
			echo '<option value="0">إختر قدرة الجهاز</option>';
			for ($i=0; $i <= count($ch2)-1 ; $i++) { 
				echo '<option value="'.$ch2[$i]['id'].'">'.$ch2[$i]['name'].'</option>';
			}
		}
	}	
//--------------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'SelectSCat' && isset($_POST['xvar2'])  ){
	$xvar2 = filter_var($_POST['xvar2']  , FILTER_SANITIZE_STRING) ;
	$ch = getAllFrom('*' , 'category' , 'WHERE id ="'.$xvar2.'"  AND status = 1  ', '');
	if (count($ch) > 0){
		$ch2 = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$ch[0]['id'].'"  ', '');
		if (count($ch2) > 0){
			echo '<option value="0">إختر موديل الجهاز</option>';
			for ($i=0; $i <= count($ch2)-1 ; $i++) { 
				echo '<option value="'.$ch2[$i]['id'].'">'.$ch2[$i]['title'].'</option>';
			}
		}
	}	
//--------------------------------------------------------------	
}elseif (isset($_POST['action']) && $_POST['action'] == 'GoToRead' && isset($_POST['xvar3'])  ){
	$xvar3 = filter_var($_POST['xvar3']  , FILTER_SANITIZE_STRING) ;
	$ch2 = getAllFrom('*' , 'products_title' , 'WHERE id ="'.$xvar3.'"  ', '');
	if (count($ch2) > 0){
			echo $ch2[0]['link'];
	}
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll'  && isset($_POST['type']) && $_POST['type'] == 'merchants'  ){
	$sub = getAllFrom('*' , 'users' , 'WHERE type = 1 ', 'ORDER BY id DESC' );
	for ($x=0; $x <= count($sub)-1 ; $x++) { 
	                    
        if ($sub[$x]['active'] == 1 ){
			$titx = 'مفعل' ; $clsx = 'btn btn-sm btn-success smb' ;
		}else{
			$titx = 'محظور' ; $clsx = 'btn btn-sm btn-warning smb' ;	
		}

		if ($sub[$x]['type'] == 1 ){
			$tit = 'شركة' ; $cls = 'btn btn-sm btn-success smb' ;
		}else{
			$tit = 'عادية' ; $cls = 'btn btn-sm btn-success smb' ;	
		}

		if (empty($sub[$x]['cname'])){
			$cname = 'غير معروف';
		}else{
			$cname = $sub[$x]['cname'];
		}

		$c = getAllFrom('*' , 'cities' , 'WHERE id = "'.$sub[$x]['cityid'].'" AND status = 1 ', 'ORDER BY id DESC');
		if (count($c) > 0){
			$city = $c[0]['name'];
		}else{
			$city = 'غير معروف';
		}

		if ($sub[$x]['certified'] == 1){
			$certified = '<i style="font-style: normal;font-size: 30px;color: #0da513;" class="fa fa-check-square-o" aria-hidden="true"></i>';
		}else{
			$certified = '<i style="font-style: normal;font-size: 30px;color: #E91E63;" class="fa fa-square-o" aria-hidden="true"></i>';
		}

		if(empty($sub[$x]['photo'])){
			$photo = '<a href="'.$Site_URL.'/company/'.$sub[$x]['cname'].'"><img src="'.$Site_URL.'/'.GetTableSet ('DefaultImage').'" class="company_logo1"></a>' ;	
		}else{
			$photo = '<a href="'.$Site_URL.'/company/'.$sub[$x]['cname'].'"><img src="'.$Site_URL.'/'.$sub[$x]['photo'].'" class="company_logo1"></a>' ;
		}
		
        echo '<tr>
                
                <td class="padding30">'.$photo.$cname.'</td>
                <td class="padding30">'.$sub[$x]['phone'].'</td>
                <td class="padding30">'.$city.'</td>
                <td class="padding30">'.date('Y/m/d' ,$sub[$x]['datetime']).'</td>
                <td class="padding30">'.$certified.'</td>
                <td class="padding30"><a href="'.$Site_URL.'/company/'.$sub[$x]['cname'].'" class="btn btn-sm btn-danger smb">مشاهدة كل العروض</a> </td>
          	</tr>';
    }
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'GetCategoryProducts' && isset($_POST['catid'])  ){

	$q = ' AND ( catid = "'.$_POST['catid'].'" ';
	$sub = getAllFrom('id,parent' , 'category' , 'WHERE parent = "'.$_POST['catid'].'" ', 'AND status = 1');
	for($x=0; $x<=count($sub)-1; $x++){
		$q .= ' OR catid = "'.$sub[$x]['id'].'" ';
		$xsub = getAllFrom('id,parent' , 'category' , 'WHERE parent = "'.$sub[$x]['id'].'" ', 'AND status = 1');
		for($z=0; $z<=count($xsub)-1; $z++){
			$q .= ' OR catid = "'.$xsub[$z]['id'].'" ';
		}
	}
	$q .= ' ) ';
	$f = '';
	if($_POST['filter'] != ''){
		$filter = $_POST['filter'];
		$xf = explode('&' , $filter);
		for($x=0; $x<=count($xf)-1;$x++){
			$cf = explode('=', $xf[$x])[1];
			if(trim($cf) != ''){
				$f .= ' filters LIKE "%,'.trim($cf).',%" OR ';
			}
		} 
	}


	if($f !=''){
		$f = substr($f, 0, -3);
		$f = ' AND (' . $f . ' ) ';
	} 
	$allproducts = getAllFrom('id,title,link,price' , 'products_title' , 'WHERE status = 1 '.$q.' '.$f.' AND type = 0', '  ORDER BY price ASC,id DESC');
	echo json_encode($allproducts, JSON_UNESCAPED_UNICODE);
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'GetProduct' && isset($_POST['id'])  ){
	echo GetProduct($_POST['id']);
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddAddress'){
	$add_name = filter_var($_POST['add_name']  , FILTER_SANITIZE_STRING) ;
	$add_phone = filter_var($_POST['add_phone']  , FILTER_SANITIZE_STRING) ;
	$add_address = filter_var($_POST['add_address']  , FILTER_SANITIZE_STRING) ;
	$AllAddress = array();
	$uads = json_decode($UserAdress);
	if(is_array($uads) && count($uads) > 0){
		for($i=0; $i<=count($uads)-1; $i++){
			array_push($AllAddress , array(
				'name' => $uads[$i]->name,
				'phone' => $uads[$i]->phone,
				'address' => $uads[$i]->address,
			));
		}
	}

	array_push($AllAddress , array(
		'name' => $add_name,
		'phone' => $add_phone,
		'address' => $add_address
	));

	$stmt = $db->prepare("UPDATE users SET adress = :var1 WHERE  id = :var0 ");  
    $stmt->execute(array( 'var1' => json_encode($AllAddress, JSON_UNESCAPED_UNICODE) , 'var0' => $User_ID ));
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'RemoveAddress'){
	$AllAddress = array();
	$uads = json_decode($UserAdress);
	if(is_array($uads) && count($uads) > 0){
		for($i=0; $i<=count($uads)-1; $i++){
			if($i != $_POST['index']){
				array_push($AllAddress , array(
					'name' => $uads[$i]->name,
					'phone' => $uads[$i]->phone,
					'address' => $uads[$i]->address,
				));
			}
		}
	}

	$stmt = $db->prepare("UPDATE users SET adress = :var1 WHERE  id = :var0 ");  
    $stmt->execute(array( 'var1' => json_encode($AllAddress, JSON_UNESCAPED_UNICODE) , 'var0' => $User_ID ));
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'ConfirmToAddOrder'){
	$name = filter_var($_POST['name']  , FILTER_SANITIZE_STRING) ;
	$phone = filter_var($_POST['phone']  , FILTER_SANITIZE_STRING) ;
	$address = filter_var($_POST['address']  , FILTER_SANITIZE_STRING) ;
	$Cart = array();
	$all_cart = $_SESSION['UserCart'] ;
	for($i=0; $i<=count($all_cart)-1; $i++){
		$product = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$all_cart[$i]['productid'].'" ', '');
		if(count($product) > 0){
			array_push($Cart , array(
				'title' => $product[0]['title'],
				'price' => $product[0]['price'],
				'ship' => $all_cart[$i]['ship'],
				'city' => $all_cart[$i]['city'],
				'filter' => $all_cart[$i]['filter'],
				'count' => $all_cart[$i]['count'],
			));
		}
	}
 
	
	$hash = Generate_Hash() ;
	$stmt = $db->prepare("INSERT INTO orders ( userid , orderhash , products , name  , address , phone1 , datee ) VALUES (:user_1 , :user_2 , :user_3 , :user_4 , :user_5 , :user_6 , :user_7  )");  
	$stmt->execute(array(
	'user_1' => $User_ID ,
	'user_2' => $hash ,
	'user_3' => json_encode($Cart, JSON_UNESCAPED_UNICODE) ,
	'user_4' => $name ,
	'user_5' => $address ,
	'user_6' => $phone ,
	'user_7' => time()
	)) ;

	$_SESSION['UserCart'] = array();
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'ConfirmToAddOrderNew'){
	 
	$hash = Generate_Hash() ;
	$stmt = $db->prepare("INSERT INTO new_orders ( hash , name , phone , city  , area , street , emark , n_name , n_phone , totalprice , totalship , cartproduct , datee , bank_username , receipt , coupon ) VALUES (:user_1 , :user_2 , :user_3 , :user_4 , :user_5 , :user_6 , :user_7 , :user_8 , :user_9 , :user_10 , :user_11 , :user_12 , :user_13 , :user_14 , :user_15 , :user_16 )");  
	$stmt->execute(array(
	'user_1' => $hash ,
	'user_2' => $_POST['um1'] ,
	'user_3' => $_POST['um2'] ,
	'user_4' => $_POST['cty1'] ,
	'user_5' => $_POST['cty2'] ,
	'user_6' => $_POST['cty3'] ,
	'user_7' => $_POST['cty4'] ,
	'user_8' => $_POST['xm1'] ,
	'user_9' => $_POST['xm2'] ,
	'user_10' => $_POST['totalprice'] ,  
	'user_11' => $_POST['totalship'] ,  
	'user_12' => $_POST['cartproduct'] ,  
	'user_13' => time(),
	'user_14' => $_POST['imus'],
	'user_15' => $_POST['im'],
	'user_16' => $_POST['coupon']
	)) ;

	$_SESSION['UserCart'] = array();
//--------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'Like'){
	$ul  = $_SESSION['UserLikes'];
	$newlike = array();
	$ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_POST['productid'].'" ', '');
	if(count($ch) > 0){

		if($_POST['type'] == 'add'){
			
			if(!in_array($_POST['productid'] , $ul)){
				array_push($ul , $_POST['productid']);
			}  
			$newlike = $ul;
			$stmt = $db->prepare("UPDATE products_title SET likes = :var1 WHERE  id = :var0 ");  
			$stmt->execute(array( 'var1' => $ch[0]['likes'] + 1 , 'var0' => $_POST['productid'] ));

		}elseif ($_POST['type'] == 'remove'){
			
			for ($i=0; $i <= count($ul)-1 ; $i++) { 
				if($ul[$i] != '' && $ul[$i] != $_POST['productid']){
					array_push($newlike , $_POST['productid']);
				}
			}

			if($ch[0]['likes'] > 0){
			$stmt = $db->prepare("UPDATE products_title SET likes = :var1 WHERE  id = :var0 ");  
			$stmt->execute(array( 'var1' => $ch[0]['likes'] - 1 , 'var0' => $_POST['productid'] ));
			}
		}
	}
	$_SESSION['UserLikes'] = $newlike;
//--------------------------------------------------------------
//--------------------------------------------------------------

}