<?php
ob_start();
$Title_page = 'تسجيل الدخول' ;
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');
if (isset($_SESSION['userData'])){ header("Location:".$Site_URL.'/'); exit();}
echo '<div class="container-fluid products-content">';
?>
<div class="row"> 
        <div class="contact">
        	<div class="col-md-4 col-md-offset-4">
        		<div class="signal">
                <h5 class="text-center" style="margin-bottom: 20px;"><?php echo $Title_page;?></h5>
                <form method="post">
    			<div class="col-md-12 pading5">
    	                <div class="form-group">
    	                  <label class="form-control-label">رقم الموبايل</label>
	                  <input type="text" name="log_var1" class="form-control ltr">
	                </div>
	            </div>
	            <div class="col-md-12 pading5">    
    	                <div class="form-group">
    	                  <label class="form-control-label">كلمة المرور</label>
	                  <input type="password" name="log_var2" class="form-control ltr">
	                </div>
	            </div>
	            <div class="col-md-12 pading5">    
    	                <div class="form-group">
    	                    <button type="submit" name="login" class="btn btn-danger btn-block ">دخول</button>
	                </div> 
	                <div class="form-group">
    	                 <a href="<?php echo $Site_URL.'/register.php' ;?>">تسجيل حساب جديد !</a>   
                    </div> 
                    <div class="form-group">
                        <?php
                        if (isset($_POST['login'])){
                            $log_var1 = filter_var($_POST['log_var1']  , FILTER_SANITIZE_STRING) ;
                            $log_var2 = filter_var($_POST['log_var2']  , FILTER_SANITIZE_STRING) ;
                            $check = getAllFrom('*' , 'users' , 'WHERE phone = "'.$log_var1.'" AND password = "'.$log_var2.'" ', '');
                            if (count($check) > 0){
                                $stmt = $db->prepare("UPDATE users SET ip = :var1 WHERE  id = :var0 ");  
                                $stmt->execute(array('var1' => get_client_ip() , 'var0' => $check[0]['id'] )); 
                                $_SESSION['userData'] = $check[0]['id'];
                                echo  Show_Alert('info' , 'تم تسجيل الدخول بنجاح . سيتم تحويلك خلال 1 ثواني. ');
                                header('Location: '.$Site_URL.'/'); exit();
                                
                            }else{
                                echo Show_Alert('danger' , 'رقم الموبايل او كلمة المرور غير صحيحة');
                            }
                        }
                        ?>
	                </div>   
                </div>
                </form>
    		</div>	
    	</div>
    </div>
</div>
<?php
    echo ' </div>';
    include('footer.php'); 
    ob_end_flush();
?>
