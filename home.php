<?php
$thisIsHomePage = '';
include('header.php'); 
include('navbar.php');
?>

<!-- Swiper CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>








<div class="container">

<!--<div class="swiper mySwiper mb-4">
  <div class="swiper-wrapper">
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 1"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 2"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 3"></div>
    <div class="swiper-slide"><img src="img/SHE.png" alt="Slide 4"></div>
  </div>
</div>-->




<style>
.mySwiper {
  width: 100%;
  padding-left: 10px;
  margin-bottom: 30px; 
}
.mySwiper .swiper-slide {
  height: 200px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .mySwiper .swiper-slide {
    width: 85%;
  }
}

@media (min-width: 769px) {
  .mySwiper .swiper-slide {
    width: auto;
  }
}

.mySwiper .swiper-slide img {
  width: 100%;
  height: auto;
  object-fit: cover;
}



</style>




    <!-- سلايدر الأقسام -->
    <div class="swiper categorySwiper">
        <div class="swiper-wrapper">
            <?php
                for ($i=0; $i <= count($allcategory)-1 ; $i++) { 
                    if($allcategory[$i]['showindex'] == 3){
                        echo '<div class="swiper-slide">';
                        echo '<div class="index-box2 text-center">';
                        if (!empty($allcategory[$i]['photo'])){
                            echo '<a href="'.$Site_URL.'/category/'.$allcategory[$i]['link'].'">
                                    <img alt="'.$allcategory[$i]['name'].'" src="'.$Site_URL.'/'. $allcategory[$i]['photo'].'" title="'.$allcategory[$i]['name'].'" />
                                  </a>';
                        } else {
                            echo '<a href="'.$Site_URL.'/category/'.$allcategory[$i]['link'].'">
                                    <img alt="'.$allcategory[$i]['name'].'" src="'.$Site_URL.'/'. GetTableSet('DefaultImage').'" title="'.$allcategory[$i]['name'].'" />
                                  </a>';
                        }
                        echo '<p><a href="'.$Site_URL.'/category/'.$allcategory[$i]['link'].'">'.$allcategory[$i]['name'].'</a></p>';
                        echo '</div></div>';
                    }
                }
            ?>
        </div>

  
    </div>

    <?php
        $products = getAllFrom('*','products_title' ,'WHERE status = 1 AND Quantity > 0 ','ORDER BY id DESC LIMIT '.GetTableSet('IndexProductsComp'));
        if (count($products) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<h2 class="product-title">فساتين سهره السعودية</h2>';
            for ($i=0; $i <= count($products)-1 ; $i++) { 
                echo GetProduct($products[$i]['id']);
            }
        }

        echo ' <a href="/category/evening-dresses" class="btn-success2">تسوقي جميع الفساتين</a>';

        $tags = getAllFrom('*','tags' ,'','ORDER BY count DESC ,id ASC ');
        if (count($tags) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<h2 class="product-title">الأكثر بحثاً</h2>';
            for ($i=0; $i <= count($tags)-1 ; $i++) { 
                echo '<a class="tgs" style="font-size:'.(100+$tags[$i]['count']).'%" href="'.$tags[$i]['link'].'">'.$tags[$i]['name'].'</a>';
            }
            echo '<div class="col-md-12"><br></div>';
        }

        $blogs = getAllFrom('*' , 'blog' , '', 'ORDER BY id DESC LIMIT 20');
        if(count($blogs) > 0){
            echo '<div class="col-md-12"><br></div>';
            echo '<div class="col-md-12 padding0">';
            for($i=0; $i<= count($blogs)-1; $i++){
                echo Get_The_Posts($blogs[$i]['id']);
            }
            echo '</div>';
        }
    ?>
</div>

<!-- Swiper JS -->
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
    const swiper = new Swiper(".categorySwiper", {
        slidesPerView: 4,
        spaceBetween: 10,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        breakpoints: {
            0: {
                slidesPerView: 4,
                spaceBetween: 10
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 15
            },
            992: {
                slidesPerView: 6,
                spaceBetween: 20
            }
        }
    });
</script>


<script>
const swiperMain = new Swiper(".mySwiper", {
  loop: true,
  spaceBetween: 15,
  breakpoints: {
    // موبايل
    0: {
      slidesPerView: 'auto',
    },
    // كمبيوتر
    768: {
      slidesPerView: 3,
    },
  },
});



</script>




<?php
include('footer.php'); 
ob_end_flush();
?>



