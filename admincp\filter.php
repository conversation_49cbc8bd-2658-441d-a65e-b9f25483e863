<?php
ob_start();
$Title_page = 'الفلاتر';
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php'); 

//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'filter' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'filter', 'WHERE id = '.$_GET['id'] );
			
			
			header('Location: filter.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'filter' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
			     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['name'];?> " </b> ؟</h3>
			     <p>برجاء العلم انه سيتم الحذف بشكل نهائي.</p>

			     <center>
			     	<a class="btn btn-danger btn-md" href="filter.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-md" href="filter.php">رجوع</a>
			     </center>
		 
			</div>	
		</div>
	</div>
</div>
<?php
	}else{
		header('Location: filter.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'filter' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel">التعديل على <i class="fa fa-hand-o-left"></i><b> <?php echo $ch[0]['name'];?></b> </h4>
				</div>
			</div>
		</div>			
	</div>		
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
	                 	$var2  = preg_split('/\r\n|[\r\n]/', $_POST['var2']) ;

						if (empty($var1)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك كتابة الأسم.');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("UPDATE filter
							    SET name = :var1 , value = :var2  , show_in_product = :var3  
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => json_encode($var2, JSON_UNESCAPED_UNICODE) ,
								'var3' => $_POST['var3'] ,
					            'var0' => $_GET['id']
					          )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

					        redirect_home ('back' , 1); exit();
					    }
	                 }



                     $val = $ch[0]['value'];
                     $val = json_decode($val);
                     $txval = '';
                     for($i=0; $i<= count($val)-1; $i++){
                        if(trim($val[$i]) != ''){
                            $txval .= trim($val[$i]) . PHP_EOL;
                        }
                     }
                     $txval = rtrim($txval , PHP_EOL);
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الإسم</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['name'];?>" class="form-control">
		                    </div>
                            <br>
							<div class="form-group">
		                      <label class="form-control-label">غرض الفلتر فى اختيارات المنتج</label>
		                      <select class="form-control" name="var3">
					 				<option value="1">نعم</option>
					 				<option value="0">لا</option>
							  </select>
		                    </div>
                            <br>
                            <div class="form-group">
                                <label class="form-control-label">القيمه كل واحده فى سطر</label>
                                <textarea style="min-height: 300px;" class="form-control" name="var2"><?php echo $txval;?></textarea>
                            </div>

	               	 	</div>
						

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                      <a href="filter.php" class="btn btn-lg btn-primary">رجوع</a>
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>


			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: filter.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة فلتر جديد</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
	                 	$var2  = preg_split('/\r\n|[\r\n]/', $_POST['var2']) ;
	                 	
						if (empty($var1)){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك كتابة الأسم.');
					        echo '</div>'; 
						}else{
							$stmt = $db->prepare("INSERT INTO filter ( name , value , show_in_product ) 
							 VALUES (:user_1 , :user_2 , :user_3  )");  
							$stmt->execute(array(
					          'user_1' => $var1 , 'user_2' => json_encode($var2, JSON_UNESCAPED_UNICODE) , 'user_3' => $_POST['var3'] )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة فلتر جديد بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الفلاتر خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=filter.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
					    
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم</label>
		                      <input type="text" name="var1" value="" class="form-control">
		                    </div>
                            <br>
							<div class="form-group">
		                      <label class="form-control-label">غرض الفلتر فى اختيارات المنتج</label>
		                      <select class="form-control" name="var3">
					 				<option value="1">نعم</option>
					 				<option value="0">لا</option>
							  </select>
		                    </div>
                            <br>
                            <div class="form-group">
                                <label class="form-control-label">القيمه كل واحده فى سطر</label>
                                <textarea style="min-height: 300px;" class="form-control" name="var2"></textarea>
                            </div>
	               	 	</div>
						
	               	 	
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="filter.php?do=add_new" class="btn btn-lg btn-success">إضافة فلتر جديد</a> 
			</div>
		</div>
	</div>			
</div>


	<?php
	echo '<div class="row">
	<div class="col-md-12 col-sm-12">';
	
	$check = getAllFrom('*' , 'filter' , '', 'ORDER BY id DESC');
	if(count($check) > 0){
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if($check[$i]['show_in_product'] == 0){
				$show = 'غير معروض فى اختيارات المنتج';
			}else{
				$show = 'معروض فى اختيارات المنتج';
			}
			echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card"><hr>
			<div class="card-body">';
			echo '<div class="divs">';
			echo '<h4 class="cattitlel">'.$check[$i]['name'].' -> '. $show.'</h4>';
            $fl = json_decode($check[$i]['value']);
            echo '<ul>';
            for($x=0; $x<=count($fl)-1;$x++){
                if(trim($fl[$x]) != ''){
                    echo '<li>'.trim($fl[$x]).'</li>';
                }
            }
            echo '</ul>';
			echo ' <a href="filter.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary">تعديل</a> ';
			echo ' <a href="filter.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '</div>';
			
	
			echo '</div></div></div></div>';
			
		}
	}else{
		echo  '<br>'.Show_Alert('warning' , ' لا يوجد أى فلاتر. ');
	}	
	echo '</div>';


	echo '</div>';

}

include('footer.php'); 
ob_end_flush();
?>