<?php

ob_start();

$Title_page = 'الماركات';

include('../webset.php');

include('../session.php'); 

include('header.php'); 

include('navbar.php'); 



//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){

	$ch = getAllFrom('*' , 'logos' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['status'] == 0){

			UpdateTable('logos' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );

		}else{

			UpdateTable('logos' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'logos' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] >= 0){

			UpdateTable('logos' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'logos' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] > 0){

			UpdateTable('logos' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){

	$ch = getAllFrom('*' , 'logos' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		

			DeleteColum( 'logos', 'WHERE id = '.$_GET['id'] );

			

			header('Location: logos.php');	exit();

	}else{

		redirect_home ('back' , 0); exit();

	} 

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'logos' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

?>

<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body text-center">

		     

			     <h3>هل انت متأكد من انك تريد الحذف ؟</h3>

			     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>

			     <br>

			     <center>

			     	<a class="btn btn-danger btn-lg" href="logos.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>

			     	<a class="btn btn-success btn-lg" href="logos.php">رجوع</a>

			     </center>

		 

			</div>	

		</div>

	</div>

</div>

<?php

	}else{

		header('Location: logos.php'); exit();

	} 

//---------------------------------------------------

}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){

	?>

	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">

					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة صورة ماركة جديده</h4>

				</div>

			</div>

		</div>

	</div>



	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">				

					<div class="col-md-6 col-md-offset-3">

	                 	<label class="form-control-label" style="text-align: center; width: 100%;">صورة الماركة </label>

					  <center>

					    <img style="margin: 20px 0px; width: 300px; max-width: 100%;" src="<?php echo $Site_URL.'/'.GetTableSet ('DefaultImage') ;?>">

					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >

					         

					          <input type="file" name="photo" id="photo" required style="display: none;" />

					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />

					          <input type="hidden" name="Image_For" value="Logos">

					        </form>

					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>

					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>

					  </center>

	                 </div>	

	                 

				</div>

			</div>

		</div>

	</div>		

<?php	

//---------------------------------------------------	

}else{

?>

<div class="row">

	<div class="col-md-12 col-sm-12">

		<div class="card">

			<div class="card-body">

				<a href="logos.php?do=add_new" class="btn btn-lg btn-primary">إضافة صورة  ماركة جديده</a>

			</div>

		</div>
		<hr>
	</div>			

</div>





	<?php

	$check = getAllFrom('*' , 'logos' , '', 'ORDER BY orders DESC ,id DESC');

	if(count($check) > 0){

		for ($i=0; $i <= count($check)-1 ; $i++) { 

			if ($check[$i]['status'] == 0 ){

				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;

			}else{

				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	

			}

			echo '<div class="col-md-4 col-sm-4"><div class="card">

			<div class="card-body">';

			echo '<div class="divs">';

			echo '<h4 class="cattitlel text-center">'.$check[$i]['orders'].' - بتاريخ : '.date("Y-m-d H:s" , $check[$i]['datetime']).'</h4><center>';

			echo ' <a href="logos.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';

			echo ' <a href="logos.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';

			echo '<a href="logos.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-up"></i></a>  

                        	<a href="logos.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-down"></i></a>';

			echo '</center></div>';

			echo '<div class="divs"><center><img style="width: 200px;height: 100px;" src="'.$Site_URL.'/'.$check[$i]['photo'].'"></center>';

			





			echo '<hr></div></div></div></div>';

		}

	}else{

		echo  Show_Alert('warning' , 'لا يوجد أى صور  للماركات. ');

	}	



}

?>

<?php

include('footer.php'); 

ob_end_flush();

?>