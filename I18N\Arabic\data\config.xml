<?xml version="1.0" encoding="utf-8"?>
<data file="Arabic">
  <!--
  Set of configuration used by main class file
  -->
    <compatible>
		<case old="EnTransliteration">Transliteration</case>
		<case old="ArTransliteration">Transliteration</case>
		<case old="ArAutoSummarize">AutoSummarize</case>
		<case old="ArCharsetC">CharsetC</case>
		<case old="ArCharsetD">CharsetD</case>
		<case old="ArDate">Date</case>
		<case old="ArGender">Gender</case>
		<case old="ArGlyphs">Glyphs</case>
		<case old="ArIdentifier">Identifier</case>
		<case old="ArKeySwap">KeySwap</case>
		<case old="ArNumbers">Numbers</case>
		<case old="ArQuery">Query</case>
		<case old="ArSoundex">Soundex</case>
		<case old="ArStrToTime">StrToTime</case>
		<case old="ArWordTag">WordTag</case>
		<case old="ArCompressStr">CompressStr</case>
		<case old="ArMktime">Mktime</case>
		<case old="ArStemmer">Stemmer</case>
		<case old="ArStandard">Standard</case>
		<case old="ArNormalise">Normalise</case>
		<case old="a4_max_chars">a4MaxChars</case>
		<case old="a4_lines">a4Lines</case>
		<case old="swap_ea">swapEa</case>
		<case old="swap_ae">swapAe</case>
	</compatible>

	<lazyLoading>
		<case method="loadExtra">AutoSummarize</case>
		<case method="doSummarize">AutoSummarize</case>
		<case method="doRateSummarize">AutoSummarize</case>
		<case method="highlightSummary">AutoSummarize</case>
		<case method="highlightRateSummary">AutoSummarize</case>
		<case method="getMetaKeywords">AutoSummarize</case>
		<case method="cleanCommon">AutoSummarize</case>
		<case method="guess">CharsetD</case>
		<case method="getCharset">CharsetD</case>
		<!-- Conflict with Soundex class similar methods -->
		<!-- <case method="setLang">CompressStr</case> -->
		<case method="compress">CompressStr</case>
		<case method="decompress">CompressStr</case>
		<case method="search">CompressStr</case>
		<case method="length">CompressStr</case>
		<case method="setMode">Date</case>
		<case method="getMode">Date</case>
		<case method="date">Date</case>
		<case method="dateCorrection">Date</case>
		<case method="isFemale">Gender</case>
		<case method="a4MaxChars">Glyphs</case>
		<case method="a4Lines">Glyphs</case>
		<case method="utf8Glyphs">Glyphs</case>
		<case method="setLanguage">Hiero</case>
		<case method="getLanguage">Hiero</case>
		<case method="str2graph">Hiero</case>
		<case method="identify">Identifier</case>
		<case method="isArabic">Identifier</case>
		<case method="swapAe">KeySwap</case>
		<case method="swapEa">KeySwap</case>
		<case method="swapAf">KeySwap</case>
		<case method="swapFa">KeySwap</case>
		<case method="fixKeyboardLang">KeySwap</case>
		<case method="mktime">Mktime</case>
		<case method="mktimeCorrection">Mktime</case>
		<case method="hijriMonthDays">Mktime</case>
		<case method="stripTatweel">Normalise</case>
		<case method="stripTashkeel">Normalise</case>
		<case method="normaliseHamza">Normalise</case>
		<case method="normaliseLamaleph">Normalise</case>
		<case method="unichr">Normalise</case>
		<case method="normalise">Normalise</case>
		<case method="unshape">Normalise</case>
		<case method="utf8Strrev">Normalise</case>
		<case method="isTashkeel">Normalise</case>
		<case method="isHaraka">Normalise</case>
		<case method="isShortharaka">Normalise</case>
		<case method="isTanwin">Normalise</case>
		<case method="isLigature">Normalise</case>
		<case method="isHamza">Normalise</case>
		<case method="isAlef">Normalise</case>
		<case method="isWeak">Normalise</case>
		<case method="isYehlike">Normalise</case>
		<case method="isWawlike">Normalise</case>
		<case method="isTehlike">Normalise</case>
		<case method="isSmall">Normalise</case>
		<case method="isMoon">Normalise</case>
		<case method="isSun">Normalise</case>
		<case method="charName">Normalise</case>
		<case method="setFeminine">Numbers</case>
		<case method="setFormat">Numbers</case>
		<case method="setOrder">Numbers</case>
		<case method="getFeminine">Numbers</case>
		<case method="getFormat">Numbers</case>
		<case method="getOrder">Numbers</case>
		<case method="int2str">Numbers</case>
		<case method="money2str">Numbers</case>
		<case method="str2int">Numbers</case>
		<case method="int2indic">Numbers</case>
		<case method="setArrFields">Query</case>
		<case method="setStrFields">Query</case>
		<!-- Conflict with Date class similar methods -->
		<!-- <case method="setMode">Query</case> -->
		<!-- <case method="getMode">Query</case> -->
		<case method="getArrFields">Query</case>
		<case method="getStrFields">Query</case>
		<case method="getWhereCondition">Query</case>
		<case method="getOrderBy">Query</case>
		<case method="allForms">Query</case>
		<case method="setDate">Salat</case>
		<case method="setLocation">Salat</case>
		<case method="setConf">Salat</case>
		<case method="getPrayTime">Salat</case>
		<case method="getPrayTime2">Salat</case>
		<case method="getQibla">Salat</case>
		<case method="coordinate2deg">Salat</case>
		<case method="setLen">Soundex</case>
		<case method="setLang">Soundex</case>
		<case method="setCode">Soundex</case>
		<case method="getLen">Soundex</case>
		<case method="getLang">Soundex</case>
		<case method="getCode">Soundex</case>
		<case method="soundex">Soundex</case>
		<case method="standard">Standard</case>
		<case method="stem">Stemmer</case>
		<case method="strtotime">StrToTime</case>
		<case method="en2ar">Transliteration</case>
		<case method="ar2en">Transliteration</case>
		<case method="enNum">Transliteration</case>
		<case method="arNum">Transliteration</case>
		<case method="isNoun">WordTag</case>
		<case method="tagText">WordTag</case>
		<case method="highlightText">WordTag</case>                               
	</lazyLoading>
</data>