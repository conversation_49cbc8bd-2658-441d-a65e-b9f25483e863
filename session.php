<?php
ob_start();
session_start();
//session_name("userData");
//--------------------------------------------------------------------- 
$User_ID = 0 ;  $FullName = '' ; $UserPhone = '' ; $User_Type = 0 ; $UserDatetime = ''; $UserAdress = '';
//---------------------------------------------------------------------

if (isset($_SESSION['userData'])){
	$user_id = $_SESSION['userData'];
	$UserData =  getAllFrom('*' , 'users' , 'WHERE id = "'.$user_id.'" ', '');
	if (count($UserData) > 0 ){
		$User_ID  =  $UserData[0]['id'];
		$FullName =  $UserData[0]['fullname'];
		$UserPhone = $UserData[0]['phone'];
		$UserAdress = $UserData[0]['adress'];
		$UserDatetime = $UserData[0]['datetime'];
		$UserPassword = $UserData[0]['password'];
		$User_Type = $UserData[0]['type']; 
	}else{
		session_destroy();
		session_unset();
		unset($_SESSION['userData']);
		header("Location:".$Site_URL.'/logout.php'); 
		exit();
	}

	if ($UserData[0]['active'] == 0 ){header("Location:".$Site_URL.'/block.php'); exit();}
}
if(!isset($_SESSION['UserCart'])){
	$_SESSION['UserCart'] = array();
}
if(!isset($_SESSION['UserLikes'])){
	$_SESSION['UserLikes'] = array();
}
