<?php
ob_start();

function GetRate($productid){
	$rate = 0 ;
	$count = 0 ;
	$p = getAllFrom('*' , 'products' , 'WHERE id = "'.$productid.'" ', 'AND status = 1');
	if (count($p) > 0){
		$r = getAllFrom('count(id),sum(rate)' , 'ratings' , 'WHERE productid = "'.$productid.'" ', '');
		if (count($r) > 0){
			if($r[0]['sum(rate)'] > 0 && $r[0]['count(id)'] > 0){
				$count = $r[0]['count(id)'];
				$rate = $r[0]['sum(rate)']/$r[0]['count(id)'];	
			}
		}
		$rate = ceil($rate);
		echo '<p class="rev">';
		for ($i=1; $i <= 5 ; $i++) { 
			if ($rate >= $i){
				echo '<i class="fa fa-star"></i>';
			}else{
				echo '<i class="fa fa-star-o"></i>';
			}
		}
		if ($count == 0){
			$tit = 'لم يتم تقييمه';
		}elseif ($count == 2){
			$tit = 'تقييمان';
		}elseif ($count > 2){
			$tit = $count.' تقييمات';
		}else{
			$tit = $count.' تقييم';
		}
		echo '<span class="margin-right-10">'.$tit.'</span></p>';

	}
}
//-------------------------------------------
function GetProduct($productid){
	global $Site_URL;
	$p = getAllFrom('*' , 'products' , 'WHERE id = "'.$productid.'" ', 'AND status = 1');
	if (count($p) > 0){
		if (empty($p[0]['photo'])){
			$img = GetTableSet ('DefaultImage');
		}else{
			$x =explode('&&&', $p[0]['photo']);
			if (count($x) > 1){
				$igid = getAllFrom('*' , 'photos' , 'WHERE id = "'.$x[0].'" ', '');
				if (count($igid) > 0){
					$img = $igid[0]['link'];
				}else{
					$img = GetTableSet ('DefaultImage');	
				}
			}else{
				$img = GetTableSet ('DefaultImage');
			}
		}
		$cid = getAllFrom('*' , 'category' , 'WHERE id = "'.$p[0]['catid'].'" ', '');
		if (count($cid) > 0){
			$cname = $cid[0]['name'];
		}else{
			$cname = 'غير معروف';
		}
		echo '<div class="col-md-25"><div class="owl-item"><div class="product"><article> 
            	<a href="'.$Site_URL.'/'.$p[0]['link'].'"><img class="img-responsive" src="'.$Site_URL.'/'.$img.'" alt="'.$Site_URL.'/'.$p[0]['title'].'"></a>';
		            if (!empty($p[0]['discount']) && $p[0]['discount'] != 0){
		            	echo  '<span class="sale-tag">خصم '.$p[0]['discount'].' جنية</span>';
		            }
        echo '<span class="tag">'.$cname.'</span><a href="'.$Site_URL.'/'.$p[0]['link'].'" class="tittle">'.$p[0]['title'].'</a>';
        GetRate($p[0]['id']); 
        echo '<div class="price">السعر : '.number_format($p[0]['price'] , 2).' جنيه</div>';
        echo '<p class="delivery">التوصيل خلال '.$p[0]['delivery'].' أيام</p>';
        echo '<a href="'.$Site_URL.'/cart.php?productid='.$p[0]['id'].'" class="cart" data-toggle="tooltip" data-placement="right" title="أضف الى السلة"><i class="fa fa-cart-arrow-down"></i></a>';
        echo '</article></div></div></div>';
	}
}
//--------------------------------------------------------
function GetBreadcrumb($parts){
	global $Site_URL;
	echo '<ol class="breadcrumb"><li><a href="'.$Site_URL.'">الرئيسية</a></li>';
	for ($i=0; $i <= count($parts)-1 ; $i++) { 
		$tit = str_replace('-', ' ', urldecode($parts[$i]));
		if ($tit != 'category' && $tit != 'seller'){
			if ($i == count($parts)-1){
				echo '<li class="active">'.$tit.'</li>';
			}else{
				echo '<li>'.$tit.'</li>';
			}
		}
	}
	echo '</ol>';
}
//--------------------------------------------------------
function Get_Category_Page($catid , $parts){
	$cid = getAllFrom('*' , 'category' , 'WHERE id = "'.$catid.'" AND status = 1', '');
	if (count($cid) > 0){
		$cname = $cid[0]['name'];
		$parent = $cid[0]['parent'];
	}else{
		$cname = 'غير معروف';
		$parent = 0;
	}
	echo '<section class="paid-product"><div class=" container"><div class="row"><div class="col-md-12 nopadding">';
	GetBreadcrumb($parts);
	echo '<h2 class="paid-title">'.$cname.'</h2>';
	if ($parent == 0){
		$q = "  ";
		$sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
		if (count($sub) > 0){
			$q = " OR ";
			for ($i=0; $i <= count($sub)-1 ; $i++) { 
				if ($i == count($sub)-1){
					$q = $q .' catid = '.$sub[$i]['id'];
				}else{
					$q = $q .' catid = '.$sub[$i]['id'] . ' OR';	
				}
			}
		}
		$paid = getAllFrom('*' , 'products' , 'WHERE catid ="'.$catid.'" '.$q.' AND status = 1 ', '  ORDER BY paid DESC , id DESC');
	}else{
		$paid = getAllFrom('*' , 'products' , 'WHERE catid ="'.$catid.'"', 'AND status = 1 ORDER BY paid DESC , id DESC');	
	}
	if (count($paid) > 0){
		for ($i=0; $i <= count($paid)-1 ; $i++) { 
			GetProduct($paid[$i]['id']);
		}
	}else{

		echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
	}
	echo '</div></div></div></section><hr>';
}
//--------------------------------------------------------
function Get_Seller_Page($userid , $parts){
	$cid = getAllFrom('*' , 'users' , 'WHERE id = "'.$userid.'" ', '');
	if (count($cid) > 0){
		$cname = $cid[0]['username'];
	}else{
		$cname = 'غير معروف';
	}
	echo '<section class="paid-product"><div class=" container"><div class="row"><div class="col-md-12 nopadding">';
	GetBreadcrumb($parts);
	echo '<h2 class="paid-title">جميع منتجات '.$cname.'</h2>';
	
		
	$paid = getAllFrom('*' , 'products' , 'WHERE userid ="'.$userid.'" AND status = 1', '  ORDER BY paid DESC , id DESC');
	
	if (count($paid) > 0){
		for ($i=0; $i <= count($paid)-1 ; $i++) { 
			GetProduct($paid[$i]['id']);
		}
	}else{
		echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
	}
	echo '</div></div></div></section><hr>';
}
//--------------------------------------------------------
function Get_Product_Content($productid){
	global $Site_URL;	
	$product = getAllFrom('*' , 'products' , 'WHERE id ="'.$productid.'" AND status = 1 ', '');
	if (count($product) > 0){
		$photos = array();
		$def = GetTableSet ('DefaultImage');
		if (empty($product[0]['photo'])){
			$photos = array($def);
		}else{
			$p = explode('&&&', $product[0]['photo']);
			if(count($p) >0){
				for ($i=0; $i <= count($p)-1 ; $i++) { 
					if ($p[$i] != ""){
						$l = getAllFrom('*' , 'photos' , 'WHERE id ="'.$p[$i].'" ', '');
						if (count($l) >0){
							array_push($photos, $l[0]['link']);
						}
					}
				}
			}
		}

		$catname = '';
		$canm = getAllFrom('*' , 'category' , 'WHERE id ="'.$product[0]['catid'].'" ', '');
		if (count($canm) > 0){
			$catname = '<a href="'.$Site_URL.'/category/'.$canm[0]['link'].'">'.$canm[0]['name'].'</a>';
		}else{
			$catname = 'غير معروف';
		}

		$user = getAllFrom('*' , 'users' , 'WHERE id ="'.$product[0]['userid'].'" ', '');
		if (count($user) > 0){
			$phone = trim($user[0]['phone']);
			$phone = str_replace('+2', '', $phone);
			$phone = str_replace('002', '', $phone);
			$seller = '<a href="'.$Site_URL.'/seller/'.$user[0]['username'].'">'.$user[0]['username'].'</a>';
		}else{
			$phone = '';
			$seller = 'غير معروف';
		}

		echo '<section class="products-content">';
		echo '<div class="col-md-12">';
		GetBreadcrumb(array($product[0]['title']));
		echo '</div>';
		//-----------------------------
		echo '<div class="col-md-12">';
		//---
		echo '<div class="col-md-5">';//photo section
		echo '<h1>'.$product[0]['title'].'</h1>';
		echo '<h2 class="catg">القسم : '.$catname.' <span class="sprr">';
		GetRate($product[0]['id']);
		echo'</span></h2>';
		echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
			if (count($photos) > 0){
				for ($i=0; $i <=count($photos)-1 ; $i++) { 
					echo '<li><a href="'.$Site_URL.'/'.$photos[$i].'"><img src="'.$Site_URL.'/'.$photos[$i].'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
				}
			}else{
				echo '<li><a href="'.$Site_URL.'/'.$def.'"><img src="'.$Site_URL.'/'.$def.'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
				
			}
		echo '</ul></article></div>';
		echo '</div>';
		//---
		echo '<div class="col-md-4">';//desc section
		echo '<h3> السعر : '.number_format($product[0]['price'],2).'<p class="pound">جنية</p></h3><hr>';
		echo '<p class="desc">'.nl2br($product[0]['descr']).'</p>';

		echo '</div>';
		//---
		echo '<div class="col-md-3"><div class="send-rate"><p>تقييم المنتج</p><div class="rrate"><i onclick="Rate(1,'.$product[0]['id'].')" id="s1" class="fa fa-star-o cr"></i><i onclick="Rate(2,'.$product[0]['id'].')" id="s2" class="fa fa-star-o cr"></i><i onclick="Rate(3,'.$product[0]['id'].')" id="s3" class="fa fa-star-o cr"></i><i onclick="Rate(4,'.$product[0]['id'].')" id="s4" class="fa fa-star-o cr"></i><i onclick="Rate(5,'.$product[0]['id'].')" id="s5" class="fa fa-star-o cr"></i><a id="res_rate"></a></div></div>';//order section

		if (GetTableSet ('PhoneNumberType') ==1){$phone = GetTableSet ('DefPhoneNumber'); }
		if (!empty($phone) && strlen($phone) <= 11){
			$X =""; $len = GetTableSet ('ShowPhone');
			for ($i=1; $i <= strlen($phone)-$len ; $i++) { 
				$X = $X.'X';
			}
			echo '<div class="seller"><h4>البائع : '.$seller.'</h4><h4>التوصيل : خلال '.$product[0]['delivery'].' أيام</h4></div>';

			if (GetTableSet ('ShowPhoneNumber') ==1){
			echo '<div class="order"><a class="show-phone" id="phone-btn" onclick="ShowPhone('.$product[0]['id'].')"><i class="fa fa-phone"></i> '.mb_substr($phone, 0,$len).$X.'<b id="show-phone-res">إظهر رقم الموبايل<b></a></div><input type="hidden" id="vphone" value="'.$phone.'">';
			}

			echo '<div class="order"><a href="'.$Site_URL.'/cart.php?productid='.$product[0]['id'].'" class="addtocart"><i class="fa fa-cart-plus"></i> أضف الى السلة</a></div>';

		}
		echo '</div>';
		//---
		echo '</div>';
		//-----------------------------
		$tit = explode(' ', $product[0]['title']);
		if (count($tit) > 0){
			$q = ' WHERE ';
			$limit = 5;
			if ($limit > count($tit)) {$limit = count($tit);}
			for ($i=0; $i <= $limit-1 ; $i++) { 
				if ($i == $limit-1){
					$q = $q .' title LIKE "%'.$tit[$i].'%" ';
				}else{
					$q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
				}
			}
			
			$more = getAllFrom('*' , 'products' , $q , 'AND status = 1 AND id != "'.$product[0]['id'].'" ORDER BY rand() LIMIT '.GetTableSet ('MoreProduct'));
			if (count($more) > 0){
				echo '<div class="col-md-12"><br><h2 class="paid-title">المستخدمين شاهدوا أيضاً</h2>';//more product
				for ($i=0; $i <= count($more)-1 ; $i++) { 
					GetProduct($more[$i]['id']);
				}
				echo '</div>';
			}
		}
		//-----------------------------
		echo '</section><hr>';
	}else{
		header('Location: category.php');	exit();
	}
}
//--------------------------------------------------------
function SendNotiToPhone($orderid){
	
}