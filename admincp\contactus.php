<?php
ob_start();
$Title_page = 'رسائل اتصل بنا' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
UpdateTable('contactus' , 'readed' ,1 , 'WHERE id > 0 ' );
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
		$ch = getAllFrom('*' , 'contactus' , 'WHERE id = "'.$_GET['id'].'" ', '');
		if (count($ch) > 0 ){

				DeleteColum( 'contactus', 'WHERE id = '.$_GET['id'] );

				header('Location: contactus.php');	exit();
	}else{
			redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
		$ch = getAllFrom('*' , 'contactus' , 'WHERE id = "'.$_GET['id'].'" ', '');
		if (count($ch) > 0 ){
	?>
	<div class="row">
			<div class="col-md-12 col-sm-12">
				<div class="card">
					<div class="card-body text-center">

			     <h3>هل انت متأكد من انك تريد حذف هذه الرساله ؟</h3>
			     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>
			     <center>
			     	<a class="btn btn-danger btn-lg" href="contactus.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-lg" href="contactus.php">رجوع</a>
		     </center>

	</div>	
	</div>
</div>
</div>
	<?php
	}else{
			header('Location: contactus.php'); exit();
	} 
//---------------------------------------------------	
}else{
	?>
	<div class="row">
			<div class="col-md-12 col-sm-12">
				<div class="card">
					<div class="card-body">
				<?php
				$pages = isset($_GET["pages"]) ? (int)$_GET["pages"] : 1 ;
        		$per_page = 30 ;
        		$start = ($pages > 1) ? ($pages * $per_page) - $per_page : 0 ;	
				$sub = getAllFrom('*' , 'contactus' , '', 'ORDER BY id DESC LIMIT '.$start.' , '.$per_page.' ');
				$total = count(getAllFrom('id' , 'contactus' , '', ''));
        		$pagesx = ceil($total / $per_page);
					echo '<div class="divs">';
					if (count($sub) > 0 ){
							echo '<table class="table">
	                        <thead>
	                          <tr>
	                            <th>الإسم</th>
	                            <th>الرساله</th>
	                            <th>البريد الإلكترونى</th>
	                            <th>الاى بى</th>
	                            <th>التاريخ</th>
	                            <th>حذف</th>
	                          </tr>
	                        </thead>
	                        <tbody>';
	                        for ($x=0; $x <= count($sub)-1 ; $x++) { 

	                        echo '<tr>
		                            <th>'.$sub[$x]['name'].'</th>
		                            <th>'.$sub[$x]['mesg'].'</th>
		                            <th>'.$sub[$x]['email'].'</th>
		                            <th>'.$sub[$x]['ip'].'</th>
		                            <th>'.date('Y/m/d' ,$sub[$x]['datee']).'</th>
		                            <td><a href="contactus.php?do=del&id='.$sub[$x]['id'].'" class="btn btn-sm btn-danger smb">حذف</a></td>
	                          	</tr>';
                        }
                    echo '</tbody> </table>';
                    if ($pagesx > 1 ){ 
			                ?>  
		                <div class="col-md-12">
		                    <center>
		                    <ul class="pagination pagination-sm text-center">
		                      <?php 
	                         for ($pa=1; $pa <= $pagesx ; $pa++) {
		                         if ($pages == $pa ){ $cls = "active" ;}else{ $cls = "" ;} 
	                         ?>
	                          <li class="page-item <?php echo $cls ; ?>"><a class="page-link" href="contactus.php?pages=<?php echo $pa ; ?>"><?php echo $pa ; ?></a></li>
	                         <?php 
	                        }
	                      ?>
	                    </ul>
	                   </center>
	                  </div>
	                <?php
	                }	

					}else{
							echo  Show_Alert('info' , 'لا يوجد رسائل. ');
					}
					echo '</div>';
			?>
		</div>
	</div>
</div>
</div>

<?php
}
include('footer.php'); 
ob_end_flush();
?>