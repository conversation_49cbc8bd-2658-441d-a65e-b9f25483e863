<!DOCTYPE html>
<html lang="ar">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="description" content="<?php getDescr() ; ?>" />
<meta name="keywords" content="<?php getKeyWords() ; ?>" />
<meta name="author" content="<PERSON>" />

<meta property="og:url" content="<?php echo strip_fbclid($actual_link);?>" />
<meta property="og:type" content="website" />
<meta property="og:title" content="<?php getTitle() ?>">
<meta property="og:image" content="<?php getKImages() ?>" />
<meta property="og:image:alt" content="<?php getTitle() ?>" />
<meta property="og:image:type" content="image/PNG" />
<meta property="og:image:width" content="500" />
<meta property="og:image:height" content="500" />
<meta property="og:description" content="<?php getDescr() ; ?>">
<meta property="og:keywords" content="<?php getKeyWords() ; ?>">




<link rel="shortcut icon" href="<?php echo $Site_URL;?>/favicon.ico"> 
<title><?php getTitle() ?></title>
<meta name="robots" content="INDEX,FOLLOW" />
	


<!-- StyleSheets -->
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/bootstrap-rtl.min.css">
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>

<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/slick-theme.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/font-awesome.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/animate.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/jquery.desoslide.min.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/style.css">
<link rel="stylesheet" href="<?php echo $Site_URL;?>/layout/css/responsive.css">
<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
<!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->
</head>
	<body>

		<div class="top-header">
			<ul class="right-ul">
					<p>
						<a href="#">تسجيل الدخول</a> <span>|</span>
						<a href="#">تسجيل حساب جديد</a> 
					</p>
			</ul>
			<ul class="left-ul hideonmob">
					<li><a href="#"><i class="fa fa-facebook-square"></i></a></li>
					<li><a href="#"><i class="fa fa-twitter-square"></i></a></li>
					<li><a href="#"><i class="fa fa-whatsapp"></i></a></li>
					<li><a href="#"><i class="fa fa-youtube-square"></i></a></li>
					<li><a href="#"><i class="fa fa-instagram"></i></a></li>
			</ul>
		</div>

		<div class="top-nav-bar">
				<div class="col-md-2 col-sm-12 text-center">
						<div class="logo">
							<a href=""><img class="logo" src="http://www.lavoona.com/image/cache/catalog/new2-380x157.png"></a>
						</div>
				</div>
				<div class="col-md-8 col-sm-12">
					<div class="input-group inpg">
						<span class="input-group-addon" id="ddwon" ><span id="ddwonr" onclick="ShowAllCat()">كل الأقسام</span><i onclick="ShowAllCat()" class="fa fa-arrow-down"></i>
								<ul class="catul">
									<li onclick="setCatVal(this ,0)">كل الأقسام</li>
									<li onclick="setCatVal(this ,1)">تكييف شارب</li>
									<li onclick="setCatVal(this ,2)">تكييف كاريير</li>
									<li onclick="setCatVal(this ,3)">تكييف مخلط</li>
								</ul>
						</span>

						<input type="text" class="form-control" placeholder="البحث">
						<span class="input-group-addon sch"><i class="fa fa-search"></i></span>
					</div>
				</div>
				<div class="col-md-2 col-sm-12 hideonmob">
						<a class="head-links" href="#"><i class="fa fa-question-circle"></i></a>
						<a class="head-links" href="#"><i class="fa fa-commenting-o"></i></a>
						<a class="head-links" href="#"><i class="fa fa-envelope-o"></i></a>
				</div>
				<div class="col-md-12 padding0 margintop20 hideonmob">
						
						<div class="menu <?php if (isset($thisIsHomePage)){echo 'open' ;}?>">
								<p> <i class="fa fa-bars" aria-hidden="true"></i> القائمة</p>
								<ul class="catul-menu">
									<?php
										for($i=0; $i<=5; $i++){
									?>
										<li class="cat-menu-title"><a href="#">تكييف شارب</a><i class="fa fa-caret-left"></i>
												<ol class="sub-cat-menu">
														<img src="https://upload.wikimedia.org/wikipedia/commons/thumb/c/c8/Logo_of_the_Sharp_Corporation.svg/1280px-Logo_of_the_Sharp_Corporation.svg.png">
														<?php
																for($x=0; $x<=5; $x++){
														?>
															<div><a href="#" class="sub-menu-link">تجربة قسم فرعى</a></div>
														<?php
															}
														?>			
												</ol>
										</li>
									<?php
										}
									?>	
									
								</ul>
						</div>
						
						<div class="left-menu">
								<div class="menu-users">
										<div class="usbox">
											<a href="#"><i class="fa fa-user"></i><span>sss</span></a>		
										</div>
										<div class="usbox">
											<a href="#"><i class="fa fa-user-plus"></i><span>sss</span></a>
										</div>
								</div>
								<div class="cart-computer">
									<p> <span>0</span> منتجات <i class="fa fa-shopping-cart"></i></p>
								</div>
						</div>
						
				</div>

				<a class="showmobmenu" id="showmobmenu" onclick="ShowMobMenu()"><i class="fa fa-bars"></i></a>
				<a class="shopping-cart" href="#"><i class="fa fa-shopping-cart"></i> <span id="cart_count">0</span></a>
		</div>

		
		<div class="mobile-menu" id="mobile-menu">
				<div class="mobile-menu-top">
						<p>القائمة</p>
						<a onclick="CloseMobMenu()"><i class="fa fa-times"></i></a>					
				</div>
				<ul>
					<li class="cat-mob-menu-title"><i class="fa fa-plus-circle"></i> <a href="#">تكييف شارب</a></li>
						<?php
							for($i=0; $i<=5; $i++){
						?>
							<li class="cat-mob-menu-title"><i class="fa fa-plus-circle"></i><a href="#">تكييف شارب</a>
									<ol class="sub-cat-mob-menu">
											<?php
													for($x=0; $x<=5; $x++){
											?>
												<div><a href="#" class="sub-mob-menu-link">تجربة قسم فرعى</a></div>
											<?php
												}
											?>			
									</ol>
							</li>
						<?php
							}
						?>	
				</ul>
		</div>



	  <div class="min-content">
			
		
		</div>

		


		<script src="<?php echo $Site_URL;?>/layout/js/jquery-1.12.4.js"></script>
		<script src="<?php echo $Site_URL;?>/layout/js/bootstrap.min.js"></script>
		<!-- <script src="<?php echo $Site_URL;?>/layout/js/main.js"></script>
		<script src="<?php echo $Site_URL;?>/layout/js/timer.js"></script> -->
		<script src="https://kenwheeler.github.io/slick/slick/slick.js"></script>
		<script src="<?php echo $Site_URL;?>/layout/js/jquery.desoslide.min.js"></script>
		<script src="<?php echo $Site_URL;?>/layout/js/js.js"></script>
		<script>
			$(document).on("ready", function(e) {
			    	
			});
			// $(".cat-menu-title").on("mouseover", function () {
			// 		$('.sub-cat-menu').hide(100);
			// 		console.log(this);
			// 	  $(this).find('ol').show(200);
			// });
			
		  </script>
   </body>
</html>
