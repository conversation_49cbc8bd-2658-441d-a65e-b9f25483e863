<?php
ob_start();
$Title_page = 'الأوردرات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['paid'] >= 0){
			UpdateTable('orders' , 'paid' ,($ch[0]['paid']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['paid'] > 0){
			UpdateTable('orders' , 'paid' ,($ch[0]['paid']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('orders' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('orders' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'orders', 'WHERE id = '.$_GET['id'] );
			
			header('Location: orders.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
if (count($ch) > 0 ){
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body text-center">

			 <h3>هل انت متأكد من انك تريد الحذف ؟</h3>
			 <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>
			 <center>
				 <a class="btn btn-danger btn-lg" href="orders.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
				 <a class="btn btn-success btn-lg" href="orders.php">رجوع</a>
			 </center>

</div>	
</div>
</div>
</div>
<?php
}else{
		header('Location: orders.php'); exit();
} 
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['idقققق'])){
	$chx = getAllFrom('*' , 'orders' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($chx) > 0 ){
	$ch = getAllFrom('*' , 'orders_title' , 'WHERE id = "'.$chx[0]['titleid'].'" ', '');
	if (count($chx) > 0 ){
		?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['title'];?>"</h4>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
		                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
						$var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;

						if (empty($var1) || $var1 == "0"){
								echo  Show_Alert('danger' , 'يجب عليك تحديد السعر.');
						}elseif (empty($var2) || $var2 == "0"){
								echo  Show_Alert('danger' , 'يجب عليك تحديد مدة التوصيل.');
						}else{

								$stmt = $db->prepare("UPDATE orders
							    SET price = :var1  ,
							    	delivery = :var2  ,
							    	status = :var3  
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
						            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var3' => $chx[0]['status'] ,
					            'var0' => $chx[0]['id']
					          )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح.');
					        echo '</div>';  		 	
				        	redirect_home ('back' , 1); exit();

				    	}
	                 }
	                 ?>
					<form method="post">

	               	 	<div class="col-md-6">
		                    <div class="form-group">
												<label class="form-control-label">السعر <b id="com" style="display: none;" >(عمولة الموقع 0 جنيه عند تنفيذ الأوردر)</b></label>
		                      <input type="number" id="p5" name="var1" onchange="GetCom()" onkeyup="GetCom()" value="<?php echo $chx[0]['price'];?>" class="form-control">
		                    </div>
	               	 	</div>

		                <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">مدة التوصيل</label>
		                      <input type="number" name="var2" value="<?php echo $chx[0]['delivery'];?>" class="form-control">
		                    </div>
	               	 	</div>
		                <input type="hidden" name="var0" value="<?php echo $chx[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>

				</div>
			</div>
		</div>
	</div>			
	<?php	
	}else{
			header('Location: orders.php'); exit();
	} 
}else{
	header('Location: orders.php'); exit();
} 
//---------------------------------------------------	
}else{
?>
<style>.table .table {
    background-color: #24262d;
}</style>
<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all"># رقم</th>
								<th class="all"> اسم العميل</th>
								<th class="all"> تاريخ الطلب</th>
								<th class="mobile-p desktop"> رقم الجوال </th>
								<th class="none"> رقم الطلب</th>
								<th class="none"> المدينه </th>
								<th class="none"> العنوان </th>
								<th class="none"> الكوبون </th>
								<th class="none"> التحويل </th>
								<th class="mobile-p desktop"> الإجمالي </th>
								<th class="mobile-p desktop"> الشحن </th>
								<th class="none"></th>
								<th class="none"></th>
								<th class="none"></th>
								<th class="mobile-p desktop"> الحالة</th>
								<th class="mobile-p desktop"> تغير الحالة</th>
								<th class="none"> حذف </th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>



<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'orders'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [20, 50, 100, 200, 500, 1000, -1],
                  [20, 50, 100, 200, 500, 1000, "كل  الأوردرات"]
                ],
                'destroy': true,
                responsive:true,
                "order": [[0, "desc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن أوردر",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
		});
		
		function ChangeOrderStatus(orderid , st){
			if (st == 0 || st == 1 || st == 2){
        Swal({
        title: 'هل انت متأكد',
        text: "",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم',
        cancelButtonText: 'لا',
      }).then((result) => {
        if (result.value) {
					$('#datatables').addClass("dis");
					var lasthtml = $('#prod'+orderid).html();
					$('#prod'+orderid).html(lasthtml+' <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>');
					$.post("ajax.php", { action : 'ChangeOrderStatus' , orderid : orderid , st : st} ,function(data){ 
						GetData();
						Swal({
              title: 'تم بنجاح',
              text: "",
              type: 'success',
              confirmButtonColor: '#3085d6',
              confirmButtonText: 'إغلاق',
            })
						$('#datatables').removeClass("dis");
					});
        }
      })
		}else if (st == 2){
			Swal.fire({
				title: 'أكتب سبب إلغاء الأوردر !',
				input: 'text',
				inputAttributes: {
					autocapitalize: 'off'
				},
				customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'تأكيد الإلغاء',
        cancelButtonText: 'رجوع',
			}).then((res) => {
					if (res.value == '' || res.value == null){
						Swal.fire({
							title: 'يجب كتابة سبب الإلغاء',
							type: 'error',
							text: "",
							showCancelButton: false,
							confirmButtonColor: '#3085d6',
							confirmButtonText: 'موافق',
						});
					}else{
						$('#datatables').addClass("dis");
						var lasthtml = $('#prod'+orderid).html();
						$('#prod'+orderid).html(lasthtml+' <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>');
						$.post("ajax.php", { action : 'ChangeOrderStatus' , orderid : orderid , st : st ,res:res.value } ,function(data){ 
							GetData();
							Swal({
								title: 'تم بنجاح',
								text: "",
								type: 'success',
								confirmButtonColor: '#3085d6',
								confirmButtonText: 'إغلاق',
							})
							$('#datatables').removeClass("dis");
						});
					}
				},
			)
		}else{
			Swal({
				title: 'خطأ',
				type: 'error',
        text: "",
        customClass: 'animated tada',
        showCancelButton: false,
        confirmButtonColor: '#3085d6',
        confirmButtonText: 'موافق',
      });
		}
	}
</script>
