<?php
ob_start();
$Title_page = 'الأقسام';
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php'); 

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 0){
			UpdateTable('category' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('category' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] >= 0){
			UpdateTable('category' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['orders'] > 0){
			UpdateTable('category' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'category', 'WHERE id = '.$_GET['id'] );
			DeleteColum( 'category', 'WHERE parent = '.$_GET['id'] );
			DeleteColum( 'products_title', 'WHERE catid = '.$_GET['id'] );
			
			
			header('Location: category.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
			     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['name'];?> " </b> ؟</h3>
			     <p>برجاء العلم انه سيتم حذف القسم ولا يمكن الرجوع فيه.</p>
			     <p>برجاء العلم  ايضا انه سيتم حذف الاقسام الفرعية و المنتجات فى هذا القسم.</p>

			     <center>
			     	<a class="btn btn-danger btn-md" href="category.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
			     	<a class="btn btn-success btn-md" href="category.php">رجوع</a>
			     </center>
		 
			</div>	
		</div>
	</div>
</div>
<?php
	}else{
		header('Location: category.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel">التعديل على <i class="fa fa-hand-o-left"></i><b> <?php echo $ch[0]['name'];?></b> </h4>
				</div>
			</div>
		</div>			
	</div>		
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">		
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;
						$var3  = filter_var($_POST['var3']   , FILTER_SANITIZE_STRING) ;
						$var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;
						$cc = getAllFrom('*' , 'category' , 'WHERE name = "'.$var1.'" AND id != "'.$ch[0]['id'].'"  ', '');
						if (count($cc) > 0){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'هذا القسم موجود من قبل. ');
					        echo '</div>'; 
						}else{

							$filters = ',';
                            if (isset($_POST['check_list'])){
    							foreach($_POST['check_list'] as $check) {
    						        $filters = $filters.$check.',' ;
    						    }
    						} 

							$stmt = $db->prepare("UPDATE category
							    SET name = :var1  ,
							    	parent = :var2,
							    	title = :var5,
							    	filters = :var6,
							    	descr = :var4  
							    	WHERE  id = :var0 ");  
					           $stmt->execute(array(
					            'var1' => $var1 ,
					            'var2' => $var2 ,
					            'var4' => $var3 ,
					            'var5' => $var5 ,
					            'var6' => $filters ,
					            'var0' => $_GET['id']
					          )); 	
					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
					        echo '</div>';  		 	

					        redirect_home ('back' , 1); exit();
					    }
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الإسم</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['name'];?>" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم فى التايتل</label>
		                      <input type="text" name="var5" value="<?php echo $ch[0]['title'];?>" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">النوع</label>
		                      <select class="form-control" name="var2">
		                      	<option value="0" <?php if ($ch[0]['parent']== 0){echo 'selected' ;} ?>>قسم أساسي</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'category' , 'WHERE id != "'.$ch[0]['id'].'"  ORDER BY id DESC', '');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      	 		if ($ch[0]['parent']== $cc[$i]['id']){ $sel = 'selected' ; }else{ $sel = '' ;}
		                      			echo '<option value="'.$cc[$i]['id'].'" '.$sel.'>قسم فرعى فى '.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>
						<div class="col-md-12">
							<hr>
	               	 	    <label class="form-control-label">الفلاتر</label>
		                    <br>
		                    <div class="form-group">
		                        <?php 
		                        $ch1 = getAllFrom('*' , 'filter' , 'ORDER BY id DESC', '');	
                                    if (count($ch1) > 0){
                                        for ($x=0; $x <= count($ch1)-1 ; $x++) {
                                            if (strpos($ch[0]['filters'],','.$ch1[$x]['id'].',')!== false){
                                                $chk = 'checked';
                                            }else{
                                                $chk = '';
                                            }
                                            echo '<div class="col-md-3"><input type="checkbox" name="check_list[]" value="'.$ch1[$x]['id'].'" '.$chk.'> '.$ch1[$x]['name'].'</div>';
                                        }
                                    }
		                        ?>
		                    </div><br><br>
	               	 	</div>
						<div class="col-md-12">
							<div class="form-group">
								<label class="form-control-label">وصف شامل للقسم</label>
								<textarea style="min-height: 100px;" class="form-control" name="var3"><?php echo $ch[0]['descr'];?></textarea>
							</div>
						</div>
	               	 	
		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                      <a href="category.php" class="btn btn-lg btn-primary">رجوع</a>
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>


				<?php
					if ($ch[0]['parent'] == 0){
						echo '<div class="divs"><br><hr>';
						echo '<h4><i class="fa fa-picture-o" aria-hidden="true"></i> صورة القسم</h4><p>450*400</p>';
						$ph = GetTableSet ('DefaultImage') ;
						if (!empty($ch[0]['photo'])){
							$ph = $ch[0]['photo'];
						}
						?>
						<center>

					    <img style="margin: 20px 0px; width: 300px; max-width: 100%;" src="<?php echo $Site_URL.'/'.$ph ;?>">

					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >

					         

					          <input type="file" name="photo" id="photo" required style="display: none;" />

					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />

					          <input type="hidden" name="Image_For" value="Category">
					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">

					        </form>

					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>

					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>

					  </center>

						<?php
						echo '</div>';
					}
				?>

			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: category.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة قسم جديد</h4>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = trim(filter_var($_POST['var1']   , FILTER_SANITIZE_STRING)) ;
						$var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;
						$var3  = filter_var($_POST['var3']   , FILTER_SANITIZE_STRING) ;

						$cc = getAllFrom('*' , 'category' , 'WHERE name = "'.$var1.'" ', '');

						if (count($cc) > 0){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'هذا القسم موجود من قبل. ');
					        echo '</div>'; 
						}else{

							$filters = ',';
                            if (isset($_POST['check_list'])){
    							foreach($_POST['check_list'] as $check) {
    						        $filters = $filters.$check.',' ;
    						    }
    						}


							$stmt = $db->prepare("INSERT INTO category ( name , link , parent , datetime , descr , title , filters ) 
							 VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4  ,:user_5 ,:user_6 ,:user_7 )");  
							$stmt->execute(array(
					          'user_1' => $var1 , 'user_2' => str_replace(' ', '-', $var1)  , 'user_3' => $var2 , 'user_4' => time()  , 'user_5' => $var3 , 'user_6' => $var1 , 'user_7' => $filters   )) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة قسم جديد بنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الاقسام خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=category.php");
					        exit();
						}

						
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الإسم</label>
		                      <input type="text" name="var1" value="قسم جديد" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">النوع</label>
		                      <select class="form-control" name="var2">
		                      	<option value="0">قسم أساسي</option>
		                      	<?php
		                      	 $cc = getAllFrom('*' , 'category' , 'WHERE id != "'.$ch[0]['id'].'"  ORDER BY id DESC', '');
		                      	 if (count($cc) > 0 ){
		                      	 	for ($i=0; $i <= count($cc)-1 ; $i++) { 
		                      			echo '<option value="'.$cc[$i]['id'].'">قسم فرعى فى '.$cc[$i]['name'].'</option>';
		                      	 	}
		                      	 }
		                      	?>
		                      </select>
		                    </div>
						</div>

						<div class="col-md-12">
							<hr>
	               	 	    <label class="form-control-label">الفلاتر</label>
		                    <br>
		                    <div class="form-group">
		                        <?php 
		                        $ch1 = getAllFrom('*' , 'filter' , 'ORDER BY id DESC', '');	
                                    if (count($ch1) > 0){
                                        for ($x=0; $x <= count($ch1)-1 ; $x++) {
											$chk = '';
                                            echo '<div class="col-md-3"><input type="checkbox" name="check_list[]" value="'.$ch1[$x]['id'].'" '.$chk.'> '.$ch1[$x]['name'].'</div>';
                                        }
                                    }
		                        ?>
		                    </div><br><br>
	               	 	</div>
						
						<div class="col-md-12">
							<div class="form-group">
								<label class="form-control-label">وصف شامل للقسم</label>
								<textarea style="min-height: 100px;" class="form-control" name="var3"></textarea>
							</div>
						</div>
	               	 	
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="category.php?do=add_new" class="btn btn-lg btn-success">إضافة قسم جديد</a>
			</div>
		</div>
	</div>			
</div>


	<?php
	$check = getAllFrom('*' , 'category' , 'WHERE parent = 0', 'ORDER BY orders DESC ,id DESC');
	if(count($check) > 0){
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if ($check[$i]['status'] == 0 ){
				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
			}else{
				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
			}
			echo '<div class="row"><div class="col-md-12 col-sm-12"><div class="card"><hr>
			<div class="card-body">';
			echo '<div class="divs">';
			echo '<h4 class="cattitlel">'.$check[$i]['orders'].' - '.$check[$i]['name'].'</h4>';
			echo ' <a href="category.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary">تعديل</a> ';
			echo ' <a href="category.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
			echo ' <a href="category.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '<a href="category.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
                        	<a href="category.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>';
			echo '</div>';
			$sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$check[$i]['id'].'" ', 'ORDER BY orders DESC , id DESC');
			echo '<div class="divs">';
			if (count($sub) > 0 ){
				echo '<table class="table">
                <thead>
                  <tr>
                    <th>الأسم</th>
                    <th>الترتيب</th>
                    <th>تعديل</th>
                    <th>إخفاء</th>
                    <th>حذف</th>
                  </tr>
                </thead>
                <tbody>';
                for ($x=0; $x <= count($sub)-1 ; $x++) { 

					$xsub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$sub[$x]['id'].'" ', 'ORDER BY orders DESC , id DESC');
					 

                    if ($sub[$x]['status'] == 0 ){
						$titx = 'مخفى' ; $clsx = 'btn btn-sm btn-warning smb' ;
					}else{
						$titx = 'معروض' ; $clsx = 'btn btn-sm btn-success smb' ;	
					}	
                echo '<tr>
                        <th>'.$sub[$x]['orders'].' - '.$sub[$x]['name'].'</th>
                        <td>
                        	<a href="category.php?do=moveup&id='.$sub[$x]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
                        	<a href="category.php?do=movedn&id='.$sub[$x]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>  
                        </td>
                        <td><a href="category.php?do=edit&id='.$sub[$x]['id'].'" class="btn btn-sm btn-primary smb">تعديل</a></td>
                        <td><a href="category.php?do=show_hide&id='.$sub[$x]['id'].'" class="'.$clsx.'">'.$titx.'</a></td>
                        <td><a href="category.php?do=del&id='.$sub[$x]['id'].'" class="btn btn-sm btn-danger smb">حذف</a></td>
                  	</tr>';


					for($z=0; $z<=count($xsub)-1; $z++){
						if ($xsub[$z]['status'] == 0 ){
							$titx = 'مخفى' ; $clsx = 'btn btn-sm btn-warning smb' ;
						}else{
							$titx = 'معروض' ; $clsx = 'btn btn-sm btn-success smb' ;	
						}	
					echo '<tr>
							<th> -> '.$xsub[$z]['orders'].' - '.$xsub[$z]['name'].'</th>
							<td>
								<a href="category.php?do=moveup&id='.$xsub[$z]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-up"></i></a>  
								<a href="category.php?do=movedn&id='.$xsub[$z]['id'].'" class="btn btn-sm btn-primary smb"><i class="fa fa-arrow-down"></i></a>  
							</td>
							<td><a href="category.php?do=edit&id='.$xsub[$z]['id'].'" class="btn btn-sm btn-primary smb">تعديل</a></td>
							<td><a href="category.php?do=show_hide&id='.$xsub[$z]['id'].'" class="'.$clsx.'">'.$titx.'</a></td>
							<td><a href="category.php?do=del&id='.$xsub[$z]['id'].'" class="btn btn-sm btn-danger smb">حذف</a></td>
						</tr>';
					}

                }

            echo '</tbody> </table>';
			}else{
				echo  Show_Alert('warning' , 'لا يوجد أقسام فرعية. ');
			}
			echo '</div></div></div></div></div>';
			
		}
	}else{
		echo  '<br>'.Show_Alert('warning' , 'لا يوجد أقسام. ');
	}	

}
?>
<?php
include('footer.php'); 
ob_end_flush();
?>