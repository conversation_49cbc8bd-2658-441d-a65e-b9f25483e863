<?php
ob_start();
$Title_page = 'خريطة الموقع' ;
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');
?>
<style>
    .sitemap{
        width: 100%;
        display: inline-block;
        margin-top: 20px;
        font-family: 'GE SS','Open Sans', Tahoma, sans-serif;
    }
    .ms1{
        margin-bottom: 15px !important;  
        padding-right:30px;  
    }
    .ms1 li{
        list-style: none;
        margin-bottom: 10px;
        padding: 2px;
    }
    .ms1 i{
        font-style: normal;
        margin-left: 5px;
    }
    .ms2{
        margin-bottom: 15px !important;
        padding-right:30px;  
    }
    .ms2 li{
        list-style: none;
        margin-bottom: 10px;
        padding: 2px;
    }
    .ms2 i{
        font-style: normal;
        margin-left: 5px;
    }
    .ms3{
        margin-bottom: 15px !important;
        padding-right:30px;  
    }
    .ms3 li{
        list-style: none;
        margin-bottom: 10px; 
        padding: 2px;   
    }
    .ms3 i{
        font-style: normal;
        margin-left: 5px;    
    }
</style>
<?php
echo '<div class="container-fluid products-content">';


    echo '<ul class="ms1">';
        echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/">الرئيسية</a></li>';

        if(count($category) > 0){
            
            for ($i=0; $i <= count($category)-1 ; $i++) { 
                echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/category/'.$category[$i]['link'].'">'.$category[$i]['name'].'</a>';
                    $sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
                    if (count($sub) > 0){
                        echo '<ul class="ms2">';
                        
                        for ($S=0; $S <= count($sub)-1 ; $S++) { 
                            echo '<li><i class="fa fa-hand-o-left" aria-hidden="true"></i><a href="'.$Site_URL.'/category/'.$sub[$S]['link'].'">'.$sub[$S]['name'].'</a>';
                            $productsz = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$sub[$S]['id'].'"', ' ORDER BY id DESC');	
                            if (count($productsz) > 0){
                                echo '<ul class="ms3">';
                                for ($pf=0; $pf <= count($productsz)-1 ; $pf++) { 
                                    echo '<li><i class="fa fa-pencil-square-o" aria-hidden="true"></i><a href="'.$Site_URL.'/'.$productsz[$pf]['link'].'" title="'.$productsz[$pf]['title'].'">'.$productsz[$pf]['title'].'</a></li>';
                                }
                                echo '</ul>';
                            }
                            
                            echo '</li>';
                        }    
                        echo '</ul>';
                    }

                echo '</li>';
            }
            
        }

        echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/login.php">تسجيل الدخول</a></li>';
        echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/register.php">تسجيل حساب جديد</a></li>';
        echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/about.php">عن '.$Site_Name.'</a></li>';
        echo '<li><i class="fa fa-arrow-left" aria-hidden="true"></i><a href="'.$Site_URL.'/privacy.php">سياسة الخصوصية</a></li>';

    echo '</ul>';


echo ' </div>';
include('footer.php'); 
ob_end_flush();
?>

