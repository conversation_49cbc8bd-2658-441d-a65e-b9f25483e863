<?php
ob_start();
include('../webset.php');
include('../session.php');
?>
<!DOCTYPE html>
<html lang="en" dir="rtl">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <meta name="description" content="<?php echo $Description ; ?>" />
        <meta name="keywords" content="<?php echo $Keywords ; ?>" />
        <meta name="author" content="<PERSON>" />
        <link rel="shortcut icon" href="favicon.ico"> 
        <title>تسجيل الدخول - <?php getTitle() ?></title>

        <!-- Bootstrap -->
        <link href="layout/bootstrap-rtl-master/dist/css/bootstrap-rtl.min.css" rel="stylesheet">
        <link href="layout/css/waves.min.css" type="text/css" rel="stylesheet"><link rel="stylesheet" href="css/nanoscroller.css">
        <!--        <link rel="stylesheet" href="css/nanoscroller.css">-->
        <link href="layout/css/style.css" type="text/css" rel="stylesheet">
        <link href="layout/font-awesome/css/font-awesome.min.css" rel="stylesheet">
        <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
          <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
          <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
        <![endif]-->
    </head>
    <body class="account">
        <div class="container">
            <div class="row">
                <div class="account-col text-center">
                    <h1><?php getTitle() ?></h1>
                    <h3>تسجيل الدخول الى لوحة التحكم</h3><br>
                    <form class="m-t" role="form" method="post">
                         <div class="form-group">
                            <input type="phone" class="form-control ltr" name="log_var1" placeholder="رقم الموبايل" required="">
                        </div>
                        <div class="form-group">
                            <input type="password" class="form-control ltr" name="log_var2" placeholder="كلمة المرور" required="">
                        </div>
                        <button type="submit" name="login" class="btn btn-primary btn-block ">دخول</button>
                        <?php
                        if (isset($_POST['login'])){
                            $log_var1 = filter_var($_POST['log_var1']  , FILTER_SANITIZE_STRING) ;
                            $log_var2 = filter_var($_POST['log_var2']  , FILTER_SANITIZE_STRING) ;
                            $check = getAllFrom('*' , 'users' , 'WHERE phone = "'.$log_var1.'" AND password = "'.$log_var2.'" ', '');
                            if (count($check) > 0){
                                $stmt = $db->prepare("UPDATE users SET ip = :var1 WHERE  id = :var0 ");  
                                $stmt->execute(array('var1' => get_client_ip() , 'var0' => $check[0]['id'] )); 
                                $_SESSION['userData'] = $check[0]['id'];
                                echo  Show_Alert('info' , 'تم تسجيل الدخول بنجاح . سيتم تحويلك خلال 3 ثواني. ');
                                if ($check[0]['type'] == 2){
                                    header('Location: '.$Site_URL.'/admincp/'); exit();
                                }elseif($check[0]['type'] == 1){
                                    header('Location: '.$Site_URL.'/merchant/');    exit();
                                }else{
                                    header('Location: '.$Site_URL.'/'); exit();
                                }
                            }else{
                                echo Show_Alert('danger' , 'رقم الموبايل او كلمة المرور غير صحيحة');
                            }
                        }

                        ?>
                    </form>
                <p>جميع الحقوق محفوظه &copy; <?php getTitle() ?></p>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="layout/js/jquery.min.js"></script>
        <script type="text/javascript" src="layout/bootstrap-rtl-master/dist/js/bootstrap-rtl.min.js"></script>
        <script src="layout/js/pace.min.js"></script>
    </body>
</html>
