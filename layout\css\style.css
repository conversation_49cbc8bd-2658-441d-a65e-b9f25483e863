.dis {pointer-events:none; opacity: 0.7;}
/*@import url('https://fonts.googleapis.com/css?family=Exo:400,700');*/
a {
  color: #5d2a8c;
  text-decoration: none !important;
  cursor: pointer;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  text-decoration: none !important;
}
a:focus, a:hover {
  color: #bd2637;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  text-decoration: none !important;
}
html {
  line-height: 1.2;
}

body { 
  overflow-x: hidden;
  font-family: 'Cairo', sans-serif;
  font-size: 12px;
  line-height: 25px;
  background:#f6f6f6;
}

::-webkit-scrollbar {
  width: 0.2em;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
}

::-webkit-scrollbar-thumb {
  background-color: #C5C7C9;
  outline: 1px solid #C5C7C9;
}
::selection {
  background: #ddd; /* WebKit/Blink Browsers */
}
::-moz-selection {
  background: #ddd; /* Gecko Browsers */
}
*{
  outline: none !important;
}
img{
  max-width: 100%;
}
.padding0 , .nopadding{
  padding: 0;
}
.padding5{
  padding: 5px;
}
.margintop10{
  margin-top: 10px;
}
.margintop20{
  margin-top: 0px;
}
.padding_r0{
  padding-right: 0;
}

.top-header{
  width: 100%;
  display: inline-block;
  background: #010206;
  padding: 5px;
}

.icons-p{
    text-align: center;
    background-color: #fdf9f9;
    padding: 2px;
    margin: 2px;
    border: 1px solid #dfdede;
    border-radius: 6px;
    font-size: 13px;
    color: #102c32;
}

.icons-p img {
    width: 50px;
    margin: 2px;
    padding: 2px;
}

.pord-info {
    color: #f00;

}

.chat-p {
    display: block;
    font-size: 13px;
    padding: 3px;
    background-color: #6d9d42;
    color: #fff;
    border-radius: 9px;
    margin: 4px;
    text-align: center;
}

.chat-p img {
    width: 30px;
}

.chat-p-nostok {
    display: block;
    font-size: 13px;
    padding: 3px;
    background-color: #898888;
    color: #fff;
    border-radius: 12px;
    margin: 3px;
    text-align: center;
}

.chat-p-nostok img {
    width: 30px;
}


.right-ul{
  display: inline-block;
  margin: 0px 20px;
  padding: 0;
}
.right-ul p{
  color: #f1f1f1;
  margin: 0;
  font-size: 14px;
}
.right-ul a ,.right-ul a:hover , .right-ul a:active , .right-ul a:focus {
  color: #f1f1f1;
  font-size: 12px;
  margin-left: 0px 5px;
}
.right-ul span{
  margin: 0px 5px;
  color: #24303a;
}
.left-ul{
  float: left;
  display: inline-block;
  margin: 0px 20px;
  padding: 0;
}
.left-ul li{
  display: inline-block;
  margin: 0px 5px;
  font-size: 18px;
}
.left-ul  a{
  color: #f1f1f1;
}
.left-ul  a:hover , .left-ul  a:focus , .left-ul  a:active {
  color: #fff;
}

.min-content{
  min-height: 500px;
}

.top-nav-bar{
  width: 100%;
  display: table;
  background: #fff;
  padding-top: 0px;
  min-height: 50px;
  position: relative;
}
.logo{
  width: 100%;
  display: inline-block;
  position: relative;
}
.logo img{
  width: 110px;
  margin: 9px;
}
.inpg{
  margin-top: 14px;
}
#ddwon{
  cursor: pointer;
  position: relative;
  background: #fff;
  color: #0976cd;
  font-size: 12px;
}
#ddwon:hover , #ddwon:active , #ddwon:focus {
  background: #ffa800;
  border-color: #ffa800;
  color: #fff;
}
.sch{
  cursor: pointer;
  position: relative;
  background: #fff;
  color: #0976cd;
  border-left-color: transparent !important;
}
#ddwon i{
  font-size: 10px;
  margin-right: 10px;
}
.catul {
  position: absolute;
  background: #fff;
  top: 45px;
  right: 10px;
  min-width: 170px;
  padding: 0;
  border-radius: 3px;
  border: 1px solid #ddd;
  display: none;
  z-index: 10;
}
.catul::before {
  content: "";
  width: 0;
  height: 0;
  border: 8px solid #ffffff00;
  pointer-events: none;
  display: block;
  border-bottom-color: rgb(255, 255, 255);
  position: absolute;
  margin-top: -16px;
  right: 10px;
  left: auto;
  transform: translateX(0);
}
.catul li{
  width: 100%;
  padding: 10px 10px;
  list-style: none;
  text-align: right;
  border-bottom: 1px solid #ddd;
  color: #3A4750;
}
.catul li:last-child{
  border-bottom: 0;
}
.catul li:hover ,.catul li:focus , .catul li:active {
  background: #f1f1f1;
}

.head-links{
  margin-top: 30px;
  font-size: 22px;
  display: inline-block;
  margin-right: 5px;
  margin-left: 5px;
  padding: 4px 10px;
  background: #fff;
  border-radius: 3px;
  color: #5d2a8c;
}
.head-links:hover ,.head-links:focus , .head-links:active {
  background: #e73d50;
  color: #fff2f2;
}


.menu {
    display: inline-block;
    background: #e5bd75;
    padding: 10px;
    color: #fff;
    cursor: pointer;
    border-radius: 10px;
    margin: 9px;
}
.menu i {
  margin-left: 5px;
  font-size: 14px;
  border-radius: 50%;
  border: 2px solid #fff;
  width: 25px;
  height: 25px;
  text-align: center;
  vertical-align: middle;
  padding: 3px;
  margin-right: 5px;
}
.menu p {
  margin: 0;
  font-size: 16px;
}


.oldprice-p {
    text-decoration: line-through;
    color: #68686c;
    font-size: 12px;
    font-weight: 600;
    padding: 0px;
    margin: 0px -13px 0px 0px;
}


.left-menu{
  float: left;
  display: inline-block;
  margin: 10px;
}
.menu-users{
  display: inline-block;
  margin-left: 30px;
}
.usbox{
  display: inline-block;
}

.menu-users a {
  font-size: 18px;
  padding: 3px;
  /* width: 15px; */
  height: 30px;
  /* border: 1px solid #fff; */
  display: inline-block;
  border-radius: 50%;
  text-align: center;
  /* background: #f1f1f1; */
  color: #f1f1f1;
  margin: 0px 5px;
}
.menu-users a:hover , .menu-users a:active , .menu-users a:focus , .menu-users span:hover , .menu-users span:active , .menu-users span:focus  {
  color: #F9AE00;
}
.menu-users span {
  display: inline-block;
  color: #f1f1f1;
  margin-right: 5px;
  font-size: 12px;
}



.cart-computer{
  display: inline-block;
  background: #e5bd75;
  padding: 10px;
  border-radius: 15px;
  color: #fff;
  cursor: pointer;
}
.cart-computer i {
  margin-left: 5px;
  font-size: 18px;
  text-align: center;
  vertical-align: middle;
  padding: 3px;
  margin-right: 5px;
}
.cart-computer p {
  margin: 0;
  font-size: 16px;
  text-align: center;
  color: #fff !important;
}




.product-info {
  margin: 0px;
  padding: 13px;
  background: #fff;
  margin-top: 5px;
}

.product-info-wrp {
    margin-left: 18px;
    margin-right: 0;
    display: inline-block;
}


.product-info .label {
   color: #8b8b8b;
    font: 12px/21px arial,sans-serif;
    padding: 0.2em 0 0.3em;
    font-weight: 700;
    font-family: 'Cairo';
}

.product-info .label  img {
  width: 20px;
}

.product-info .value {
   color: #2b617e;
    font-size: 12px;
    font-weight: 700;
}



.catul-menu {
  position: absolute;
  background: #fff;
  top: 55px;
  right: 25px;
  min-width: 170px;
  padding: 0;
  border-radius: 3px;
  border: 1px solid #ddd;
  z-index: 100;
  width: 195px;
  display: none;
}
.catul-menu::before {
  content: "";
  width: 0;
  height: 0;
  border: 8px solid #8e8e8e00;
  pointer-events: none;
  display: block;
  border-bottom-color: #ddd;
  position: absolute;
  margin-top: -16px;
  right: 10px;
  left: auto;
  transform: translateX(0);
}
.catul-menu li{
  width: 100%;
  padding: 5px 10px;
  list-style: none;
  text-align: right;
  border-bottom: 1px solid #f9f9f9;
  color: #3A4750;
}
.catul-menu li:last-child{
  border-bottom: 0;
}
.catul-menu li:hover ,.catul-menu li:focus , .catul-menu li:active {
  background: #fcfcfc;
}
.catul-menu li i{
  margin: 0;
  border: 0;
  float: left;
  margin-left: -5px;
}
.cat-menu-title a{
  color: #1f3748;
  width: 85%;
  display: inline-block;
  font-size: 16px;
}
.sub-cat-menu{
  position: absolute;
  background: #fff;
  top: -1px;
  right: 193px;
  min-width: 200px;
  padding: 0;
  border-radius: 3px;
  border: 1px solid #ddd;
  z-index: 100;
  width: 195px;
  min-height: 100px;
  text-align: center;
  padding: 10px;
  display: none;
}
.sub-cat-menu img{
  width: 150px;
  margin-bottom: 10px;
  display: inline-block;
}
.sub-menu-link{
  margin-top: 5px;
  background: #d1a960;
  display: inline-block;
  padding: 3px 10px;
  color: #fff !important;
  border-radius: 4px;
  width: 100%;
}

.open .catul-menu{
  display: block;
}

.sub-brands{
  display: inline-flex;
    margin: 1px;
    padding: 2px;
    
}

.sub-brands img {
 width: 222px;
 padding: 1px;
 border-radius: 17px;
}
.showmobmenu , .showmobmenu:hover , .showmobmenu:active , .showmobmenu:focus{
  position: absolute;
  right: 20px;
  top: 22px;
  font-size: 24px;
  color: #000;
  cursor: pointer;
}
.shopping-cart , .shopping-cart:hover , .shopping-cart:active , .shopping-cart:focus{
  position: absolute;
  left: 20px;
  top: 22px;
  font-size: 24px;
  color: #000;
  cursor: pointer;
}

.shopping-cart span{
  font-size: 10px;
  position: absolute;
  top: -8px;
  left: 5px;
  color: #fff;
  background: #e73d50;
  padding: 0px;
  text-align: center;
  display: inline-block;
  border-radius: 50%;
  width: 17px;
  font-weight: bold;
  height: 17px;
  line-height: 17px;
  display: none;
}

.mobile-menu{
  position: fixed;
  width: 300px;
  height: 100%;
  background: #fff;
  top: 0;
  z-index: 1000;
  background: rgba(255,255,255,1);
  background-image: linear-gradient(to bottom,#fff,#dee6f9);
  box-shadow: -20px 20px 0px 80px rgba(0, 0, 0, 0.46);
  overflow: auto;
  display: none;
}
.mobile-menu-top{
    width: 100%;
    display: inline-block;
    background: #182942;
    padding: 10px;
    position: relative;
}
.mobile-menu-top p{
  font-size: 18px;
  color: #fff;
  margin: 0;
}
.mobile-menu-top a , .mobile-menu-top a:active , .mobile-menu-top a:hover , .mobile-menu-top a:focus{
  color: #fff;
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 20px;
  cursor: pointer;
}


.cat-mob-menu-title a{
  color: #3A4750;
font-size: 18px;
}
.sub-cat-mob-menu{
  display: none;
}
.sub-cat-mob-menu img{
  width: 150px;
  margin-bottom: 10px;
  display: inline-block;
}
.sub-mob-menu-link{
  margin-top: 5px;
  display: inline-block;
  color: #24303a;
}
.mobile-menu ul{
  padding: 0;
  margin: 15px;
}
.cat-mob-menu-title{
    list-style: none;
    padding: 12px;
    font-size: 14px;
    margin-bottom: 10px;
    border-bottom: 1px solid #dbdbdb;
}
.cat-mob-menu-title i{
    margin-left: 10px;
    font-size: 20px;
    color: #ed7017;
}
.showmobmenu , .shopping-cart{
  display: none;
}

/* ============== */
.min-content{
  position: relative;
  width: 100%;
  display: inline-block;
  padding-bottom: 20px;
}
.static-side {
  position: absolute;
  width: 230px;
  right: 0;
  top: 0px;
}
.index-page{
  width: 100%;
  display: inline-block;
  padding: 10px;
  padding-right: 272px;
  min-height: 300px;
}
.index-box{
  width: 100%;
  display: grid;

}
.index-box img{
  width: 100%;
border-radius: 16px;
}
.index-box p {
    margin: 0;
    color: #f5f5f5;
    line-height: 39px;
    font-size: 14px;
    height: 41px;
    overflow: hidden;
    background: #2c2528b3;
    text-align: center;
    margin-top: -41px;
    border-radius: 0px 0px 16px 16px;
}

.index-box a{
 color: #fff;
 font-weight: 600;
 font-size: 16px;
}
.product-title{
  margin: 20px 0px 10px 0px;
  font-size: 20px;
  font-weight: bold;
  color: #132042;
  width: 100%;
  display: inline-block;
  text-align: right;
}
.item {
    width: 100%;
    display: inline-block;
    padding: 10px;
    border-radius: 11px;
    box-shadow: 0px 4px 20px #e3e3e3d1;
}
.owl-carousel{
  display: inline-block;
}
.fixedw{
  
  float: none !important;
  display: inline-block !important;
}
@media (min-width:320px){
  .fixedw{
     width: calc(50% - 6px) !important;
    margin: 2px !important;
    display: inline-flex;
  }
}
@media (min-width:768px){
  .fixedw{
      width: 30% !important;
      margin: 7px !important;
  }
}
@media (min-width:1200px){
  .fixedw{
      width: 22% !important;
      margin: 10px !important;
  }
}
@media screen and (max-width: 767px) {
  .hideonmob{
      display: none !important;
  }
}
.item-area {
  background-color: #fff;
}
.item-area {
  width: 100%;
  position: relative;
}
.product-image-area {
  padding: 0px 0 0;
  overflow: initial;
}
.product-image {
    width: 100%;
    height: 260px;
    display: block;
    position: relative;
    z-index: 0;
    overflow: hidden;
    border-radius: 3px;
    background: #fff;
}
.product-image img {
  max-width: 100%;
  height: auto;
}
.product-image img {
  width: 100%;
  transition: opacity .3s;
}
.product-image img {
  width: 90%;
  position: absolute;
  top: 0;
  right: 5%;
}
.product-image img.defaultImage {
  opacity: 1;
  filter: alpha(opacity=100);
}
.product-image img {
  width: 100%;
  transition: opacity .3s;
}
 .product-image img {
    position: absolute;
    top: 0;
    right: 0%;
    aspect-ratio: 1/1.5;
    object-fit: cover;
    width: 100%;
}
 .product-image img.hoverImage {
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.details-area {
  padding: 1px 10px 5px;
}
.product-name {
    margin: 5px 0 3px;
    font-size: 13px;
    color: #999;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    text-align: right;
    white-space: nowrap;
    text-overflow: ellipsis;

}
 .product-name {
  overflow: hidden;
  line-height: 16px;
}
.product-name a {
    font-size: 13px;
    line-height: 1.3;
    color: #264372;
    letter-spacing: 0.005em;
}
.disc {
  text-align: right;
  height: 37px;
  overflow: hidden;
  line-height: 17px;
  color: #0976cd;
}
.ratings {
  text-align: right;
  margin: 5px 0;
  display: none;

}
.rating-box {
  width: 64px;
  height: 11px;
  font-size: 0;
  line-height: 0;
  text-indent: -999em;
  overflow: hidden;
  position: relative;
}
.ratings .rating-box {
  display: inline-block;
  vertical-align: middle;
}
.rating-box:before {
  font-family: 'FontAwesome';
  content: "\f005\20\f005\20\f005\20\f005\20\f005";
  width: 64px;
  height: 11px;
  color: #c3c5c9;
  display: block;
  font-size: 11px;
  line-height: 11px;
}
.rating-box .rating {
  float: right;
  height: 11px;
  position: absolute;
  right: 0;
  top: 0;
  overflow: hidden;
}
.rating-box .rating:before {
  font-family: 'FontAwesome';
  content: "\f005\20\f005\20\f005\20\f005\20\f005";
  width: 64px;
  height: 11px;
  color: #ffc600;
  display: block;
  font-size: 11px;
  line-height: 11px;
}
.filter-products .price-box {
  min-height: 37px;
}
.price-box {
  margin: 1px 0;
}
.special-price {
  margin: 0;
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}
.special-price {
  display: block;
  text-align: right;
}
.special-price .price {
    font-weight: 700;
    font-size: 13px;
    color: #a77c18;
}

.catlink {

    background-color: #d5e3ee;
    border-radius: 10px;
    padding: 1px;
    margin: 5px;
}

.shipand {
    color: #0976cd;
    font-size: 14px;
    font-weight: 600;
}

.oldprice {
  color: #30302e;
  font-size: 16px;
}

.item .details-area .actions a.addtocart {
    border-color: #ca3434;
    background-color: #edecec;
    color: #13354e;
    width: 100%;
    text-align: center;
    height: 36px;
    line-height: 32px;
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0;
    text-transform: capitalize;
    margin-top: 5px;
    padding: 0;
    position: relative;
    display: inline-block;
    border-radius: 20px;
    font-weight: 600;
}
.item .details-area .actions a.addtocart:hover {
  border-color: #333;
  background-color: #333;
}



.footer-container {
    width: 100%;
    background-color: #3d3b43;
    color: #143339;
    display: inline-block;
    padding-top: 10px;
}
.footer-middle, .footer-middle p {
  font-size: 13px;
  letter-spacing: 0.005em;
  font-weight: 400;
  line-height: 26px;
}
.footer-container .footer .footer-middle .footer-logo {
  text-align: center;
  width: 100%;
  display: block;
  margin-top: 15px;
}
.footer-container .footer .footer-middle .footer-paragraph {
  text-align: right;
}
.footer-container .footer .footer-middle .footer-paragraph p {
  color: #fff;
  line-height:25px;
  margin-bottom: 14px;
  display: inline-block;
  margin: 10px 0px;
}
.footer-container .footer .footer-middle .footer-custom-block {
  float: right;
}
.footer-container .footer .footer-middle .footer-custom-block span {
  color: #da3f3f;
    display: block;
    text-align: right;
    font-size: 12px;
    line-height: 1;
    font-weight: 700;
    width: 100%;
    display: inline-block;
}
.footer-container .footer .footer-middle .footer-custom-block a {
  text-decoration: none;
}
.footer-container .footer .footer-middle a {
    color: #fff;
    font-weight: 500;
    font-size: 13px;
font-weight: 600;
}
.footer-middle .block {
  text-align: right;
  line-height: 1.5;
  border: 0;
  margin: 0;
  background-color: transparent;
  float: none;
  width: auto;
}
.footer-middle .block .block-title {
  margin-bottom: 10px;
  margin-top: 15px;
  border-right: none;
  padding-right: 0;
}
.footer-middle .block .block-title strong, .footer-top .block-title strong, .footer-top .block-title span {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #fff;
}
.footer-middle .block .block-title strong, .footer-top .block-title strong, .footer-top .block-title span {
  color: #000;
  font-weight: bold;
  letter-spacing: 0;
  text-align: right;
  margin-bottom: 5px;
  display: inline-block;
  margin-top: 20px;
}
.block .block-title {
  margin: 5px 0 10px;
  line-height: 8px;
  font-size: 16px;
  font-weight: 700;
  color: #da3f3f;
  padding-right: 8px;
  border-right: 8px solid #ff9393;
  margin-bottom: 13px;
}
.footer-middle .block .block-title {
  margin-bottom: 20px;
}
.footer-middle .block .block-title {
  margin-bottom: 20px;
  margin-top: 15px;
}
.footer-middle .block .block-title {
  margin-bottom: 10px;
  margin-top: 15px;
  border-right: none;
  padding-right: 0;
  width: 100%;
  display: inline-block;
}
.footer-middle ul {
  padding: 0;
  margin: 0;
}
.footer-container .footer .footer-middle .block-content .col-md-6 .footer-links li {
  margin: 2px 0;
}
.footer-links li {
  margin-bottom: 5px !important;
  list-style: none;
  margin: 0;
}

.footer-middle a {
  font-size: 13px;
  font-weight: 300;
  letter-spacing: 0.005em;
}
.footer-bottom address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857143;
  width: 100%;
  display: inline-block;
  text-align: center;
  padding-top: 20px;
  font-size: 14px;
  margin-bottom: 10px;
}
.footer-bottom address a , .footer-bottom address a:hover , .footer-bottom address a:active , .footer-bottom address a:focus{
 color: #F9AE00;
}
.social-links{
  width: 100%;
  display: inline-block;
  text-align: center;
  padding-bottom: 20px;
}
.social-links a{
  font-size: 16px;
  width: 30px;
  height: 30px;
  border: 1px solid #b1b1b175;
  display: inline-block;
  margin: 0px 5px;
  text-align: center;
  color: #afafaf;
  border-radius: 3px;
}
.social-links a:hover , .social-links a:active , .social-links a:focus{
  border: 1px solid #0000004d;
  background: #00000047;
  color: #ffa800;
}

.breadcrumb {
  display: block !important;
  text-align: right;
  padding: 10px;
  padding-right: 20px;
}
.breadcrumb::before {
  content: "";
  position: absolute;
  width: 100vw;
  height: 100%;
  left: 50%;
  margin-left: -50vw;
  top: 0;
  z-index: -1;
}
.cat-title{
  color: #3a4750;
  font-size: 17px;
  padding-right: 20px;
  text-align: right !important;
  display: block;
}
.subact ul{
  text-align: right;
}
.subact li{
    list-style: none;
    display: inline-block;
    background: #c8a280;
    padding: 3px;
    margin: 5px;
    border-radius: 13px;
}
.contact{
  width: 100%;
  display: inline-block;
}
.signal{
  width: 100%;
  display: inline-block;
  background: #3a475008;
  border-radius: 5px;
  padding: 10px;
  margin-top: 20px;
  border: 1px solid #ddd;
}
.descrr{
  text-align: right;
  line-height: 20px;
}
.show-phone b{
  font-size: 12px;
  letter-spacing: 0px;
  width: 100%;
  display: inline-block;
  text-align: center;
  line-height: 0px;
  font-weight: normal;
}
#slideshow_thumbs{
  margin: 0px;
  padding: 0px;
  display: flex;
}
#slideshow_thumbs li{
  padding: 3px;
}
#slideshow_thumbs img{
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  padding: 1px;
  border-radius: 2px;
}
.desoslide-wrapper img{
  width: 100%;
  border-bottom: 1px solid #ddd;
  padding: 1px;
  border-radius: 4px;
}
.pslide {
  width: 100%;
  display: inline-block;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.products-content{
  width: 100%;
  display: inline-block;
}
.desoslide-overlay {
  height: 36px;
  position: absolute;
  z-index: 10;
  text-align: right;
  color: #fff;
  font-weight: 700;
  background-color: #000;
  padding: 8px;
  opacity: 0;
}
.products-content h1{
    font-size: 14px;
    font-weight: bold;
    color: #433c3d;
}
.products-content h3{
  font-size: 22px;
  font-weight: bold;
  color: #1b2b41;
}
.catg {
  font-size: 16px;
  margin-bottom: 0px;
  width: 100%;
  display: inline-block;
  text-align: right;
}
.catg a ,.catg a:hover , .catg a:active , .catg a:focus{
  color: #194252;
}
.pound{
  display: inline-block;
  margin: 0;
  margin-right: 10px;
}
.desc{
  text-align: right;
  margin-bottom: 20px;
}
.box-offers {
  width: 100%;
  display: inline-block;
  margin-bottom: 10px;
  border-radius: 5px;
  background: #fff;
}
.offer-p {
  padding: 2px 5px;
}
.box-offers ul {
  padding: 0;
  margin: 0;
}
.box-offers ul li {
    line-height: 20px;
    list-style: none;
    font-size: 15px;
    color: #27466c;
}
.swal2-popup .swal2-title {
  font-size: 14px !important;
  word-wrap: break-word;
}
.CartDataClose{
  width: 100%;
  display: inline-block;
  position: relative;
}
.CartDataClose h2{
  text-align: center;
  color: #24303a;
  font-size: 18px;
  margin-top: 10px;
}
.CartDataClose a{
  position: absolute;
  left: 10px;
  top: 0px;
  font-size: 20px;
  color: #E73D50;
}
#CartDataRes{
  display: inline-block;
  width: 100%;
  position: relative;
  margin-top: 20px;
}
.removefromcart{
  width: 100%;
  text-align: center;
  display: inline-block;
  font-size: 20px;
  color: #e73d50;
}
.red {
  color: #E73D50;
}
#btn2 , #userdatacart{
  display: none;
}
.signal h2{
  font-size: 20px;
}
.signal h3{
  font-size: 16px;
}
.paid-title{
  font-size: 20px;
}

.catdescr{
    display: block;
    padding: 6px;
    border-radius: 3px;
    background: #ffffff;
    margin: 5px;
    color: #0976cd;
    margin-bottom: 7px;
    border: 1px solid #dfdede;
    font-size: 15px;
}

.block-content{
    width: 100%;
    display: inline-block;
}
.pricetable{
    width: 100%;
    display: inline-block; 
}
.pricetable tr{ 
    box-shadow: 0px 0px 2px #464646;
}
.pricetable i{ 
    margin-left:10px;
}
.pricetable a{ 
    
}
.pricetable .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    padding: 10px 10px;
    font-size: 14px;
    font-weight: bold;
    color: #616161;
    text-align: right;
    line-height: 25px;
}
@media (max-width: 991px){
.pricetable .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    display: flex;
    border: 0;
}
}

.tabletitles{
    width: 100%;
    display: inline-block;
    text-align: center;
    background: #132042;
    border: 0px solid #000;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    padding: 20px 10px;
}
.tabletitles h2{
    color: #fff;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
    line-height: 20px;
}
.tabletitles h4{
    color: #ddd;
    font-size: 12px;
}
.tabletitles i{
    margin: 0px 5px;
}
.acou{
    width: 100%;
    display: inline-block;
}
.infobtn i{
   position: absolute;
   top: 9px;
   left: 10px;
   font-size: 20px;
   color: #d9534f;
}
.infobtn {
    position: relative;
    width: 100%;
    display: inline-block;
    padding: 3px 6px;
    background: #ffffff;
    color: #000;
    font-weight: bold;
    margin-top: 6px;
    border: 1px solid #ccc;
    border-radius: 3px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin: 2px;
}
.infodata {
   width: 100%;
    border: 1px solid #ccc;
    border-top: 0;
    padding: 5px 10px;
}
.hd{
    display:none;
}
.tgs{
    display: inline-block;
    font-size: 14px;
    margin: 5px;
    padding: 10px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #303841;
    background: #fff;
}
.tgs:hover , .tgs:active , .tgs:focus{
    webkit-transition: all 0.4s ease-in-out;
    -moz-transition: all 0.4s ease-in-out;
    -o-transition: all 0.4s ease-in-out;
    -ms-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
    border: 1px dashed #303841;
    transform: scale(1.2);
    color: #da3f3f;
    background: #ffffff;
}

.discount-p {
    font-size: 14px;
    color: #eaf4ff;
    text-align: center;
    display: block;
    background: #ff8a12bf;
    margin: 0px;
    padding: 4px;
    border-radius: 5px;
}

.available-stock {
    font-size: 11px;
    text-align: center;
    color: #000000;
    margin: 3px;
    padding: 2px 2px 1px 9px;
}
.out-stock {
    font-size: 11px;
    text-align: center;
    color: #000000;
    margin: 3px;
    border-radius: 10px;
    border: 1px solid #ebebeb;
    padding: 2px 2px 1px 9px;
}
.product-price-wrp {
    margin-left: 18px;
    margin-right: 0;
    display: table-cell;
    padding: 2px;
}

.btn-danger {
    color: #ffffff;
    background-color: #12253c;
    border: #12253c;
    font-weight: 800;
    font-size: 18px;
}

.checkbox_div {
  width: 75px !important;
  background: #f7f7f7;
  padding: 3px;
  margin: 4px;
  border-radius: 11px;
  border: 1px solid #f1f1f1;
}

.iconss {
    display: inline-block;
    background: #ffffff;
    width: 100%;
    border: 1px solid #f5f5f5;
    box-shadow: 0px 0px 4px -1px #d5d5d5;
    
}

.iconss img {
  width: 60px;
  margin-right: 22px;

}


.iconss-p {
    font-size: 17px;
    margin-top: 12px;
    text-align: center;
    font-weight: 600;
    color: #1b2b41;
}
.cat-box {
 
}
.cat-box img {
     border-radius: 20px 9px 49px 0;
}

.subact a {

    color: #fff;
    padding: 6px;
    margin: 5px;
    text-align: center;
}

.index-box2 {
    width: 100%;
    display: block;
    margin: 0px;
    padding: 0px;
    border-radius: 11px;
}
.index-box2 img {
    width: 100%;
    border-radius: 90%;
}
.index-box2 p {
  color: #24303a;
  font-size: 12px;
  margin: 1px;
  padding: 0px;
  margin-top: 9px;
  line-height: 1.5;
  text-align: center;
}

.index-box2 a {
    color: #1c293a;
    font-weight: 400;
    font-size: 12px;
}


.btn-success {
    color: #fff;
    background-color: #5db57a;
    width: 100%;
    margin: 3px;
    border-color: #5db57a;
}

.btn-success2 {
    padding: 4px;
    color: #fff;
    background-color: #cba777;
    width: 100%;
    margin: 3px;
    border-color: #cba777;
    display: inline-block;
    text-align: center;
    border-radius: 4px;
    font-size: 14px;
}
.filterbox {
      width: 100%;
      margin-top: 8px;
      margin-bottom: 7px;
      margin-right: 0px;
  }


  .siza {
  padding: 0;
}

.btn-info {
    color: #000;
    background-color: #f2f2f7;
    border: 1px solid #edeffe;
    text-align: center;
}

.store-credibility {
    font-size: 11px;
    text-align: center;
    color: #000000;
    margin: 3px;
    border-radius: 10px;
    border: 1px solid #ebebeb;
    padding: 2px 2px 1px 9px;
}

.prices {
  font-size: 13px;
  font-weight: 700;
  margin: -1px;
  text-align: center;
  margin-right: 44px;
}

.footer-page {
    text-align: center;
}

.footer-page a {
    color: #232745;
    margin: 13px;
    font-size: 15px;
   font-weight: 400;
}

.block2 {
background: #dabb91;
    padding: 5px;
    margin: 0;
}

.block-title {
color: #000000;
    text-align: center;
    font-size: 14px;
}

.verified-links {
    width: 100%;
    display: inline-block;
    text-align: center;
    padding-bottom: 20px;
}

.verified-links a {
    font-size: 16px;
    width: 81px;
    height: 81px;
    border: 1px solid #ebebeb;
    display: inline-block;
    margin: 0px 5px;
    text-align: center;
    color: #e7c5c5;
    border-radius: 3px;
    background: #fff;
}

.sale-discount {
font-size: 11px;
    text-align: revert;
    color: #ffffff;
    margin: 3px;
    z-index: 999999;
    position: absolute;
    background: #000000bf;
    bottom: 139px;
    right: 10px;
}
.sale-discount img {
    width: 25px;
    height: 26px;
}

.box-checkout {
    width: 100%;
    padding: 5px;
    margin: 1px;
}

.cart-h3 img {
  max-width: 100%;
  width: 30px;
  height: 30px;
  padding: 3px;
}


.boxcart1 {
width: 100%;
    margin: 0;
    padding: 3px;
    background: #ffffff;
    box-shadow: 0px 1px 4px 0px #b9b9b9;
    border-radius: 8px;
    margin-top: 12px;
    margin-bottom: 12px;
}  


.current-price {
  color: #0e1a30; /* أخضر */
  font-weight: bold;
}

.old-price {
  color: #be4753; /* أحمر */
  text-decoration: line-through;
}

.save-amount {
  color: #0e1a30; /* أصفر/برتقالي */
  font-weight: bold;
}


@media (max-width: 767.98px) {
  .no-center-mobile {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    padding-left: 0;
    padding-right: 0;
  }
}






.product-price-box {
  position: relative;
  background-color: #ffffff;
  padding: 10px;
  margin-top: 7px;
}

.save-box {
  position: absolute;
  top: 0px;
  right: 13px;
  background-color: #3e9e53c4;
  color: #fff;
  padding: 9px 6px;
  font-size: 13px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 0px 18px 13px 21px;
}


.shipping-box {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
  margin-top: 15px;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  max-width: 400px; /* أو تحكم حسب الحاجة */
}

.shipping-box-title {
  font-weight: bold;
  color: #2e7d32;
  margin-bottom: 10px;
  font-size: 15px;
}

.shipping-box-content {
     display: flex ;
       align-items: flex-start;
      gap: 10px;
      text-align: justify;
  }

.shipping-box-icon img {
  width: 24px;
  height: auto;
}

.shipping-box-text > div {
  margin-bottom: 0px;
}

.shipping-box-text strong {
  font-weight: bold;
  text-align: justify;
}


.simpel-price {
text-align: right;
    margin-right: 14px;
}


