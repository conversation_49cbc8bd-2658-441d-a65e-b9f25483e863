<?php
ob_start();
$Title_page = 'الإعدادات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

	if (isset($_POST['edit1'])){

		Update('Site_Name' , $_POST['var1']);
		Update('Site_URL' , $_POST['var2']);
		Update('Description' , $_POST['var3']);
		Update('Keywords' , $_POST['var4']);
		redirect_home ('back' , 0); exit();
	}	

	if (isset($_POST['edit2'])){

		Update('CountCatIconInIndex' , $_POST['var1']);
		Update('IndexProductsComp' , $_POST['var2']);
		Update('PhoneNum' , $_POST['var3']);
		Update('ProductMostViewedComp' , $_POST['var4']);
		redirect_home ('back' , 0); exit();
	}

	
	
	if (isset($_POST['edit3'])){

		Update('Social_Face' , $_POST['var1']);
		Update('Social_twitter' , $_POST['var2']);
		Update('Social_insta' , $_POST['var3']);
		Update('Social_google' , $_POST['var4']);
		Update('Social_youtube' , $_POST['var5']);
		redirect_home ('back' , 0); exit();
	}

	
	if (isset($_POST['edit4'])){

		Update('FooterDesc' , $_POST['var1']);
		Update('AboutPageContent' , $_POST['var2']);
		Update('PrivacyPageContent' , $_POST['var3']);
		redirect_home ('back' , 0); exit();
	}



				?>		
			
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">

			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> الإعدادات العامه</h4><hr>
					<div class="col-md-6">
				<form method="post">
	                    <div class="form-group">
	                      <label class="form-control-label">إسم الموقع</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Site_Name');?>" class="form-control">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">رابط الموقع</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Site_URL');?>" class="form-control ltr">
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الوصف</label>
	                      <textarea class="form-control" name="var3"><?php echo GetTableSet ('Description');?></textarea>
	                    </div>
	                    <div class="form-group">
	                      <label class="form-control-label">الكلمات الدلاليه</label>
	                      <textarea class="form-control" name="var4"><?php echo GetTableSet ('Keywords');?></textarea>
											</div>
										
	                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit1" class="btn btn-primary">
		                </div>
               	</form> 	
               	 	</div>	
               	 	<div class="col-md-6">
               	 		  <label class="form-control-label" style="text-align: center; width: 100%;">لوجو الموقع</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('Logo') ;?>" style="width: 200px;height: auto; margin-bottom: 20px;margin-top: 10px;">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photo" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="LOGO">
							        </form>
							        <label for="photo" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploads" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>
							<hr>
							<label class="form-control-label" style="text-align: center; width: 100%;">الصورة الإفتراضية</label>
							  <center>
							    <img src="<?php echo $Site_URL.'/'.GetTableSet ('DefaultImage') ;?>" style="width: 250px; height: 200px;margin-bottom: 20px;margin-top: 10px;">
							    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >
							         
							          <input type="file" name="photo" id="photo1" required style="display: none;" />
							          <input type="submit" style="display: none;" id="Uploads1" value="Uploads" class="submit" />
							          <input type="hidden" name="Image_For" value="DefaultImage">
							        </form>
							        <label for="photo1" class="btn btn-info btn-sm" ><i class="fa fa-camera"></i> إختر الصوره</label>
							        <label for="Uploads1" class="btn btn-info btn-sm" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>
							  </center>  
               	 	</div>	
			</div>	
</div></div></div></div>



<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات أخرى</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد ايقونات الأقسام فى الرئيسية</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('CountCatIconInIndex');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المنتجات فى الرئيسية لكل قسم</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('IndexProductsComp');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">رقم موبايل الدعم الفنى الموجود فى الفوتر</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('PhoneNum');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">عدد المنتجات فى الأكثر مشاهدة</label>
	                      <input type="text" name="var4" value="<?php echo GetTableSet ('ProductMostViewedComp');?>" class="form-control">
	                    </div>
					</div>	
					
					
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit2" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>


<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات التواصل الإجتماعى</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة الفيس بوك</label>
	                      <input type="text" name="var1" value="<?php echo GetTableSet ('Social_Face');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة تويتر</label>
	                      <input type="text" name="var2" value="<?php echo GetTableSet ('Social_twitter');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة إنستجرام</label>
	                      <input type="text" name="var3" value="<?php echo GetTableSet ('Social_insta');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة جوجل بلس</label>
	                      <input type="text" name="var4" value="<?php echo GetTableSet ('Social_google');?>" class="form-control">
	                    </div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">قناة اليوتيوب</label>
	                      <input type="text" name="var5" value="<?php echo GetTableSet ('Social_youtube');?>" class="form-control">
	                    </div>
					</div>	
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit3" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>



<div class="row">
		<div class="col-md-12 col-sm-12"><br>
			<div class="card">
				<div class="card-body">
			<div class="divs">
				<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إعدادات محتوى html</h4><hr>
				<form method="post">
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">الوصف فى الفوتر</label>
	                      <textarea style="min-height: 300px;" class="form-control ltr" name="var1"><?php echo GetTableSet ('FooterDesc');?></textarea>
	                    </div>
					</div>	
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة معلومات عنا</label>
	                      <textarea style="min-height: 300px;" class="form-control ltr" name="var2"><?php echo GetTableSet ('AboutPageContent');?></textarea>
	                    </div>
					</div>					
					<div class="col-md-6">
						<div class="form-group">
	                      <label class="form-control-label">صفحة سياسة الخصوصية</label>
	                      <textarea style="min-height: 300px;" class="form-control ltr" name="var3"><?php echo GetTableSet ('PrivacyPageContent');?></textarea>
	                    </div>
					</div>
					
					<div class="col-md-12">
					 <div class="form-group">       
		                 <input type="submit" value="تعديل" name="edit4" class="btn btn-primary">
		             </div>
		            </div>    
				</form>
			</div>
</div></div></div></div>

<?php
include('footer.php'); 
ob_end_flush();
?>