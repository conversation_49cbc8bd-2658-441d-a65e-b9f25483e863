<html>
<head>
<title>File Source for Query.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file Query.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---Query.php.html">Query.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa.</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Arabic&nbsp;Queary&nbsp;Class</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;Query.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;Build&nbsp;WHERE&nbsp;condition&nbsp;for&nbsp;SQL&nbsp;statement&nbsp;using&nbsp;MySQL&nbsp;REGEXP&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Arabic&nbsp;lexical&nbsp;&nbsp;rules</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;Queary&nbsp;Class</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;class&nbsp;build&nbsp;WHERE&nbsp;condition&nbsp;for&nbsp;SQL&nbsp;statement&nbsp;using&nbsp;MySQL&nbsp;REGEXP&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;lexical&nbsp;&nbsp;rules.</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*&nbsp;With&nbsp;the&nbsp;exception&nbsp;of&nbsp;the&nbsp;Qur'an&nbsp;and&nbsp;pedagogical&nbsp;texts,&nbsp;Arabic&nbsp;is&nbsp;generally</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;written&nbsp;without&nbsp;vowels&nbsp;or&nbsp;other&nbsp;graphic&nbsp;symbols&nbsp;that&nbsp;indicate&nbsp;how&nbsp;a&nbsp;word&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;pronounced.&nbsp;The&nbsp;reader&nbsp;is&nbsp;expected&nbsp;to&nbsp;fill&nbsp;these&nbsp;in&nbsp;from&nbsp;context.&nbsp;Some&nbsp;of&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*&nbsp;graphic&nbsp;symbols&nbsp;include&nbsp;sukuun,&nbsp;which&nbsp;is&nbsp;placed&nbsp;over&nbsp;a&nbsp;consonant&nbsp;to&nbsp;indicate&nbsp;that</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;it&nbsp;is&nbsp;not&nbsp;followed&nbsp;by&nbsp;a&nbsp;vowel;&nbsp;shadda,&nbsp;written&nbsp;over&nbsp;a&nbsp;consonant&nbsp;to&nbsp;indicate&nbsp;it&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;doubled;&nbsp;and&nbsp;hamza,&nbsp;the&nbsp;sign&nbsp;of&nbsp;the&nbsp;glottal&nbsp;stop,&nbsp;which&nbsp;can&nbsp;be&nbsp;written&nbsp;above&nbsp;or</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;below&nbsp;(alif)&nbsp;at&nbsp;the&nbsp;beginning&nbsp;of&nbsp;a&nbsp;word,&nbsp;or&nbsp;on&nbsp;(alif),&nbsp;(waaw),&nbsp;(yaa'),</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;or&nbsp;by&nbsp;itself&nbsp;on&nbsp;the&nbsp;line&nbsp;elsewhere.&nbsp;Also,&nbsp;common&nbsp;spelling&nbsp;differences&nbsp;regularly</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;appear,&nbsp;including&nbsp;the&nbsp;use&nbsp;of&nbsp;(haa')&nbsp;for&nbsp;(taa'&nbsp;marbuuta)&nbsp;and&nbsp;(alif&nbsp;maqsuura)</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*&nbsp;for&nbsp;(yaa').&nbsp;These&nbsp;features&nbsp;of&nbsp;written&nbsp;Arabic,&nbsp;which&nbsp;are&nbsp;also&nbsp;seen&nbsp;in&nbsp;Hebrew&nbsp;as</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;well&nbsp;as&nbsp;other&nbsp;languages&nbsp;written&nbsp;with&nbsp;Arabic&nbsp;script&nbsp;(such&nbsp;as&nbsp;Farsi,&nbsp;Pashto,&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;Urdu),&nbsp;make&nbsp;analyzing&nbsp;and&nbsp;searching&nbsp;texts&nbsp;quite&nbsp;challenging.&nbsp;In&nbsp;addition,&nbsp;Arabic</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;morphology&nbsp;and&nbsp;grammar&nbsp;are&nbsp;quite&nbsp;rich&nbsp;and&nbsp;present&nbsp;some&nbsp;unique&nbsp;issues&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;information&nbsp;retrieval&nbsp;applications.</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;There&nbsp;are&nbsp;essentially&nbsp;three&nbsp;ways&nbsp;to&nbsp;search&nbsp;an&nbsp;Arabic&nbsp;text&nbsp;with&nbsp;Arabic&nbsp;queries:</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;literal,&nbsp;stem-based&nbsp;or&nbsp;root-based.</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a64"></a><span class="src-doc">&nbsp;*&nbsp;A&nbsp;literal&nbsp;search,&nbsp;the&nbsp;simplest&nbsp;search&nbsp;and&nbsp;retrieval&nbsp;method,&nbsp;matches&nbsp;documents</span></div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">&nbsp;*&nbsp;based&nbsp;on&nbsp;the&nbsp;search&nbsp;terms&nbsp;exactly&nbsp;as&nbsp;the&nbsp;user&nbsp;entered&nbsp;them.&nbsp;The&nbsp;advantage&nbsp;of&nbsp;this</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;technique&nbsp;is&nbsp;that&nbsp;the&nbsp;documents&nbsp;returned&nbsp;will&nbsp;without&nbsp;a&nbsp;doubt&nbsp;contain&nbsp;the&nbsp;exact</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;term&nbsp;for&nbsp;which&nbsp;the&nbsp;user&nbsp;is&nbsp;looking.&nbsp;But&nbsp;this&nbsp;advantage&nbsp;is&nbsp;also&nbsp;the&nbsp;biggest</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;disadvantage:&nbsp;many,&nbsp;if&nbsp;not&nbsp;most,&nbsp;of&nbsp;the&nbsp;documents&nbsp;containing&nbsp;the&nbsp;terms&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;different&nbsp;forms&nbsp;will&nbsp;be&nbsp;missed.&nbsp;Given&nbsp;the&nbsp;many&nbsp;ambiguities&nbsp;of&nbsp;written&nbsp;Arabic,&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;success&nbsp;rate&nbsp;of&nbsp;this&nbsp;method&nbsp;is&nbsp;quite&nbsp;low.&nbsp;For&nbsp;example,&nbsp;if&nbsp;the&nbsp;user&nbsp;searches</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;for&nbsp;(kitaab,&nbsp;book),&nbsp;he&nbsp;or&nbsp;she&nbsp;will&nbsp;not&nbsp;find&nbsp;documents&nbsp;that&nbsp;only</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;contain&nbsp;(`al-kitaabu,&nbsp;the&nbsp;book).</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a74"></a><span class="src-doc">&nbsp;*&nbsp;Stem-based&nbsp;searching,&nbsp;a&nbsp;more&nbsp;complicated&nbsp;method,&nbsp;requires&nbsp;some&nbsp;normalization&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;original&nbsp;texts&nbsp;and&nbsp;the&nbsp;queries.&nbsp;This&nbsp;is&nbsp;done&nbsp;by&nbsp;removing&nbsp;the&nbsp;vowel&nbsp;signs,</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-doc">&nbsp;*&nbsp;unifying&nbsp;the&nbsp;hamza&nbsp;forms&nbsp;and&nbsp;removing&nbsp;or&nbsp;standardizing&nbsp;the&nbsp;other&nbsp;signs.</span></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-doc">&nbsp;*&nbsp;Additionally,&nbsp;grammatical&nbsp;affixes&nbsp;and&nbsp;other&nbsp;constructions&nbsp;which&nbsp;attach&nbsp;directly</span></div></li>
<li><div class="src-line"><a name="a78"></a><span class="src-doc">&nbsp;*&nbsp;to&nbsp;words,&nbsp;such&nbsp;as&nbsp;conjunctions,&nbsp;prepositions,&nbsp;and&nbsp;the&nbsp;definite&nbsp;article,&nbsp;should&nbsp;be</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;*&nbsp;identified&nbsp;and&nbsp;removed.&nbsp;Finally,&nbsp;regular&nbsp;and&nbsp;irregular&nbsp;plural&nbsp;forms&nbsp;need&nbsp;to&nbsp;be</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;*&nbsp;identified&nbsp;and&nbsp;reduced&nbsp;to&nbsp;their&nbsp;singular&nbsp;forms.&nbsp;Performing&nbsp;this&nbsp;type&nbsp;of&nbsp;stemming</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;*&nbsp;leads&nbsp;to&nbsp;more&nbsp;successful&nbsp;searches,&nbsp;but&nbsp;can&nbsp;be&nbsp;problematic&nbsp;due&nbsp;to&nbsp;over-generation</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;*&nbsp;or&nbsp;incorrect&nbsp;generation&nbsp;of&nbsp;stems.</span></div></li>
<li><div class="src-line"><a name="a83"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a84"></a><span class="src-doc">&nbsp;*&nbsp;A&nbsp;third&nbsp;method&nbsp;for&nbsp;searching&nbsp;Arabic&nbsp;texts&nbsp;is&nbsp;to&nbsp;index&nbsp;and&nbsp;search&nbsp;for&nbsp;the&nbsp;root</span></div></li>
<li><div class="src-line"><a name="a85"></a><span class="src-doc">&nbsp;*&nbsp;forms&nbsp;of&nbsp;each&nbsp;word.&nbsp;Since&nbsp;most&nbsp;verbs&nbsp;and&nbsp;nouns&nbsp;in&nbsp;Arabic&nbsp;are&nbsp;derived&nbsp;from</span></div></li>
<li><div class="src-line"><a name="a86"></a><span class="src-doc">&nbsp;*&nbsp;triliteral&nbsp;(or,&nbsp;rarely,&nbsp;quadriliteral)&nbsp;roots,&nbsp;identifying&nbsp;the&nbsp;underlying&nbsp;root&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a87"></a><span class="src-doc">&nbsp;*&nbsp;each&nbsp;word&nbsp;theoretically&nbsp;retrieves&nbsp;most&nbsp;of&nbsp;the&nbsp;documents&nbsp;containing&nbsp;a&nbsp;given&nbsp;search</span></div></li>
<li><div class="src-line"><a name="a88"></a><span class="src-doc">&nbsp;*&nbsp;term&nbsp;regardless&nbsp;of&nbsp;form.&nbsp;However,&nbsp;there&nbsp;are&nbsp;some&nbsp;significant&nbsp;challenges&nbsp;with&nbsp;this</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;*&nbsp;approach.&nbsp;Determining&nbsp;the&nbsp;root&nbsp;for&nbsp;a&nbsp;given&nbsp;word&nbsp;is&nbsp;extremely&nbsp;difficult,&nbsp;since&nbsp;it</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;*&nbsp;requires&nbsp;a&nbsp;detailed&nbsp;morphological,&nbsp;syntactic&nbsp;and&nbsp;semantic&nbsp;analysis&nbsp;of&nbsp;the&nbsp;text&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;*&nbsp;fully&nbsp;disambiguate&nbsp;the&nbsp;root&nbsp;forms.&nbsp;The&nbsp;issue&nbsp;is&nbsp;complicated&nbsp;further&nbsp;by&nbsp;the&nbsp;fact</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;*&nbsp;that&nbsp;not&nbsp;all&nbsp;words&nbsp;are&nbsp;derived&nbsp;from&nbsp;roots.&nbsp;For&nbsp;example,&nbsp;loan&nbsp;words&nbsp;(words</span></div></li>
<li><div class="src-line"><a name="a93"></a><span class="src-doc">&nbsp;*&nbsp;borrowed&nbsp;from&nbsp;another&nbsp;language)&nbsp;are&nbsp;not&nbsp;based&nbsp;on&nbsp;root&nbsp;forms,&nbsp;although&nbsp;there&nbsp;are</span></div></li>
<li><div class="src-line"><a name="a94"></a><span class="src-doc">&nbsp;*&nbsp;even&nbsp;exceptions&nbsp;to&nbsp;this&nbsp;rule.&nbsp;For&nbsp;example,&nbsp;some&nbsp;loans&nbsp;that&nbsp;have&nbsp;a&nbsp;structure</span></div></li>
<li><div class="src-line"><a name="a95"></a><span class="src-doc">&nbsp;*&nbsp;similar&nbsp;to&nbsp;triliteral&nbsp;roots,&nbsp;such&nbsp;as&nbsp;the&nbsp;English&nbsp;word&nbsp;film,&nbsp;are&nbsp;handled</span></div></li>
<li><div class="src-line"><a name="a96"></a><span class="src-doc">&nbsp;*&nbsp;grammatically&nbsp;as&nbsp;if&nbsp;they&nbsp;were&nbsp;root-based,&nbsp;adding&nbsp;to&nbsp;the&nbsp;complexity&nbsp;of&nbsp;this&nbsp;type</span></div></li>
<li><div class="src-line"><a name="a97"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;search.&nbsp;Finally,&nbsp;the&nbsp;root&nbsp;can&nbsp;serve&nbsp;as&nbsp;the&nbsp;foundation&nbsp;for&nbsp;a&nbsp;wide&nbsp;variety&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a98"></a><span class="src-doc">&nbsp;*&nbsp;words&nbsp;with&nbsp;related&nbsp;meanings.&nbsp;The&nbsp;root&nbsp;(k-t-b)&nbsp;is&nbsp;used&nbsp;for&nbsp;many&nbsp;words&nbsp;related</span></div></li>
<li><div class="src-line"><a name="a99"></a><span class="src-doc">&nbsp;*&nbsp;to&nbsp;writing,&nbsp;including&nbsp;(kataba,&nbsp;to&nbsp;write),&nbsp;(kitaab,&nbsp;book),&nbsp;(maktab,</span></div></li>
<li><div class="src-line"><a name="a100"></a><span class="src-doc">&nbsp;*&nbsp;office),&nbsp;and&nbsp;(kaatib,&nbsp;author).&nbsp;But&nbsp;the&nbsp;same&nbsp;root&nbsp;is&nbsp;also&nbsp;used&nbsp;for&nbsp;regiment/</span></div></li>
<li><div class="src-line"><a name="a101"></a><span class="src-doc">&nbsp;*&nbsp;battalion,&nbsp;(katiiba).&nbsp;As&nbsp;a&nbsp;result,&nbsp;searching&nbsp;based&nbsp;on&nbsp;root&nbsp;forms&nbsp;results&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a102"></a><span class="src-doc">&nbsp;*&nbsp;very&nbsp;high&nbsp;recall,&nbsp;but&nbsp;precision&nbsp;is&nbsp;usually&nbsp;quite&nbsp;low.</span></div></li>
<li><div class="src-line"><a name="a103"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a104"></a><span class="src-doc">&nbsp;*&nbsp;While&nbsp;search&nbsp;and&nbsp;retrieval&nbsp;of&nbsp;Arabic&nbsp;text&nbsp;will&nbsp;never&nbsp;be&nbsp;an&nbsp;easy&nbsp;task,&nbsp;relying&nbsp;on</span></div></li>
<li><div class="src-line"><a name="a105"></a><span class="src-doc">&nbsp;*&nbsp;linguistic&nbsp;analysis&nbsp;tools&nbsp;and&nbsp;methods&nbsp;can&nbsp;help&nbsp;make&nbsp;the&nbsp;process&nbsp;more&nbsp;successful.</span></div></li>
<li><div class="src-line"><a name="a106"></a><span class="src-doc">&nbsp;*&nbsp;Ultimately,&nbsp;the&nbsp;search&nbsp;method&nbsp;you&nbsp;choose&nbsp;should&nbsp;depend&nbsp;on&nbsp;how&nbsp;critical&nbsp;it&nbsp;is&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a107"></a><span class="src-doc">&nbsp;*&nbsp;retrieve&nbsp;every&nbsp;conceivable&nbsp;instance&nbsp;of&nbsp;a&nbsp;word&nbsp;or&nbsp;phrase&nbsp;and&nbsp;the&nbsp;resources&nbsp;you</span></div></li>
<li><div class="src-line"><a name="a108"></a><span class="src-doc">&nbsp;*&nbsp;have&nbsp;to&nbsp;process&nbsp;search&nbsp;returns&nbsp;in&nbsp;order&nbsp;to&nbsp;determine&nbsp;their&nbsp;true&nbsp;relevance.</span></div></li>
<li><div class="src-line"><a name="a109"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a110"></a><span class="src-doc">&nbsp;*&nbsp;Source:&nbsp;Volume&nbsp;13&nbsp;Issue&nbsp;7&nbsp;of&nbsp;MultiLingual&nbsp;Computing&nbsp;&amp;</span></div></li>
<li><div class="src-line"><a name="a111"></a><span class="src-doc">&nbsp;*&nbsp;Technology&nbsp;published&nbsp;by&nbsp;MultiLingual&nbsp;Computing,&nbsp;Inc.,&nbsp;319&nbsp;North&nbsp;First&nbsp;Ave.,</span></div></li>
<li><div class="src-line"><a name="a112"></a><span class="src-doc">&nbsp;*&nbsp;Sandpoint,&nbsp;Idaho,&nbsp;USA,&nbsp;208-263-8178,&nbsp;Fax:&nbsp;208-263-6310.</span></div></li>
<li><div class="src-line"><a name="a113"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a114"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a115"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a116"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a117"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Query');</span></div></li>
<li><div class="src-line"><a name="a118"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a119"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbuser&nbsp;=&nbsp;'root';</span></div></li>
<li><div class="src-line"><a name="a120"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbpwd&nbsp;=&nbsp;'';</span></div></li>
<li><div class="src-line"><a name="a121"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbname&nbsp;=&nbsp;'test';</span></div></li>
<li><div class="src-line"><a name="a122"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a123"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;try&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a124"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbh&nbsp;=&nbsp;new&nbsp;PDO('mysql:host=localhost;dbname='.$dbname,&nbsp;$dbuser,&nbsp;$dbpwd);</span></div></li>
<li><div class="src-line"><a name="a125"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a126"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//&nbsp;Set&nbsp;the&nbsp;error&nbsp;reporting&nbsp;attribute</span></div></li>
<li><div class="src-line"><a name="a127"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbh-&gt;setAttribute(PDO::ATTR_ERRMODE,&nbsp;PDO::ERRMODE_EXCEPTION);</span></div></li>
<li><div class="src-line"><a name="a128"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a129"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbh-&gt;exec(&quot;SET&nbsp;NAMES&nbsp;'utf8'&quot;);</span></div></li>
<li><div class="src-line"><a name="a130"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a131"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;($_GET['keyword']&nbsp;!=&nbsp;'')&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a132"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$keyword&nbsp;=&nbsp;@$_GET['keyword'];</span></div></li>
<li><div class="src-line"><a name="a133"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$keyword&nbsp;=&nbsp;str_replace('\&quot;',&nbsp;'&quot;',&nbsp;$keyword);</span></div></li>
<li><div class="src-line"><a name="a134"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a135"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj-&gt;setStrFields('headline');</span></div></li>
<li><div class="src-line"><a name="a136"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj-&gt;setMode($_GET['mode']);</span></div></li>
<li><div class="src-line"><a name="a137"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a138"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strCondition&nbsp;=&nbsp;$Arabic-&gt;getWhereCondition($keyword);</span></div></li>
<li><div class="src-line"><a name="a139"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}&nbsp;else&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a140"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$strCondition&nbsp;=&nbsp;'1';</span></div></li>
<li><div class="src-line"><a name="a141"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a142"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a143"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$StrSQL&nbsp;=&nbsp;&quot;SELECT&nbsp;`headline`&nbsp;FROM&nbsp;`aljazeera`&nbsp;WHERE&nbsp;$strCondition&quot;;</span></div></li>
<li><div class="src-line"><a name="a144"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a145"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$i&nbsp;=&nbsp;0;</span></div></li>
<li><div class="src-line"><a name="a146"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;foreach&nbsp;($dbh-&gt;query($StrSQL)&nbsp;as&nbsp;$row)&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a147"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$headline&nbsp;=&nbsp;$row['headline'];</span></div></li>
<li><div class="src-line"><a name="a148"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$i++;</span></div></li>
<li><div class="src-line"><a name="a149"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;($i&nbsp;%&nbsp;2&nbsp;==&nbsp;0)&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a150"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$bg&nbsp;=&nbsp;&quot;#f0f0f0&quot;;</span></div></li>
<li><div class="src-line"><a name="a151"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}&nbsp;else&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a152"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$bg&nbsp;=&nbsp;&quot;#ffffff&quot;;</span></div></li>
<li><div class="src-line"><a name="a153"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a154"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;tr&nbsp;bgcolor=\&quot;$bg\&quot;&gt;&lt;td&gt;$headline&lt;/td&gt;&lt;/tr&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a155"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a156"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a157"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//&nbsp;Close&nbsp;the&nbsp;databse&nbsp;connection</span></div></li>
<li><div class="src-line"><a name="a158"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbh&nbsp;=&nbsp;null;</span></div></li>
<li><div class="src-line"><a name="a159"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a160"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}&nbsp;catch&nbsp;(PDOException&nbsp;$e)&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a161"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;$e-&gt;getMessage();</span></div></li>
<li><div class="src-line"><a name="a162"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a163"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a164"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a165"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a166"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a167"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a168"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a169"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a170"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a171"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a172"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a173"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a174"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a175"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;build&nbsp;WHERE&nbsp;condition&nbsp;for&nbsp;SQL&nbsp;statement&nbsp;using&nbsp;MySQL&nbsp;REGEXP&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a176"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;lexical&nbsp;&nbsp;rules</span></div></li>
<li><div class="src-line"><a name="a177"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a178"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a179"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a180"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a181"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a182"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a183"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a184"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a185"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a186"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a></div></li>
<li><div class="src-line"><a name="a187"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a188"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_fields&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a189"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_lexPatterns&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a190"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_lexReplacements&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a191"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">private&nbsp;</span><span class="src-var">$_mode&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a192"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a193"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a194"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a195"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a196"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#method__construct">__construct</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a197"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a198"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$xml&nbsp;</span>=&nbsp;<a href="http://www.php.net/simplexml_load_file">simplexml_load_file</a><span class="src-sym">(</span><a href="http://www.php.net/dirname">dirname</a><span class="src-sym">(</span>__FILE__<span class="src-sym">)</span>.<span class="src-str">'/data/ArQuery.xml'</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a199"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a200"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$xml</span><span class="src-sym">-&gt;</span><span class="src-id">xpath</span><span class="src-sym">(</span><span class="src-str">&quot;//preg_replace[@function='__construct']/pair&quot;</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a201"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">as&nbsp;</span><span class="src-var">$pair</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{&nbsp;</span></div></li>
<li><div class="src-line"><a name="a202"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a203"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_lexPatterns</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$pair</span><span class="src-sym">-&gt;</span><span class="src-id">search</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a204"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/array_push">array_push</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_lexReplacements</span><span class="src-sym">,&nbsp;</span>(string)<span class="src-var">$pair</span><span class="src-sym">-&gt;</span><span class="src-id">replace</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a205"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a206"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a207"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a208"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a209"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;value&nbsp;for&nbsp;$_fields&nbsp;array</span></div></li>
<li><div class="src-line"><a name="a210"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a211"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc-var">$arrConfig&nbsp;</span><span class="src-doc">Name&nbsp;of&nbsp;the&nbsp;fields&nbsp;that&nbsp;SQL&nbsp;statement&nbsp;will&nbsp;search</span></div></li>
<li><div class="src-line"><a name="a212"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;them&nbsp;(in&nbsp;array&nbsp;format&nbsp;where&nbsp;items&nbsp;are&nbsp;those</span></div></li>
<li><div class="src-line"><a name="a213"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fields&nbsp;names)</span></div></li>
<li><div class="src-line"><a name="a214"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a215"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a216"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a217"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a218"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodsetArrFields">setArrFields</a><span class="src-sym">(</span><span class="src-var">$arrConfig</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a219"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a220"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_array">is_array</a><span class="src-sym">(</span><span class="src-var">$arrConfig</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a221"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Get&nbsp;_fields&nbsp;array</span></div></li>
<li><div class="src-line"><a name="a222"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields&nbsp;</span>=&nbsp;<span class="src-var">$arrConfig</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a223"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a224"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a225"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a226"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a227"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a228"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a229"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;value&nbsp;for&nbsp;$_fields&nbsp;array</span></div></li>
<li><div class="src-line"><a name="a230"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a231"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$strConfig&nbsp;</span><span class="src-doc">Name&nbsp;of&nbsp;the&nbsp;fields&nbsp;that&nbsp;SQL&nbsp;statement&nbsp;will&nbsp;search</span></div></li>
<li><div class="src-line"><a name="a232"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;them&nbsp;(in&nbsp;string&nbsp;format&nbsp;using&nbsp;comma&nbsp;as&nbsp;delimated)</span></div></li>
<li><div class="src-line"><a name="a233"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a234"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a235"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a236"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a237"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodsetStrFields">setStrFields</a><span class="src-sym">(</span><span class="src-var">$strConfig</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a238"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a239"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_string">is_string</a><span class="src-sym">(</span><span class="src-var">$strConfig</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a240"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Get&nbsp;_fields&nbsp;array</span></div></li>
<li><div class="src-line"><a name="a241"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">','</span><span class="src-sym">,&nbsp;</span><span class="src-var">$strConfig</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a242"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a243"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a244"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a245"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a246"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a247"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a248"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;$mode&nbsp;propority&nbsp;value&nbsp;that&nbsp;refer&nbsp;to&nbsp;search&nbsp;mode</span></div></li>
<li><div class="src-line"><a name="a249"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;[0&nbsp;for&nbsp;OR&nbsp;logic&nbsp;|&nbsp;1&nbsp;for&nbsp;AND&nbsp;logic]</span></div></li>
<li><div class="src-line"><a name="a250"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a251"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$mode&nbsp;</span><span class="src-doc">Setting&nbsp;value&nbsp;to&nbsp;be&nbsp;saved&nbsp;in&nbsp;the&nbsp;$mode&nbsp;propority</span></div></li>
<li><div class="src-line"><a name="a252"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a253"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a254"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a255"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a256"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodsetMode">setMode</a><span class="src-sym">(</span><span class="src-var">$mode</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a257"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a258"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/in_array">in_array</a><span class="src-sym">(</span><span class="src-var">$mode</span><span class="src-sym">,&nbsp;</span><span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'0'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'1'</span><span class="src-sym">)))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a259"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Set&nbsp;search&nbsp;mode&nbsp;[0&nbsp;for&nbsp;OR&nbsp;logic&nbsp;|&nbsp;1&nbsp;for&nbsp;AND&nbsp;logic]</span></div></li>
<li><div class="src-line"><a name="a260"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>=&nbsp;<span class="src-var">$mode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a261"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a262"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a263"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a264"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a265"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a266"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a267"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Getting&nbsp;$mode&nbsp;propority&nbsp;value&nbsp;that&nbsp;refer&nbsp;to&nbsp;search&nbsp;mode</span></div></li>
<li><div class="src-line"><a name="a268"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;[0&nbsp;for&nbsp;OR&nbsp;logic&nbsp;|&nbsp;1&nbsp;for&nbsp;AND&nbsp;logic]</span></div></li>
<li><div class="src-line"><a name="a269"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a270"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc">Value&nbsp;of&nbsp;$mode&nbsp;properity</span></div></li>
<li><div class="src-line"><a name="a271"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a272"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a273"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetMode">getMode</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a274"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a275"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Get&nbsp;search&nbsp;mode&nbsp;value&nbsp;[0&nbsp;for&nbsp;OR&nbsp;logic&nbsp;|&nbsp;1&nbsp;for&nbsp;AND&nbsp;logic]</span></div></li>
<li><div class="src-line"><a name="a276"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a277"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a278"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a279"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a280"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Getting&nbsp;values&nbsp;of&nbsp;$_fields&nbsp;Array&nbsp;in&nbsp;array&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a281"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a282"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">Value&nbsp;of&nbsp;$_fields&nbsp;array&nbsp;in&nbsp;Array&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a283"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a284"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a285"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetArrFields">getArrFields</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a286"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a287"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$fields&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a288"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a289"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$fields</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a290"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a291"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a292"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a293"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Getting&nbsp;values&nbsp;of&nbsp;$_fields&nbsp;array&nbsp;in&nbsp;String&nbsp;format&nbsp;(comma&nbsp;delimated)</span></div></li>
<li><div class="src-line"><a name="a294"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a295"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">Values&nbsp;of&nbsp;$_fields&nbsp;array&nbsp;in&nbsp;String&nbsp;format&nbsp;(comma&nbsp;delimated)</span></div></li>
<li><div class="src-line"><a name="a296"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a297"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a298"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetStrFields">getStrFields</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a299"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a300"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$fields&nbsp;</span>=&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">','</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a301"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a302"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$fields</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a303"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a304"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a305"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a306"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Build&nbsp;WHERE&nbsp;section&nbsp;of&nbsp;the&nbsp;SQL&nbsp;statement&nbsp;using&nbsp;defind&nbsp;lex's&nbsp;rules,&nbsp;search</span></div></li>
<li><div class="src-line"><a name="a307"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;mode&nbsp;[AND&nbsp;|&nbsp;OR],&nbsp;and&nbsp;handle&nbsp;also&nbsp;phrases&nbsp;(inclosed&nbsp;by&nbsp;&quot;&quot;)&nbsp;using&nbsp;normal</span></div></li>
<li><div class="src-line"><a name="a308"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;LIKE&nbsp;condition&nbsp;to&nbsp;match&nbsp;it&nbsp;as&nbsp;it&nbsp;is.</span></div></li>
<li><div class="src-line"><a name="a309"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a310"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;that&nbsp;user&nbsp;search&nbsp;for&nbsp;in&nbsp;the&nbsp;database&nbsp;table</span></div></li>
<li><div class="src-line"><a name="a311"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a312"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">The&nbsp;WHERE&nbsp;section&nbsp;in&nbsp;SQL&nbsp;statement</span></div></li>
<li><div class="src-line"><a name="a313"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(MySQL&nbsp;database&nbsp;engine&nbsp;format)</span></div></li>
<li><div class="src-line"><a name="a314"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a315"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a316"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWhereCondition">getWhereCondition</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a317"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a318"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sql&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a319"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a320"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//$arg&nbsp;=&nbsp;mysql_real_escape_string($arg);</span></div></li>
<li><div class="src-line"><a name="a321"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$search&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">&quot;\\&quot;</span><span class="src-sym">,&nbsp;&nbsp;</span><span class="src-str">&quot;\x00&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\n&quot;</span><span class="src-sym">,&nbsp;&nbsp;</span><span class="src-str">&quot;\r&quot;</span><span class="src-sym">,&nbsp;&nbsp;</span><span class="src-str">&quot;'&quot;</span><span class="src-sym">,&nbsp;&nbsp;</span><span class="src-str">'&quot;'</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\x1a&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a322"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$replace&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">&quot;\\\\&quot;</span><span class="src-sym">,</span><span class="src-str">&quot;\\0&quot;</span><span class="src-sym">,</span><span class="src-str">&quot;\\n&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\\r&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\'&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-str">'\&quot;'</span><span class="src-sym">,&nbsp;</span><span class="src-str">&quot;\\Z&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a323"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-var">$search</span><span class="src-sym">,&nbsp;</span><span class="src-var">$replace</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a324"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a325"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Check&nbsp;if&nbsp;there&nbsp;are&nbsp;phrases&nbsp;in&nbsp;$arg&nbsp;should&nbsp;handle&nbsp;as&nbsp;it&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a326"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$phrase&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">&quot;\&quot;&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a327"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a328"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/count">count</a><span class="src-sym">(</span><span class="src-var">$phrase</span><span class="src-sym">)&nbsp;</span>&gt;&nbsp;<span class="src-num">2</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a329"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Re-init&nbsp;$arg&nbsp;variable</span></div></li>
<li><div class="src-line"><a name="a330"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;(It&nbsp;will&nbsp;contain&nbsp;the&nbsp;rest&nbsp;of&nbsp;$arg&nbsp;except&nbsp;phrases).</span></div></li>
<li><div class="src-line"><a name="a331"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a332"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a333"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">for&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i&nbsp;</span>&lt;&nbsp;<a href="http://www.php.net/count">count</a><span class="src-sym">(</span><span class="src-var">$phrase</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i</span>++<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a334"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$subPhrase&nbsp;</span>=&nbsp;<span class="src-var">$phrase</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a335"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>%&nbsp;<span class="src-num">2&nbsp;</span>==&nbsp;<span class="src-num">0&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$subPhrase&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a336"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Re-build&nbsp;$arg&nbsp;variable&nbsp;after&nbsp;restricting&nbsp;phrases</span></div></li>
<li><div class="src-line"><a name="a337"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>.=&nbsp;<span class="src-var">$subPhrase</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a338"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">elseif&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>%&nbsp;<span class="src-num">2&nbsp;</span>==&nbsp;<span class="src-num">1&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$subPhrase&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a339"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Handle&nbsp;phrases&nbsp;using&nbsp;reqular&nbsp;LIKE&nbsp;matching&nbsp;in&nbsp;MySQL</span></div></li>
<li><div class="src-line"><a name="a340"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">wordCondition</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordLike">getWordLike</a><span class="src-sym">(</span><span class="src-var">$subPhrase</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a341"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a342"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a343"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a344"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a345"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Handle&nbsp;normal&nbsp;$arg&nbsp;using&nbsp;lex's&nbsp;and&nbsp;regular&nbsp;expresion</span></div></li>
<li><div class="src-line"><a name="a346"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$words&nbsp;</span>=&nbsp;<a href="http://www.php.net/preg_split">preg_split</a><span class="src-sym">(</span><span class="src-str">'/\s+/'</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/trim">trim</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a347"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a348"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$words&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a349"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//if&nbsp;(is_numeric($word)&nbsp;||&nbsp;strlen($word)&nbsp;&gt;&nbsp;2)&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a350"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Take&nbsp;off&nbsp;all&nbsp;the&nbsp;punctuation</span></div></li>
<li><div class="src-line"><a name="a351"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//$word&nbsp;=&nbsp;preg_replace(&quot;/\p{P}/&quot;,&nbsp;'',&nbsp;$word);</span></div></li>
<li><div class="src-line"><a name="a352"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$exclude&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'('</span><span class="src-sym">,&nbsp;</span><span class="src-str">')'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'['</span><span class="src-sym">,&nbsp;</span><span class="src-str">']'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'{'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'}'</span><span class="src-sym">,&nbsp;</span><span class="src-str">','</span><span class="src-sym">,&nbsp;</span><span class="src-str">';'</span><span class="src-sym">,&nbsp;</span><span class="src-str">':'</span><span class="src-sym">,&nbsp;</span></div></li>
<li><div class="src-line"><a name="a353"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-str">'?'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'!'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'،'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'؛'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'؟'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a354"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$word&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-var">$exclude</span><span class="src-sym">,&nbsp;</span><span class="src-str">''</span><span class="src-sym">,&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a355"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a356"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">wordCondition</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordRegExp">getWordRegExp</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a357"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//}</span></div></li>
<li><div class="src-line"><a name="a358"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a359"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a360"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-sym">!</span><span class="src-key">empty</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">wordCondition</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a361"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a362"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sql&nbsp;</span>=&nbsp;<span class="src-str">'('&nbsp;</span>.&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">')&nbsp;OR&nbsp;('</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">wordCondition</span><span class="src-sym">)&nbsp;</span>.&nbsp;<span class="src-str">')'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a363"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">elseif&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_mode&nbsp;</span>==&nbsp;<span class="src-num">1</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a364"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sql&nbsp;</span>=&nbsp;<span class="src-str">'('&nbsp;</span>.&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">')&nbsp;AND&nbsp;('</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">wordCondition</span><span class="src-sym">)&nbsp;</span>.&nbsp;<span class="src-str">')'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a365"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a366"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a367"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a368"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$sql</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a369"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a370"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a371"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a372"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Search&nbsp;condition&nbsp;in&nbsp;SQL&nbsp;format&nbsp;for&nbsp;one&nbsp;word&nbsp;in&nbsp;all&nbsp;defind&nbsp;fields&nbsp;using</span></div></li>
<li><div class="src-line"><a name="a373"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;REGEXP&nbsp;clause&nbsp;and&nbsp;lex's&nbsp;rules</span></div></li>
<li><div class="src-line"><a name="a374"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a375"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;(one&nbsp;word)&nbsp;that&nbsp;you&nbsp;want&nbsp;to&nbsp;build&nbsp;a&nbsp;condition&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a376"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a377"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">sub&nbsp;SQL&nbsp;condition&nbsp;(for&nbsp;internal&nbsp;use)</span></div></li>
<li><div class="src-line"><a name="a378"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a379"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a380"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordRegExp">getWordRegExp</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a381"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a382"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodlex">lex</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a383"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//$sql&nbsp;=&nbsp;implode(&quot;&nbsp;REGEXP&nbsp;'$arg'&nbsp;OR&nbsp;&quot;,&nbsp;$this-&gt;_fields)&nbsp;.&nbsp;&quot;&nbsp;REGEXP&nbsp;'$arg'&quot;;</span></div></li>
<li><div class="src-line"><a name="a384"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sql&nbsp;</span>=&nbsp;<span class="src-str">'&nbsp;REPLACE('&nbsp;</span>.&nbsp;</div></li>
<li><div class="src-line"><a name="a385"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">&quot;</span><span class="src-str">,&nbsp;'ـ',&nbsp;'')&nbsp;REGEXP&nbsp;'<span class="src-var">$arg</span>'&nbsp;OR&nbsp;REPLACE(</span><span class="src-str">&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields</span><span class="src-sym">)&nbsp;</span>.&nbsp;</div></li>
<li><div class="src-line"><a name="a386"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-str">&quot;</span><span class="src-str">,&nbsp;'ـ',&nbsp;'')&nbsp;REGEXP&nbsp;'<span class="src-var">$arg</span>'</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a387"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a388"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a389"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$sql</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a390"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a391"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a392"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a393"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Search&nbsp;condition&nbsp;in&nbsp;SQL&nbsp;format&nbsp;for&nbsp;one&nbsp;word&nbsp;in&nbsp;all&nbsp;defind&nbsp;fields&nbsp;using</span></div></li>
<li><div class="src-line"><a name="a394"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;normal&nbsp;LIKE&nbsp;clause</span></div></li>
<li><div class="src-line"><a name="a395"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a396"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;(one&nbsp;word)&nbsp;that&nbsp;you&nbsp;want&nbsp;to&nbsp;build&nbsp;a&nbsp;condition&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a397"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a398"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">sub&nbsp;SQL&nbsp;condition&nbsp;(for&nbsp;internal&nbsp;use)</span></div></li>
<li><div class="src-line"><a name="a399"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a400"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a401"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordLike">getWordLike</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a402"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a403"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sql&nbsp;</span>=&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">&quot;</span><span class="src-str">&nbsp;LIKE&nbsp;'<span class="src-var">$arg</span>'&nbsp;OR&nbsp;</span><span class="src-str">&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_fields</span><span class="src-sym">)&nbsp;</span>.&nbsp;<span class="src-str">&quot;</span><span class="src-str">&nbsp;LIKE&nbsp;'<span class="src-var">$arg</span>'</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a404"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a405"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$sql</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a406"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a407"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a408"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a409"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;more&nbsp;relevant&nbsp;order&nbsp;by&nbsp;section&nbsp;related&nbsp;to&nbsp;the&nbsp;user&nbsp;search&nbsp;keywords</span></div></li>
<li><div class="src-line"><a name="a410"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a411"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;that&nbsp;user&nbsp;search&nbsp;for&nbsp;in&nbsp;the&nbsp;database&nbsp;table</span></div></li>
<li><div class="src-line"><a name="a412"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a413"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">sub&nbsp;SQL&nbsp;ORDER&nbsp;BY&nbsp;section</span></div></li>
<li><div class="src-line"><a name="a414"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Saleh&nbsp;AlMatrafe&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a415"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a416"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetOrderBy">getOrderBy</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a417"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a418"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Check&nbsp;if&nbsp;there&nbsp;are&nbsp;phrases&nbsp;in&nbsp;$arg&nbsp;should&nbsp;handle&nbsp;as&nbsp;it&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a419"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$phrase&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">&quot;\&quot;&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a420"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/count">count</a><span class="src-sym">(</span><span class="src-var">$phrase</span><span class="src-sym">)&nbsp;</span>&gt;&nbsp;<span class="src-num">2</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a421"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Re-init&nbsp;$arg&nbsp;variable&nbsp;</span></div></li>
<li><div class="src-line"><a name="a422"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;(It&nbsp;will&nbsp;contain&nbsp;the&nbsp;rest&nbsp;of&nbsp;$arg&nbsp;except&nbsp;phrases).</span></div></li>
<li><div class="src-line"><a name="a423"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a424"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">for&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i&nbsp;</span>&lt;&nbsp;<a href="http://www.php.net/count">count</a><span class="src-sym">(</span><span class="src-var">$phrase</span><span class="src-sym">)</span><span class="src-sym">;&nbsp;</span><span class="src-var">$i</span>++<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a425"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>%&nbsp;<span class="src-num">2&nbsp;</span>==&nbsp;<span class="src-num">0&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$phrase</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a426"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Re-build&nbsp;$arg&nbsp;variable&nbsp;after&nbsp;restricting&nbsp;phrases</span></div></li>
<li><div class="src-line"><a name="a427"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>.=&nbsp;<span class="src-var">$phrase</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a428"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">elseif&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>%&nbsp;<span class="src-num">2&nbsp;</span>==&nbsp;<span class="src-num">1&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$phrase</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a429"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Handle&nbsp;phrases&nbsp;using&nbsp;reqular&nbsp;LIKE&nbsp;matching&nbsp;in&nbsp;MySQL</span></div></li>
<li><div class="src-line"><a name="a430"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordOrder</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordLike">getWordLike</a><span class="src-sym">(</span><span class="src-var">$phrase</span><span class="src-sym">[</span><span class="src-var">$i</span><span class="src-sym">]</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a431"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a432"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a433"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a434"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a435"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Handle&nbsp;normal&nbsp;$arg&nbsp;using&nbsp;lex's&nbsp;and&nbsp;regular&nbsp;expresion</span></div></li>
<li><div class="src-line"><a name="a436"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$words&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">'&nbsp;'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a437"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$words&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a438"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$word&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a439"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordOrder</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-str">'CASE&nbsp;WHEN&nbsp;'&nbsp;</span>.&nbsp;</div></li>
<li><div class="src-line"><a name="a440"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodgetWordRegExp">getWordRegExp</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span>.&nbsp;</div></li>
<li><div class="src-line"><a name="a441"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-str">'&nbsp;THEN&nbsp;1&nbsp;ELSE&nbsp;0&nbsp;END'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a442"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a443"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a444"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a445"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$order&nbsp;</span>=&nbsp;<span class="src-str">'(('&nbsp;</span>.&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">')&nbsp;+&nbsp;('</span><span class="src-sym">,&nbsp;</span><span class="src-var">$wordOrder</span><span class="src-sym">)&nbsp;</span>.&nbsp;<span class="src-str">'))&nbsp;DESC'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a446"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a447"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$order</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a448"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a449"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a450"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a451"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;This&nbsp;method&nbsp;will&nbsp;implement&nbsp;various&nbsp;regular&nbsp;expressin&nbsp;rules&nbsp;based&nbsp;on</span></div></li>
<li><div class="src-line"><a name="a452"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;pre-defined&nbsp;Arabic&nbsp;lexical&nbsp;rules</span></div></li>
<li><div class="src-line"><a name="a453"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a454"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;of&nbsp;one&nbsp;word&nbsp;user&nbsp;want&nbsp;to&nbsp;search&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a455"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a456"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">Regular&nbsp;Expression&nbsp;format&nbsp;to&nbsp;be&nbsp;used&nbsp;in&nbsp;MySQL&nbsp;query&nbsp;statement</span></div></li>
<li><div class="src-line"><a name="a457"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a458"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a459"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodlex">lex</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a460"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a461"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$arg&nbsp;</span>=&nbsp;<a href="http://www.php.net/preg_replace">preg_replace</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_lexPatterns</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">_lexReplacements</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a462"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a463"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a464"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a465"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a466"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a467"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;most&nbsp;possible&nbsp;Arabic&nbsp;lexical&nbsp;forms&nbsp;for&nbsp;a&nbsp;given&nbsp;word</span></div></li>
<li><div class="src-line"><a name="a468"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a469"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$word&nbsp;</span><span class="src-doc">String&nbsp;that&nbsp;user&nbsp;search&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a470"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a471"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">list&nbsp;of&nbsp;most&nbsp;possible&nbsp;Arabic&nbsp;lexical&nbsp;forms&nbsp;for&nbsp;a&nbsp;given&nbsp;word</span></div></li>
<li><div class="src-line"><a name="a472"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a473"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a474"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodallWordForms">allWordForms</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a475"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a476"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a477"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a478"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$postfix1&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'كم'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'كن'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'نا'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'ها'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'هم'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'هن'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a479"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$postfix2&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'ين'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'ون'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'ان'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'ات'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'وا'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a480"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a481"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$len&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_strlen">mb_strlen</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a482"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a483"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">2</span><span class="src-sym">)&nbsp;</span>==&nbsp;<span class="src-str">'ال'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a484"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$word&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a485"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a486"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a487"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$word</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a488"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a489"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str1&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a490"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str2&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a491"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str3&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">3</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a492"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a493"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$last1&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a494"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$last2&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a495"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$last3&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">3</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a496"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a497"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">6&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$last3&nbsp;</span>==&nbsp;<span class="src-str">'تين'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a498"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str3</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a499"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str3&nbsp;</span>.&nbsp;<span class="src-str">'ة'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a500"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$word&nbsp;</span>.&nbsp;<span class="src-str">'ة'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a501"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a502"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a503"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">6&nbsp;</span>&amp;&amp;&nbsp;<span class="src-sym">(</span><span class="src-var">$last3&nbsp;</span>==&nbsp;<span class="src-str">'كما'&nbsp;</span>||&nbsp;<span class="src-var">$last3&nbsp;</span>==&nbsp;<span class="src-str">'هما'</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a504"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str3</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a505"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str3&nbsp;</span>.&nbsp;<span class="src-str">'كما'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a506"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str3&nbsp;</span>.&nbsp;<span class="src-str">'هما'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a507"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a508"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a509"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">5&nbsp;</span>&amp;&amp;&nbsp;<a href="http://www.php.net/in_array">in_array</a><span class="src-sym">(</span><span class="src-var">$last2</span><span class="src-sym">,&nbsp;</span><span class="src-var">$postfix2</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a510"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a511"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'ة'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a512"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'تين'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a513"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a514"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$postfix2&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$postfix</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a515"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2&nbsp;</span>.&nbsp;<span class="src-var">$postfix</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a516"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a517"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a518"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a519"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">5&nbsp;</span>&amp;&amp;&nbsp;<a href="http://www.php.net/in_array">in_array</a><span class="src-sym">(</span><span class="src-var">$last2</span><span class="src-sym">,&nbsp;</span><span class="src-var">$postfix1</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a520"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a521"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'ي'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a522"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'ك'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a523"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'كما'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a524"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span>.<span class="src-str">'هما'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a525"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a526"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$postfix1&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$postfix</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a527"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2&nbsp;</span>.&nbsp;<span class="src-var">$postfix</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a528"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a529"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a530"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a531"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">5&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$last2&nbsp;</span>==&nbsp;<span class="src-str">'ية'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a532"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a533"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a534"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a535"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a536"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">((</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">4&nbsp;</span>&amp;&amp;&nbsp;<span class="src-sym">(</span><span class="src-var">$last1&nbsp;</span>==&nbsp;<span class="src-str">'ة'&nbsp;</span>||&nbsp;<span class="src-var">$last1&nbsp;</span>==&nbsp;<span class="src-str">'ه'&nbsp;</span>||&nbsp;<span class="src-var">$last1&nbsp;</span>==&nbsp;<span class="src-str">'ت'</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a537"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;||&nbsp;<span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">5&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$last2&nbsp;</span>==&nbsp;<span class="src-str">'ات'</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a538"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a539"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a540"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1&nbsp;</span>.&nbsp;<span class="src-str">'ة'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a541"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1&nbsp;</span>.&nbsp;<span class="src-str">'ه'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a542"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1&nbsp;</span>.&nbsp;<span class="src-str">'ت'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a543"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1&nbsp;</span>.&nbsp;<span class="src-str">'ات'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a544"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a545"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a546"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$len&nbsp;</span>&gt;=&nbsp;<span class="src-num">4&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$last1&nbsp;</span>==&nbsp;<span class="src-str">'ى'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a547"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$str1&nbsp;</span>.&nbsp;<span class="src-str">'ا'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a548"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a549"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a550"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$trans&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'أ'&nbsp;</span>=&gt;&nbsp;<span class="src-str">'ا'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'إ'&nbsp;</span>=&gt;&nbsp;<span class="src-str">'ا'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'آ'&nbsp;</span>=&gt;&nbsp;<span class="src-str">'ا'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a551"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$wordForms&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a552"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$normWord&nbsp;</span>=&nbsp;<a href="http://www.php.net/strtr">strtr</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">,&nbsp;</span><span class="src-var">$trans</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a553"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$normWord&nbsp;</span>!=&nbsp;<span class="src-var">$word</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a554"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms</span><span class="src-sym">[</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$normWord</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a555"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a556"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a557"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a558"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms&nbsp;</span>=&nbsp;<a href="http://www.php.net/array_unique">array_unique</a><span class="src-sym">(</span><span class="src-var">$wordForms</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a559"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a560"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$wordForms</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a561"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a562"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a563"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a564"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;most&nbsp;possible&nbsp;Arabic&nbsp;lexical&nbsp;forms&nbsp;of&nbsp;user&nbsp;search&nbsp;keywords</span></div></li>
<li><div class="src-line"><a name="a565"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a566"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$arg&nbsp;</span><span class="src-doc">String&nbsp;that&nbsp;user&nbsp;search&nbsp;for</span></div></li>
<li><div class="src-line"><a name="a567"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a568"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">list&nbsp;of&nbsp;most&nbsp;possible&nbsp;Arabic&nbsp;lexical&nbsp;forms&nbsp;for&nbsp;given&nbsp;keywords</span></div></li>
<li><div class="src-line"><a name="a569"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a570"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a571"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodallForms">allForms</a><span class="src-sym">(</span><span class="src-var">$arg</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a572"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a573"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a574"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$words&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">'&nbsp;'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$arg</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a575"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a576"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$words&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$word</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a577"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$wordForms&nbsp;</span>=&nbsp;<a href="http://www.php.net/array_merge">array_merge</a><span class="src-sym">(</span><span class="src-var">$wordForms</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Query.html#methodallWordForms">allWordForms</a><span class="src-sym">(</span><span class="src-var">$word</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a578"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a579"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a580"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="http://www.php.net/implode">implode</a><span class="src-sym">(</span><span class="src-str">'&nbsp;'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$wordForms</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a581"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a582"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$str</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a583"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a584"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:18 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>