<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>
    RewriteEngine On
    
    RewriteCond %{HTTP_HOST} www.shebox.co$ [NC]
    RewriteRule (.*) https://shebox.co/$1 [R=301,L] 
    
    RewriteCond %{HTTPS} !on
    RewriteCond %{SERVER_PORT} !^443$
    RewriteCond %{HTTP:X-Forwarded-Proto} !https

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_URI} !(/$|\.) 
    RewriteRule (.*) %{REQUEST_URI}/ [R=301,L] 

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    #RewriteRule ^ index.php [L]
    RewriteRule .* index.php?path=$0
    
    



    AddType 'text/css; charset=UTF-8' css
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php74” package as the default “PHP” programming language.
<IfModule mime_module>
  ###########AddHandler application/x-httpd-ea-php74 .php .php7 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
