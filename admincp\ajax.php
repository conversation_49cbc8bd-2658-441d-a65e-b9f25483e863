<?php
ob_start();
include('../webset.php');
include('../session.php');
function br2nl($input) {
    return preg_replace('/<br\\s*?\/??>/i', '', $input);
}
function nlt2br($input) {
    return str_replace('<br /><br />', '<br>', $input);
}
//-----------------------------------------------------------
if (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'products_title' ){
	$sub = getAllFrom('*' , 'products_title' , '', 'ORDER BY id DESC');
	if (count($sub) > 0){
		for ($i=0; $i <= count($sub)-1 ; $i++) { 
			$mcat = '';
			$scat = '';
			$cid = 0 ;
			$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$sub[$i]['catid'].'"', 'ORDER BY id DESC');
			if (count($ch) > 0){
				$scat = $ch[0]['name'];
				$cid = $ch[0]['parent'] ;
			}else{
				$scat = 'غير معروف';
			}
			if ($cid != 0){
				$chx = getAllFrom('*' , 'category' , 'WHERE id = "'.$cid.'"', 'ORDER BY id DESC');
				if (count($chx) > 0){
					$mcat = $chx[0]['name'];
				}else{
					$mcat = 'غير معروف';
				}
			}else{
				$mcat = 'غير معروف';
			}

			if ($sub[$i]['paid'] == 0){
				$p_paid = ' <a href="products.php?do=paid&id='.$sub[$i]['id'].'" class="btn btn-sm btn-success smb">عادى</a> ';
			}else{
				$p_paid = ' <a href="products.php?do=paid&id='.$sub[$i]['id'].'" class="btn btn-sm btn-warning smb">مميز</a> ';
			}

			if ($sub[$i]['status'] == 1){
				$p_st = ' <a href="products.php?do=show_hide&id='.$sub[$i]['id'].'" class="btn btn-sm btn-success smb">معروض</a> ';
			}else{
				$p_st = ' <a href="products.php?do=show_hide&id='.$sub[$i]['id'].'" class="btn btn-sm btn-warning smb">مخفي</a> ';
			}

			echo '<tr>
					<td class="padding30">'.$sub[$i]['id'].'</td>
					<td class="padding30 mw200">'.$sub[$i]['title'].'</td>
					<td class="padding30">'.$mcat.'</td>
					<td class="padding30">'.$sub[$i]['price'].' جنية</td>
					<td class="padding30">'.$scat.'</td>
					<td class="padding30"><i class="fa fa-eye" aria-hidden="true"></i> &nbsp;'.$sub[$i]['views'].'</td>
					<td class="padding30"><i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$sub[$i]['datee']).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$sub[$i]['datee']).'</td>
					<td class="padding30">'.$p_st.$p_paid.'
					<a href="products.php?do=edit&id='.$sub[$i]['id'].'" class="btn btn-sm btn-info smb">تعديل</a> <a href="products.php?do=del&id='.$sub[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';
		}
	}

//----------------------------------------------------------------
}elseif (isset($_POST['do']) && $_POST['do'] == 'Get_Photo_From_DB' && isset($_POST['id'])){
	$id = trim(filter_var($_POST['id']   , FILTER_SANITIZE_STRING)) ;
	$photo = getAllFrom('*' , 'photos' , 'WHERE id = "'.$id.'" ', '');
	if (count($photo) > 0 ){
		//echo $ch[0]['link'];
		echo '<div class="md_img"><img src="'.$Site_URL.'/'.$photo[0]['link'].'"></div>';
	}
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'GetSubCategory' && isset($_POST['id'])){
	
	$id = filter_var($_POST['id']  , FILTER_SANITIZE_STRING) ;
	$cat = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = 0 AND id ="'.$id.'" ', '');
	if (count($cat) > 0){
		$sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent ="'.$id.'" ', 'ORDER BY orders DESC, id DESC');
		if (count($sub) > 0){
			for ($i=0; $i <= count($sub)-1 ; $i++) { 
				echo '<option value="'.$sub[$i]['id'].'">'.$sub[$i]['name'].'</option>';
			}
		}else{
			echo '<option value="0">لا يوجد قسم فرعى فى '.$cat[0]['name'].'</option>';
		}
	}
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'AddProduct' ){
	$title = trim(filter_var($_POST['title']  , FILTER_SANITIZE_STRING)) ;
	$catid = filter_var($_POST['catid']  , FILTER_SANITIZE_STRING) ;
	$descr = $_POST['descr']   ;
	$photo = filter_var($_POST['photo']  , FILTER_SANITIZE_STRING) ;
	$price = filter_var($_POST['price']  , FILTER_SANITIZE_STRING) ;
	$oldprice = filter_var($_POST['oldprice']  , FILTER_SANITIZE_STRING) ;


	$ch = getAllFrom('*' , 'products_title' , 'WHERE title = "'.$title.'" ', '');

	if (empty($title) || $title == "0"){
		echo  Show_Alert('danger' , 'يجب عليك كتابة إسم المنتج.');
	}elseif (empty($catid) || $catid == "0"){
		echo  Show_Alert('danger' , 'يجب عليك تحديد القسم.');
	}elseif (empty($descr) || $descr == "0"){
		echo  Show_Alert('danger' , 'يجب عليك كتابة وصف المنتج.');
	}else{
		$stmt = $db->prepare("INSERT INTO products_title ( title , descr , catid , photo , datee, link , price  , oldprice , seller , ship ) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6  ,:user_7  ,:user_9 ,:user_8 ,:user_10)");  
		$stmt->execute(array(
			'user_1' => $title , 
			'user_2' => nlt2br($descr) , 
			'user_3' => $catid , 
			'user_4' => $photo ,
			'user_5' => time() ,
			'user_6' => str_replace(' ' , '-' ,$title),
			'user_7' => $price ,
			'user_9' => $oldprice ,
			'user_10' => $_POST['ship'] ,
			'user_8' => $_POST['seller']
		)) ;

		echo $db->lastInsertId();
		/*
		echo '<div class="col-md-12">';
        echo  Show_Alert('success' , 'تم إضافة المنتج بنجاح. ');
        echo '</div>';
		*/
	}
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'merchants'  ){
	$sub = getAllFrom('*' , 'users' , 'WHERE type = 1 ', 'ORDER BY id DESC' );
	for ($x=0; $x <= count($sub)-1 ; $x++) { 
	                    
        if ($sub[$x]['active'] == 1 ){
			$titx = 'مفعل' ; $clsx = 'btn btn-sm btn-success smb' ;
		}else{
			$titx = 'محظور' ; $clsx = 'btn btn-sm btn-warning smb' ;	
		}

		if ($sub[$x]['type'] == 1 ){
			$tit = 'شركة' ; $cls = 'btn btn-sm btn-success smb' ;
		}else{
			$tit = 'عادية' ; $cls = 'btn btn-sm btn-success smb' ;	
		}

		if (empty($sub[$x]['cname'])){
			$cname = 'غير معروف';
		}else{
			$cname = $sub[$x]['cname'];
		}

		 
		$city = 'غير معروف';
		

		if ($sub[$x]['certified'] == 1){
			$certified = '<i style="font-style: normal;font-size: 30px;color: #0da513;" class="fa fa-check-square-o" aria-hidden="true"></i>';
		}else{
			$certified = '<i style="font-style: normal;font-size: 30px;color: #E91E63;" class="fa fa-square-o" aria-hidden="true"></i>';
		}

		$ord = 0;
		$or = getAllFrom('id' , 'orders' , 'WHERE userid = "'.$sub[$x]['id'].'" AND status = 2 ', 'ORDER BY id DESC' );
		if (count($or) > 0){
			$ord = count($or);
		}
        echo '<tr>
                
                <td class="padding30">'.$cname.'</td>
                <td class="padding30">'.$sub[$x]['fullname'].'</td>
                <td class="padding30">'.$sub[$x]['phone'].'</td>
                <td class="padding30">'.$city.'</td>
                <td class="padding30">'.$ord.' أوردر</td>
                <td class="padding30">'.$sub[$x]['adress'].'</td>
                <td class="padding30">'.date('Y/m/d' ,$sub[$x]['datetime']).'</td>
                <td class="padding30">'.$certified.'</td>
                <td class="padding30"><a href="merchants.php?do=block&id='.$sub[$x]['id'].'" class="'.$clsx.'">'.$titx.'</a> <a href="merchants.php?do=edit&id='.$sub[$x]['id'].'" class="btn btn-sm btn-primary smb">تعديل</a> <a href="merchants.php?do=del&id='.$sub[$x]['id'].'" class="btn btn-sm btn-danger smb">حذف</a></td>
          	</tr>';
    }
//-----------------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'users'  ){
	$sub = getAllFrom('*' , 'users' , 'WHERE type != 1 ', 'ORDER BY id DESC' );
	for ($x=0; $x <= count($sub)-1 ; $x++) { 
	                    
        if ($sub[$x]['active'] == 1 ){
			$titx = 'مفعل' ; $clsx = 'btn btn-sm btn-success smb' ;
		}else{
			$titx = 'محظور' ; $clsx = 'btn btn-sm btn-warning smb' ;	
		}

		if ($sub[$x]['type'] == 2 ){
			$tit = 'أدمن' ; $cls = 'btn btn-sm btn-danger smb' ;
		}else{
			$tit = 'عادية' ; $cls = 'btn btn-sm btn-success smb' ;	
		}

		if (empty($sub[$x]['cname'])){
			$cname = 'غير معروف';
		}else{
			$cname = $sub[$x]['cname'];
		}
 
		$city = 'غير معروف';
		

        echo '<tr>
                <td class="padding30">'.$sub[$x]['fullname'].'</td>
                <td class="padding30">'.$sub[$x]['phone'].'</td>
                <td class="padding30">'.$city.'</td>
                <td class="padding30">'.$sub[$x]['adress'].'</td>
                <td class="padding30">'.date('Y/m/d' ,$sub[$x]['datetime']).'</td>
                <td class="padding30"><a href="#" class="'.$cls.'">'.$tit.'</a></td>
                <td class="padding30"><a href="users.php?do=block&id='.$sub[$x]['id'].'" class="'.$clsx.'">'.$titx.'</a> <a href="users.php?do=edit&id='.$sub[$x]['id'].'" class="btn btn-sm btn-primary smb">تعديل</a> <a href="users.php?do=del&id='.$sub[$x]['id'].'" class="btn btn-sm btn-danger smb">حذف</a></td>
          	</tr>';
    }
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'products' ){
	$sub = getAllFrom('*' , 'products' , '', 'ORDER BY id DESC');
	if (count($sub) > 0){
		for ($i=0; $i <= count($sub)-1 ; $i++) { 
			$prod = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$sub[$i]['titleid'].'"', 'ORDER BY id DESC');
			$mcat = '';
			$scat = '';
			$cid = 0 ;
			$ch = getAllFrom('*' , 'category' , 'WHERE id = "'.$prod[0]['catid'].'"', 'ORDER BY id DESC');
			if (count($ch) > 0){
				$scat = $ch[0]['name'];
				$cid = $ch[0]['parent'] ;
			}else{
				$scat = 'غير معروف';
			}
			if ($cid != 0){
				$chx = getAllFrom('*' , 'category' , 'WHERE id = "'.$cid.'"', 'ORDER BY id DESC');
				if (count($chx) > 0){
					$mcat = $chx[0]['name'];
				}else{
					$mcat = 'غير معروف';
				}
			}else{
				$mcat = 'غير معروف';
			}

			if ($sub[$i]['status'] == 1){
				$stus = '<a id="prod'.$sub[$i]['id'].'" onclick ="ChangeProductStatus('.$sub[$i]['id'].')" class="btn btn-success btn-sm">تمت الموافقه</a>';
			}else{
				$stus = '<a id="prod'.$sub[$i]['id'].'" onclick ="ChangeProductStatus('.$sub[$i]['id'].')" class="btn btn-warning btn-sm">فى انتظار الموافقه</a>';
			}

			$us = getAllFrom('*' , 'users' , 'WHERE id = "'.$sub[$i]['userid'].'"', 'ORDER BY id DESC');
			if (count($us) > 0){
				$usnam = $us[0]['cname'];
				$usphn = $us[0]['phone'];
				$ctyid = $us[0]['cityid'];
			}else{
				$usnam = 'غير معروف';
				$usphn = $us[0]['phone'];
				$ctyid = 0;
			}

			$ct = getAllFrom('*' , 'cities' , 'WHERE id = "'.$ctyid.'"', 'ORDER BY id DESC');
			if (count($ct) > 0){
				$cty = $ct[0]['name'];
			}else{
				$cty = 'غير معروف';
			}

			if ($sub[$i]['cost'] > 0){	
				$cost = $sub[$i]['cost'] . ' جنية';
			}else{
				$cost = 'مجاناً'	;
			}
			echo '<tr>
					<td class="padding30">'.$usnam.'</td>
					<td class="padding30">'.$usphn.'</td>
					<td class="padding30">'.$prod[0]['title'].'</td>
					<td class="padding30">'.$mcat.'</td>
					<td class="padding30">'.$scat.'</td>
					<td class="padding30">'.$cty.'</td>

					<td class="padding30">'.$sub[$i]['price'].' جنية</td>
					<td class="padding30">'.$cost.'</td>
					<td class="padding30">'.$sub[$i]['delivery'].' يوم</td>
					<td class="padding30"><i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$sub[$i]['datetime']).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$sub[$i]['datetime']).'</td>
					<td class="padding30">'.$stus.'</td>
					<td class="padding30"><a href="users_products.php?do=edit&id='.$sub[$i]['id'].'" class="btn btn-sm btn-info smb">تعديل</a> <a href="users_products.php?do=del&id='.$sub[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';
		}
	}
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'ChangeProductStatus' && isset($_SESSION['userData']) && isset($_POST['productid'])  ){
	$id = trim(filter_var($_POST['productid']  , FILTER_SANITIZE_STRING)) ;
	$ch = getAllFrom('*' , 'products' , 'WHERE id = "'.$id.'"', 'ORDER BY id DESC'); 
	if (count($ch) > 0 ){
		if ($ch[0]['status'] == 1){
			$stmt = $db->prepare("UPDATE products SET status = :var1  WHERE  id = :var0 ");  
		    $stmt->execute(array( 'var1' => 0 , 'var0' => $ch[0]['id'] )); 	
		}else{
			$stmt = $db->prepare("UPDATE products SET status = :var1  WHERE  id = :var0 ");  
		    $stmt->execute(array( 'var1' => 1 , 'var0' => $ch[0]['id'] )); 	
		}
	}
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'DataTableAll' && isset($_SESSION['userData']) && isset($_SESSION['userData']) && isset($_POST['type']) && $_POST['type'] == 'orders' ){
	$orders = getAllFrom('*' , 'new_orders' , '', 'ORDER BY id DESC');
	if (count($orders) > 0 ){
		for ($i=0; $i <= count($orders)-1 ; $i++) { 

			
			$sga1 = '';
			$sga2 = '';


			$cldt = $orders[$i]['datee'];
			$clst = $orders[$i]['status']; 



			 	if ($clst == 3){
					$stus = '<a id="prod'.$orders[$i]['id'].'" class="btn btn-info btn-sm">فى انتظار الشركة</a>';
				}elseif ($clst == 1){
					$stus = '<a id="prod'.$orders[$i]['id'].'" class="btn btn-success btn-sm">تم توصيله</a>';
				}elseif ($clst == 2){
					$stus = '<a id="prod'.$orders[$i]['id'].'" class="btn btn-danger btn-sm">تم الغاؤه</a>';
				}else{
					$stus = '<a id="prod'.$orders[$i]['id'].'" class="btn btn-warning btn-sm">فى الإنتظار </a>';
				}
				
        echo '<tr>
          <td class="padding30">'.$orders[$i]['id'].'</td> 
          <td class="padding30">'.$orders[$i]['name'].'</td> 
          <td class="padding30"><i class="fa fa-calendar-o" aria-hidden="true"></i> '.date("Y-m-d",$cldt).' <i class="fa fa-clock-o mr10" aria-hidden="true"></i> '.date("H:i",$cldt).'</td>
					<td class="padding30">'.$orders[$i]['phone'].'</td>

					<td class="padding30">'.$orders[$i]['hash'].'</td>
					<td class="padding30">'.$orders[$i]['city'].'</td>
					<td class="padding30">'.$orders[$i]['street'].' - ' .$orders[$i]['area'] . ' - '.$orders[$i]['emark'].'</td>
					<td class="padding30">'.$orders[$i]['coupon'].'</td>
					<td class="padding30"><img src="'.$orders[$i]['receipt'].'"></td>

					<td class="padding30">'.$orders[$i]['totalprice'].'</td>
					<td class="padding30">'.$orders[$i]['totalship'].'</td>
					<td class="padding30"><table class="table">
					<thead>
					<tr>
						<th scope="col">رقم المنتج</th>
						<th scope="col">اسم المنتج</th>
						<th scope="col">الفلاتر</th>
						<th scope="col">الكمية</th>
					</tr>
					</thead>
					<tbody>
					';

					$sga1 .= 'Good day, '.$orders[$i]['name'].PHP_EOL
					.'You have an order from efanda'.PHP_EOL
					.'Order : #'.$orders[$i]['hash'].PHP_EOL
					.'Phone : '.$orders[$i]['phone'].PHP_EOL
					.'Total cost : '.($orders[$i]['totalprice'] + $orders[$i]['totalship']).' SAR'.PHP_EOL
					.'Thank you.'.PHP_EOL.PHP_EOL.PHP_EOL;
					$sga1 .= 'أتمنى لكم يوم جيد يا سيدي / سيدتي '.$orders[$i]['name'].PHP_EOL
					.'لديك طلب من ايفاندا'.PHP_EOL
					.'رقم الاوردر : #'.$orders[$i]['hash'].PHP_EOL
					.'رقم الجوال : '.$orders[$i]['phone'].PHP_EOL.PHP_EOL;


					$sga2 .= 'الإسم : '.$orders[$i]['name'].PHP_EOL.PHP_EOL;
					$sga2 .= 'رقم الجوال : '.$orders[$i]['phone'].PHP_EOL.PHP_EOL;
					$sga2 .= 'المدينة : '.$orders[$i]['city'].PHP_EOL;
					$sga2 .= 'العنوان : '.$orders[$i]['street'].' - ' .$orders[$i]['area'] . ' - '.$orders[$i]['emark'].PHP_EOL.PHP_EOL;

					$products = json_decode($orders[$i]['cartproduct']);
					for($x=0; $x<=count($products)-1; $x++){

						$prod_link = '#';
						$chp = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$products[$x]->productid.'"', '');
						if(count($chp) > 0){
							$prod_link = $Site_URL.'/'.$chp[0]['link'];
						}

						$sga1 .= 'المنتج : '.$products[$x]->title.PHP_EOL;
						$sga1 .= 'رابط المنتج : '.$prod_link.PHP_EOL;
						$sga1 .= $products[$x]->filter . PHP_EOL;
						$sga1 .= 'الكمية : '.$products[$x]->count.PHP_EOL;
						$sga1 .= 'سعر المنتج : '.$chp[0]['price'].PHP_EOL;

						//$sga2 .= 'المنتج : '.$products[$x]->title.PHP_EOL;
						//$sga2 .= 'رابط المنتج : '.$prod_link.PHP_EOL;
						$sga2 .= $products[$x]->filter . PHP_EOL;
						$sga2 .= 'الكمية : '.$products[$x]->count.PHP_EOL;
						$sga2 .= 'سعر المنتج : '.$chp[0]['price'].PHP_EOL;


						echo '
						<tr>
							<th class="title" scope="row"><a href="'.$prod_link.'" target="_blank">'.$products[$x]->productid.'</a></th>
							<th class="title" scope="row"><a href="'.$prod_link.'" target="_blank">'.$products[$x]->title.'</a></th>
							<th class="title" scope="row">'.$products[$x]->filter.'</th>
							<th class="title" scope="row">'.$products[$x]->count.'</th>
							 
						</tr>
						';
					}
					$sga1 .= PHP_EOL;
					$sga1 .= 'المدينة : '.$orders[$i]['city'].PHP_EOL;
					$sga1 .= 'العنوان : '.$orders[$i]['street'].' - ' .$orders[$i]['area'] . ' - '.$orders[$i]['emark'].PHP_EOL;

					$sga1 .= 'شركة الشحن : 000000000000'.PHP_EOL;
					$sga1 .= 'تكلفة الشحن : '.$orders[$i]['totalship'].PHP_EOL;
					$sga1 .= 'طرق الدفع : 000000000000'.PHP_EOL; 
					$sga1 .= 'الإجمالي : '.($orders[$i]['totalprice'] + $orders[$i]['totalship']).' SAR'.PHP_EOL;

					$sga2 .= 'شركة الشحن : 000000000000'.PHP_EOL;
					$sga2 .= 'تكلفة الشحن : '.$orders[$i]['totalship'].PHP_EOL;
					$sga2 .= 'طرق الدفع : 000000000000'.PHP_EOL; 
					$sga2 .= 'الإجمالي : '.($orders[$i]['totalprice'] + $orders[$i]['totalship']).' SAR'.PHP_EOL;
				echo '</tbody>
				</table></td>

					<td class="padding30"><textarea class="form-control">'.$sga1.'</textarea></td>
					<td class="padding30"><textarea class="form-control">'.$sga2.'</textarea></td>
					<td class="padding30">'.$stus.'</td>
					<td class="padding30">
						<a onclick="ChangeOrderStatus('.$orders[$i]['id'].' , 0)" class="btn btn-warning btn-sm"><i class="fa fa-history" aria-hidden="true"></i></a>  
						
						<a onclick="ChangeOrderStatus('.$orders[$i]['id'].' , 1)" class="btn btn-success btn-sm"><i class="fa fa-check-square-o" aria-hidden="true"></i></a>  
						<a onclick="ChangeOrderStatus('.$orders[$i]['id'].' , 2)" class="btn btn-danger btn-sm"><i class="fa fa-times" aria-hidden="true"></i></a>  
						

					</td>

					<td class="padding30"><a href="orders.php?do=del&id='.$orders[$i]['id'].'" class="btn btn-sm btn-danger smb">حذف</a> </td>
				</tr>';


		}
	}
//-----------------------------------------------------------
}elseif (isset($_POST['action']) && $_POST['action'] == 'ChangeOrderStatus' && isset($_SESSION['userData']) && isset($_POST['orderid']) && isset($_POST['st'])  ){
	$id = trim(filter_var($_POST['orderid']  , FILTER_SANITIZE_STRING)) ;
	$st = trim(filter_var($_POST['st']  , FILTER_SANITIZE_STRING)) ;
	$ch = getAllFrom('*' , 'orders' , 'WHERE id = "'.$id.'"', 'ORDER BY id DESC'); 
	if (count($ch) > 0 ){
		if ($st < 4){
			if (isset($_POST['res'])){
				$res = trim(filter_var($_POST['res']  , FILTER_SANITIZE_STRING)) ;
				$stmt = $db->prepare("UPDATE orders SET status = :var1 , note = :var2  WHERE  id = :var0 ");  
		    	$stmt->execute(array( 'var1' => $st , 'var2' => 'سبب الإلغاء : '. $res , 'var0' => $ch[0]['id'] )); 	
			}else{
				$stmt = $db->prepare("UPDATE orders SET status = :var1  WHERE  id = :var0 ");  
		    	$stmt->execute(array( 'var1' => $st , 'var0' => $ch[0]['id'] )); 	
			}
			
		}else{
			SendNotiToPhone($id);
		}
	}
//-----------------------------------------------------------------	
}