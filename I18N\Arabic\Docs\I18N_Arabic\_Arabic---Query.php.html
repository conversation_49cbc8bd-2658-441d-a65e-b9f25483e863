<html>
<head>
<title>Docs for page Query.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: Query.php</h1>
Source Location: /Arabic/Query.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a></dt>
	<dd>This PHP class build WHERE condition for SQL statement using MySQL REGEXP and  Arabic lexical  rules</dd>
</div><br /><br />

<h2>Page Details:</h2>
----------------------------------------------------------------------<br /><br /><p>Copyright (c) 2006-2016 Khaled Al-Sham'aa.</p><p>http://www.ar-php.org</p><p>PHP Version 5</p><p>----------------------------------------------------------------------</p><p>LICENSE</p><p>This program is open source product; you can redistribute it and/or  modify it under the terms of the GNU Lesser General Public License (LGPL)  as published by the Free Software Foundation; either version 3  of the License, or (at your option) any later version.</p><p>This program is distributed in the hope that it will be useful,  but WITHOUT ANY WARRANTY; without even the implied warranty of  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the  GNU Lesser General Public License for more details.</p><p>You should have received a copy of the GNU Lesser General Public License  along with this program.  If not, see &lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</p><p>----------------------------------------------------------------------</p><p>Class Name: Arabic Queary Class</p><p>Filename: Query.php</p><p>Original  Author(s): Khaled Al-Sham'aa &lt;<EMAIL>&gt;</p><p>Purpose:  Build WHERE condition for SQL statement using MySQL REGEXP and            Arabic lexical  rules</p><p>----------------------------------------------------------------------</p><p>Arabic Queary Class</p><p>PHP class build WHERE condition for SQL statement using MySQL REGEXP and  Arabic lexical  rules.</p><p>With the exception of the Qur'an and pedagogical texts, Arabic is generally  written without vowels or other graphic symbols that indicate how a word is  pronounced. The reader is expected to fill these in from context. Some of the  graphic symbols include sukuun, which is placed over a consonant to indicate that  it is not followed by a vowel; shadda, written over a consonant to indicate it is  doubled; and hamza, the sign of the glottal stop, which can be written above or  below (alif) at the beginning of a word, or on (alif), (waaw), (yaa'),  or by itself on the line elsewhere. Also, common spelling differences regularly  appear, including the use of (haa') for (taa' marbuuta) and (alif maqsuura)  for (yaa'). These features of written Arabic, which are also seen in Hebrew as  well as other languages written with Arabic script (such as Farsi, Pashto, and  Urdu), make analyzing and searching texts quite challenging. In addition, Arabic  morphology and grammar are quite rich and present some unique issues for  information retrieval applications.</p><p>There are essentially three ways to search an Arabic text with Arabic queries:  literal, stem-based or root-based.</p><p>A literal search, the simplest search and retrieval method, matches documents  based on the search terms exactly as the user entered them. The advantage of this  technique is that the documents returned will without a doubt contain the exact  term for which the user is looking. But this advantage is also the biggest  disadvantage: many, if not most, of the documents containing the terms in  different forms will be missed. Given the many ambiguities of written Arabic, the  success rate of this method is quite low. For example, if the user searches  for (kitaab, book), he or she will not find documents that only  contain (`al-kitaabu, the book).</p><p>Stem-based searching, a more complicated method, requires some normalization of  the original texts and the queries. This is done by removing the vowel signs,  unifying the hamza forms and removing or standardizing the other signs.  Additionally, grammatical affixes and other constructions which attach directly  to words, such as conjunctions, prepositions, and the definite article, should be  identified and removed. Finally, regular and irregular plural forms need to be  identified and reduced to their singular forms. Performing this type of stemming  leads to more successful searches, but can be problematic due to over-generation  or incorrect generation of stems.</p><p>A third method for searching Arabic texts is to index and search for the root  forms of each word. Since most verbs and nouns in Arabic are derived from  triliteral (or, rarely, quadriliteral) roots, identifying the underlying root of  each word theoretically retrieves most of the documents containing a given search  term regardless of form. However, there are some significant challenges with this  approach. Determining the root for a given word is extremely difficult, since it  requires a detailed morphological, syntactic and semantic analysis of the text to  fully disambiguate the root forms. The issue is complicated further by the fact  that not all words are derived from roots. For example, loan words (words  borrowed from another language) are not based on root forms, although there are  even exceptions to this rule. For example, some loans that have a structure  similar to triliteral roots, such as the English word film, are handled  grammatically as if they were root-based, adding to the complexity of this type  of search. Finally, the root can serve as the foundation for a wide variety of  words with related meanings. The root (k-t-b) is used for many words related  to writing, including (kataba, to write), (kitaab, book), (maktab,  office), and (kaatib, author). But the same root is also used for regiment/  battalion, (katiiba). As a result, searching based on root forms results in  very high recall, but precision is usually quite low.</p><p>While search and retrieval of Arabic text will never be an easy task, relying on  linguistic analysis tools and methods can help make the process more successful.  Ultimately, the search method you choose should depend on how critical it is to  retrieve every conceivable instance of a word or phrase and the resources you  have to process search returns in order to determine their true relevance.</p><p>Source: Volume 13 Issue 7 of MultiLingual Computing &amp;  Technology published by MultiLingual Computing, Inc., 319 North First Ave.,  Sandpoint, Idaho, USA, ************, Fax: ************.</p><p>Example:  <ol><li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'Query'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbuser&nbsp;</span>=&nbsp;<span class="src-str">'root'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbpwd&nbsp;</span>=&nbsp;<span class="src-str">''</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbname&nbsp;</span>=&nbsp;<span class="src-str">'test'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;try&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbh&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id">PDO</span><span class="src-sym">(</span><span class="src-str">'mysql:host=localhost;dbname='</span>.<span class="src-var">$dbname</span><span class="src-sym">,&nbsp;</span><span class="src-var">$dbuser</span><span class="src-sym">,&nbsp;</span><span class="src-var">$dbpwd</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Set&nbsp;the&nbsp;error&nbsp;reporting&nbsp;attribute</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbh</span><span class="src-sym">-&gt;</span><span class="src-id">setAttribute</span><span class="src-sym">(</span><span class="src-id">PDO</span><span class="src-sym">::</span><span class="src-id">ATTR_ERRMODE</span><span class="src-sym">,&nbsp;</span><span class="src-id">PDO</span><span class="src-sym">::</span><span class="src-id">ERRMODE_EXCEPTION</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbh</span><span class="src-sym">-&gt;</span><a href="http://www.php.net/exec">exec</a><span class="src-sym">(</span><span class="src-str">&quot;SET&nbsp;NAMES&nbsp;'utf8'&quot;</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$_GET</span><span class="src-sym">[</span><span class="src-str">'keyword'</span><span class="src-sym">]&nbsp;</span>!=&nbsp;<span class="src-str">''</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$keyword&nbsp;</span>=&nbsp;<span class="src-sym">@</span><span class="src-var">$_GET</span><span class="src-sym">[</span><span class="src-str">'keyword'</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$keyword&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-str">'\&quot;'</span><span class="src-sym">,&nbsp;</span><span class="src-str">'&quot;'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$keyword</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setStrFields</span><span class="src-sym">(</span><span class="src-str">'headline'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setMode</span><span class="src-sym">(</span><span class="src-var">$_GET</span><span class="src-sym">[</span><span class="src-str">'mode'</span><span class="src-sym">]</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$strCondition&nbsp;</span>=&nbsp;<span class="src-var">$Arabic</span><span class="src-sym">-&gt;</span><span class="src-id">getWhereCondition</span><span class="src-sym">(</span><span class="src-var">$keyword</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$strCondition&nbsp;</span>=&nbsp;<span class="src-str">'1'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$StrSQL&nbsp;</span>=&nbsp;<span class="src-str">&quot;</span><span class="src-str">SELECT&nbsp;`headline`&nbsp;FROM&nbsp;`aljazeera`&nbsp;WHERE&nbsp;<span class="src-var">$strCondition</span></span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$dbh</span><span class="src-sym">-&gt;</span><span class="src-id">query</span><span class="src-sym">(</span><span class="src-var">$StrSQL</span><span class="src-sym">)&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$row</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$headline&nbsp;</span>=&nbsp;<span class="src-var">$row</span><span class="src-sym">[</span><span class="src-str">'headline'</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$i</span>++<span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$i&nbsp;</span>%&nbsp;<span class="src-num">2&nbsp;</span>==&nbsp;<span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bg&nbsp;</span>=&nbsp;<span class="src-str">&quot;#f0f0f0&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$bg&nbsp;</span>=&nbsp;<span class="src-str">&quot;#ffffff&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;tr&nbsp;bgcolor=\&quot;<span class="src-var">$bg</span>\&quot;&gt;&lt;td&gt;<span class="src-var">$headline</span>&lt;/td&gt;&lt;/tr&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Close&nbsp;the&nbsp;databse&nbsp;connection</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$dbh&nbsp;</span>=&nbsp;<span class="src-id">null</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span>catch&nbsp;<span class="src-sym">(</span><span class="src-id">PDOException&nbsp;</span><span class="src-var">$e</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-var">$e</span><span class="src-sym">-&gt;</span><span class="src-id">getMessage</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
</ol></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_I18N_Arabic__ArabicQuery.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:17 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
