<?php


if(isset($urlparts[1]) && $urlparts[1] == 'confirm'){
    $Title_page = 'تأكيد الشراء';
}elseif(isset($urlparts[1]) && $urlparts[1] == 'ok'){
    $Title_page = 'تم تأكيد الأوردر';
}else{
    $Title_page = 'سلة المشتريات';
}

include('header.php'); 
include('navbar.php'); 

$all_cart = $_SESSION['UserCart'] ;

$cities = getAllFrom('*','cities' ,'WHERE status = 1 ', '');
$jcities = json_encode($cities);

$ProductCart = [];


echo '<style>.form-control{margin-bottom: 10px;  width: 90%;   margin-right: 11px;  border-radius: 20px;}</style>';

if(isset($urlparts[1]) && $urlparts[1] == 'confirm'){
    GetBreadcrumb(array($Title_page));
    echo '
    <div id="ModelAddress" class="modal fade" data-backdrop="static" role="dialog">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
            <div class="modal-body">
                <div class="CartDataClose">
                    <h2>إضافة عنوان جديد</h2>
                    <a data-dismiss="modal"><i class="fa fa-window-close" aria-hidden="true"></i></a>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label>الإسم بالكامل</label>
                        <input type="text" class="form-control" name="add_name" id="add_name" placeholder="اكتب اسمك">
                    </div>
                    <div class="col-md-12">
                        <label>رقم الموبايل</label>
                        <input type="text" class="form-control" name="add_phone" id="add_phone" placeholder="رقم الموبايل">
                    </div>
                    <div class="col-md-12">
                        <label>العنوان بالتفصيل</label>
                        <textarea class="form-control" name="add_address" id="add_address" rows="5" placeholder="العنوان بالتفصيل"></textarea>
                    </div>
                    <div class="col-md-12">
                        <a class="btn btn-md btn-success" onclick="AddAddress()">حفظ العنوان</a>
                        <a data-dismiss="modal" class="btn btn-md btn-danger">إغلاق</a> 
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
    <div class="cantercart">
        <div class="col-md-12 padding0">
            <h1 class="filter-title"><span class="cat-title"><strong>'.$Title_page.'</strong></span></h1>
        </div>
        ';
        if(count($all_cart) > 0){
            $c= 0;
            $total = 0 ;
            $total_ship = $ShipData[0]['cost'];
            for ($i=0; $i <= count($all_cart)-1 ; $i++) { 
                $product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$all_cart[$i]['productid'].'" ', '');
                if (count($product) > 0){
                    $filter = $all_cart[$i]['filter'] == '' ? '---' : str_replace('&','<br>',str_replace(' ' ,'' , $all_cart[$i]['filter']));
                    $c = $c + 1 ;
                    $total = $total + $product[0]['price']; 

                    array_push($ProductCart , array(
                        'productid' => $all_cart[$i]['productid'],
                        'title' => $all_cart[$i]['title'],
                        'filter' => $all_cart[$i]['filter'],
                        'count' => $all_cart[$i]['count'],
                    ));
                }
            } 
        echo '
        <div class="container no-center-mobile">
        
        <div class="col-md-8"> 
        <div class="box-checkout">
          <div class="boxcart1">
            <h3 class="cart-h3"><img src="'.$Site_URL.'/img/curriculum-vitae.png" alt="card info"> بيانات المستلم  </h3> 
            <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-control-label">الأسم</label>
                            <input type="text" id="um1" placeholder="الأسم" value="'.$FullName.'" class="form-control">
                        </div> 
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-control-label">رقم الواتس اب</label>
                            <input type="text" id="um2" placeholder="رقم الواتس اب" value="'.$UserPhone.'" class="form-control">
                        </div> 
                    </div>
                  </div> 
                </div> 


                <div class="boxcart1">
                  <h3 class="cart-h3"><img src="'.$Site_URL.'/img/placeholder.png" alt="card info"> عنوان المستلم  </h3> 
                  <div class="row">

                    <div class="col-md-6">
                      <div class="form-group">
                          <label class="form-control-label">المدينة</label>
                          <select onchange="ChangeCity()" id="cty1" class="form-control">
                              <option value="0">إختر المدينة</option>
                              ';
                              for ($i=0; $i <= count($cities)-1 ; $i++) { 
                                  if($cities[$i]['parent'] == 0){
                                      echo '<option value="'.$cities[$i]['id'].'">'.$cities[$i]['name'].'</option>';
                                  }
                              }
                          echo '
                          </select> 
                      </div> 
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label class="form-control-label">الحي</label>
                          <select id="cty2" class="form-control">
                              <option value="0">إختر الحي</option> 
                          </select> 
                      </div> 
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label class="form-control-label">الشارع</label>
                          <input type="text" id="cty3" placeholder="الشارع" class="form-control">
                      </div> 
                  </div>
                  <div class="col-md-6">
                      <div class="form-group">
                          <label class="form-control-label">وصف البيت (اختياري)</label>
                          <input type="text" id="cty4" placeholder="وصف البيت" class="form-control">
                      </div> 
                  </div>


                      </div> 
                    </div> 


                    <div class="boxcart1">
                      <h3 class="cart-h3"><img src="'.$Site_URL.'/img/courier.png" alt="card info"> أختار شركة الشحن </h3> 
                      <div class="row">
                                <div class="col-md-12">
                                  <br>
                                  <div class="form-group">
                                  ';
                                      for ($i=0; $i <= count($ShipData)-1 ; $i++) { 
                                          $rch = '';
                                          if($i == 0){ $rch = 'checked'; }
                                          echo ' 
                                          <div class="radio">
                                              <label class="srb"><input value="'.$ShipData[$i]['cost'].'" onchange="SetShip()" type="radio" name="optradio" '.$rch.'>'.$ShipData[$i]['name'].'<span>'.$ShipData[$i]['cost'].' ريال</span></label>
                                          </div>
                                          ';
                                      }
                                  echo ' 
                                      <div id="r"></div>
                                  </div>
          
                              </div>
                          </div> 
                        </div> 

                          <div class="boxcart1">
                            <h3 class="cart-h3"><img src="'.$Site_URL.'/img/credit-card.png" alt="card info"> أختيارات الدفع </h3> 
                            <div class="row">
                              <div class="col-md-12">
                                       

                                <div data-id="1" class="iconss active">
                                
                                    <span><i class="fa fa-check-square-o" aria-hidden="true"></i></span>
                                    
                                    <div class="iconss-p">  <p> تحويل بنكي </p>    </div>
                                </div>    
        
                                <div data-id="2" class="iconss dno">
                                    <span><i class="fa fa-check-square-o" aria-hidden="true"></i></span> 
                                    <div class="iconss-p">  <p> الدفع عند الإستلام </p>    </div>
                                </div> 
        
                                <div class="iconss_data iconss_data_1 sg">
                                    <div id="bank_acc_info">
                                        <fieldset id="owner_info" class="form-group field-list">
                                            <div>
                                                <img src="'.$Site_URL.'/img/card-info.svg" alt="card info"> 
                                                <ul>
                                                    <li>
                                                  <li>  <div><span>اسم البنك</span> <b>الراجحي</b></div><li>
                                                  <li>  <div><span>اسم الحساب</span> <b>مؤسسه اكنان</b></div><li>
                                                  <li>  <div><span>رقم الحساب</span> <b> ***************  </b></div><li>
                                                  <li>  <div><span>ايبان</span> <b> SA4980000***************  </b></div><li>
                                                  <p>يجب ارفاق صوره أيصال التحويل</p>
                                             </ul>
                                            </div>
                                        </fieldset>
                                    </div> 
        
                                    <div class="col-md-12">
                                        <div class="profile-photo-div" id="profile-photo-div">
                                        <div class="profile-img-div" id="profile-img-div">
                                            <div id="loader"></div><img id="profile-img" src="'.$Site_URL.'/img/edaa122766896a80abb465224d4e5293.jpg"/>
                                            <input id="x-position" type="range" name="x-position" value="0" min="0"/>
                                            <input id="y-position" type="range" name="y-position" value="0" min="0"/>
                                        </div>
                                        <div class="profile-buttons-div">
                                            <div class="profile-img-input" id="profile-img-input">
                                            <label class="button" id="change-photo-label" for="change-photo">الرجاء ارفاق صورة الايصال</label>
                                            <input id="change-photo" name="change-photo" type="file" style="display: none;" accept="image/*"/>
                                            </div>
                                            <div class="profile-img-confirm" id="profile-img-confirm" style="display: none;">
                                            <div class="button half green" id="save-img"><i class="fa fa-check" aria-hidden="true"></i></div>
                                            <div class="button half red" id="cancel-img"><i class="fa fa-remove" aria-hidden="true"></i></div>
                                            </div>
                                        </div>
                                        </div>
                                        <div class="error" id="error">min sizes 400*400px</div>
                                        <canvas id="croppedPhoto" width="400" height="400"></canvas>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-control-label">اسم صاحب الحساب</label>
                                        <input type="text" id="imus" placeholder="اسم صاحب الحساب" class="form-control">
                                    </div>

                                </div>
                                <div class="iconss_data iconss_data_2"></div>
                 
                            </div>
                
                                    </div>
                                </div> 

                           <div class="boxcart1">
  <div style="border: 1px solid #ccc; padding: 15px; border-radius: 8px; background-color: #f9f9f9;">
    
    <div style="margin-bottom: 10px;">
      <strong style="color: #007BFF; font-size: 16px;">تنبيه مهم قبل إتمام الطلب</strong>
    </div>

    <div style="color: #555; font-size: 14px; line-height: 1.6; margin-bottom: 15px;">
      متجر شي بوكس يعمل كوسيط تجاري بين العميل والمورد.<br>
      ويضمن المتجر وصول المنتج إلى العميل كما هو موضح في صفحة الطلب من حيث الوصف والمواصفات.<br>
      كما يضمن للعميل عملية الاستبدال والاسترجاع وفقًا لسياسة المتجر.
    </div>

    <label style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding: 10px; background-color: #eee; border-radius: 5px; font-size: 14px; color: #333; cursor: pointer;">
      <span>أوافق</span>
      <input type="checkbox" required />
    </label>

  </div>
</div>
















                        


                    
                </div>
            </div>


            
            ';

            /*
            $UserAdressArray = array();
            try{
                $uads = json_decode($UserAdress);
                if(is_array($uads) && count($uads) > 0){
                    for($i=0; $i<=count($uads)-1; $i++){
                        array_push($UserAdressArray , $uads[$i]);
                    }
                }
            } catch (Exception $e) { }

            if(count($UserAdressArray) > 0){
                for($i=0; $i<=count($UserAdressArray)-1; $i++){
                    $checked = '';
                    if($i==0){
                        $checked = 'checked';
                    }
                    echo '
                    <div class="panel address-box">
                        <div class="panel-heading">
                            <h3 class="panel-title">
                                <input type="radio" name="radio_address" value="'.$i.'" '.$checked.'>
                                عنوان '.($i+1).'
                                <span><a onclick="RemoveAddress('.$i.')"><i class="fa fa-trash-o" aria-hidden="true"></i></a></span>
                                <input type="hidden" class="ad_name" value="'.$uads[$i]->name.'">
                                <input type="hidden" class="ad_phone" value="'.$uads[$i]->phone.'">
                                <input type="hidden" class="ad_address" value="'.$uads[$i]->address.'">
                            </h3>
                        </div>
                        <div class="panel-body">
                            <h4>'. $uads[$i]->name.'</h4>
                            <h4>'. $uads[$i]->phone.'</h4>
                            <h4>'. $uads[$i]->address.'</h4>
                        </div>
                    </div>
                    ';
                }
            }else{
                echo '<br>'.Show_Alert('warning' , 'لا يوجد لديك عناوين للشحن.');
            }

            */

        echo '
           
        <div class="col-md-4">
            <div class="confirm-box">
                <h3>ملخص الطلبات</h3>
                <h4> السعر الإجمالى : <span><b id="totalprice">'.$total.'</b> ريال</span></h4>
                <h4> تكلفة الشحن : <span><b id="totalship">'.$total_ship.'</b> ريال</span></h4>
                <hr>
                <h4> كوبون الخصم : <span><b id="coupon">0</b> ريال</span></h4>

                <div class="input-group coupon_data">
                    <input type="text" class="form-control ht34" id="coupon_code" placeholder="كوبون الخصم">
                    <span class="input-group-btn">
                        <button onclick="addCoupon()" class="btn btn-default" type="button">تطبيق</button>
                    </span>
                </div>
                <hr>
                <h4> اجمالى قيمة الطلب : <span><b id="totalcash">'.($total+$total_ship).'</b> ريال</span></h4>
                <a id="btn1" class="btn btn-md btn-success" onclick="ConfirmToAddOrder()">تأكيد الطلب</a>
            </div>
        </div>
            </div>
        ';
        }
        if(count($all_cart) <= 0){
            echo '
            <div class="col-md-12">
                '.Show_Alert('warning' , 'سلة المشتريات فارغة').'
            </div> ';
        }
        
        echo '</div>'; 
//---------------------------------------------------------------------
}elseif(isset($urlparts[1]) && $urlparts[1] == 'ok'){
    GetBreadcrumb(array($Title_page));
    echo '
    <div class="container">
        <div class="col-md-12 padding0">
            <h1 class="filter-title"><span class="cat-title"><strong>'.$Title_page.'</strong></span></h1>
        </div>
        <div class="col-md-12 padding0">
        <br>
            '.Show_Alert('success' , 'تم إضافة الأوردر بنجاح').'
        </div>
        <div class="col-md-12 padding0 text-center">
        <br>
           <h4><a href="'.$Site_URL.'">الرجوع الى الصفحة الرئيسية</a></h4><br>
        </div>
    </div>
    ';
//---------------------------------------------------------------------
}else{
    GetBreadcrumb(array($Title_page));
    /*
    echo '<pre>';
    print_r($all_cart);
    echo '</pre>';
    */
    echo '
    <div class="container">
        <div class="col-md-12 padding0">
            <h1 class="filter-title"><span class="cat-title"><strong>'.$Title_page.'</strong></span></h1>
        </div>
        ';
        if(count($all_cart) > 0){
        echo '<div class="col-md-8">'; 
            $c= 0;
            $total = 0 ;
            $total_ship = 0;
            echo '<table class="table">
                    <thead>
                    <tr>
                        <th scope="col">المنتج</th>
                        <th scope="col">السعر</th>
                        <th scope="col">المقاس</th>
                        <th scope="col">الكمية</th>
                        <th scope="col">إزالة</th>
                    </tr>
                    </thead>
                    <tbody>';
                    
                    for ($i=0; $i <= count($all_cart)-1 ; $i++) { 
                        $product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$all_cart[$i]['productid'].'" ', '');
                        if (count($product) > 0){
                            $pto = explode('&&&' , $product[0]['photo']);
                            $poto  = '';
                            for ($l=0; $l <= count($pto)-1 ; $l++) { 
                                if(trim($pto[$l]) != '' && $poto == ''){
                                    $xch = getAllFrom('*' , 'photos' , 'WHERE id = "'.trim($pto[$l]).'" ', '');
                                    if(count($xch) > 0){
                                        $poto = $xch[0]['link'];
                                    }
                                }
                            }
                            

                        $filter = $all_cart[$i]['filter'] == '' ? '---' : str_replace('&','<br>',str_replace(' ' ,'' , $all_cart[$i]['filter']));
                        echo '
                        <tr>
                            <th class="title" scope="row"><img src="'.$Site_URL.'/'.$poto.'" class="cartimg"><a target="_blank" href="'.$Site_URL.'/'.$product[0]['link'].'">'.$product[0]['title'].'</a></th>
                            <td>'.$product[0]['price'].'</td>
                            <td>'.$filter.'</td>
                            <td><input onchange="calcCartPrice()" style="width: 60px;text-align: center;" type="number" id="productcount'.$c.'" max="100" min="1" value="'.$all_cart[$i]['count'].'" class="form-control"></td>
                            <td><a class="removefromcart" onclick="RemoveFromTCart('.$all_cart[$i]['productid'].')"><i class="fa fa-trash"></i></a></td>
                             
                            <input type="hidden" class="price" id="price'.$c.'" value="'.$product[0]['price'].'">
                            <input type="hidden" class="priceship" id="priceship'.$c.'" value="'.$all_cart[$i]['ship'].'">

                            <input type="hidden" class="itmfilter" value="'.$all_cart[$i]['filter'].'">
                            <input type="hidden" class="itmcity" value="'.$all_cart[$i]['city'].'">
                            <input type="hidden" class="prductid" value="'.$product[0]['id'].'">
                        </tr>
                        ';
                        $c = $c + 1 ;
                        $total = $total + $product[0]['price'];
                        //$total_ship = $total_ship+ ($all_cart[$i]['ship']*$all_cart[$i]['count']);
                        } 
                    }
    
            echo '</tbody>
            </table>
        </div>
        <div class="col-md-4">
            <div class="confirm-box">
                <h3>ملخص الطلبات</h3>
                <h4> السعر الإجمالى : <span><b id="totalprice">'.$total.'</b> ريال</span></h4>
                <h4> تكلفة الشحن : <span><b id="totalship">لم تحسب بعد</b> أختار العنوان أولا</span></h4>
                <hr>
                <h4> الإجمالي : <span><b id="totalcash">'.($total+$total_ship).'</b> ريال</span></h4>
                <a id="btn1" class="btn btn-md btn-success" onclick="SaveCartOrder()">تاكيد الشراء</a>
                <a href="#" class="btn-success2">متابعه التسوق</a>


            </div>
        </div>
        ';
        }
        if(count($all_cart) <= 0){
            echo '
            <div class="col-md-12">
                '.Show_Alert('warning' , 'سلة المشتريات فارغة').'
            </div> ';
        }
        
        echo '</div>';

}


include('footer.php'); 
?>
<script> 
    var payment_bank = true;
    var mycoupon = {name :'' , code :'', discount :0};

    function addCoupon(){
        $('.coupon_data').addClass('dis');
        var copfound = false;
        var acj = '<?php echo $AllCouponJson;?>'; 
        var acjb = JSON.parse(atob(acj));
        var cop = $('#coupon_code').val();
        for (let i = 0; i < acjb.length; i++) {
            if(acjb[i].code == cop){
                mycoupon = {name :acjb[i].name , code :acjb[i].code, discount :acjb[i].discount}
                copfound = true;
                $('#coupon').text(acjb[i].discount);
                SetShip();
                 
            }
        }
        if(!copfound){
            Swal({
                title: 'خطأ',
                text: 'كود الكوبون غير صحيح',
                type: 'error',
                customClass: 'animated tada',
                showCancelButton: false,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#d33', 
                confirmButtonText: 'إغلاق',
            });
            $('.coupon_data').removeClass('dis');
        }
    }

    function ConfirmToAddOrder(){
        Swal({
            title: 'تأكيد الأوردر',
            text: "هل انت متأكد؟",
            type: 'info',
            customClass: 'animated tada',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'تأكيد',
            cancelButtonText: 'إلغاء',
            }).then((result) => {
                if (result.value) {
                    var errors = [];
                    var cartproduct = '<?php echo json_encode($ProductCart , JSON_UNESCAPED_UNICODE);?>';
                    var um1 = $('#um1').val();
                    var um2 = $('#um2').val();
                    var cty1 = $("#cty1 option:selected").text();  
                    var cty2 =  $("#cty2 option:selected").text(); 
                    var cty3 = $('#cty3').val();
                    var cty4 = $('#cty4').val();
                    var xm1 = $('#xm1').val();
                    var xm2 = $('#xm2').val();
                    var totalprice = $('#totalprice').text();
                    var totalship = $('input[name="optradio"]:checked').val();
                    var im = $('#profile-img').attr('src');
                    var imus = $('#imus').val();


                    console.log(payment_bank);

                    if(totalprice == 0){
                        errors.push('سلة الشراء فارغة');
                    }
                    if(um1 == '' || um1 == 0){
                        errors.push('من فضلك ادخل الإسم');
                    }
                    if(um2 == '' || um2 == 0){
                        errors.push('من فضلك ادخل رقم الواتس اب');
                    }
                    if(cty1 == '' || cty1 == 0){
                        errors.push('من فضلك اختر المدينة');
                    }
                    if(cty2 == '' || cty2 == 0){
                        errors.push('من فضلك اختر الحي');
                    }
                    if(cty3 == '' || cty3 == 0){
                        errors.push('من فضلك اكتب عنوان الشارع');
                    }
                    if($('#TriSeaDefault').is(":checked") && xm1 == ''){
                        errors.push('من فضلك ادخل إسم المستلم');
                    }
                    if($('#TriSeaDefault').is(":checked") && xm2 == ''){
                        errors.push('من فضلك ادخل رقم واتس اب المستلم');
                    }
                    
                    if(payment_bank && im.includes(site_url)){
                        errors.push('يجب عليك ارفاق ايصال الدفع');
                    }

                    if(payment_bank && (imus == '' || imus == 0) ){
                        errors.push('يجب عليك كتابة اسم صاحب الحساب الذى قام بالتحويل');
                    }
                    
                    if(errors.length > 0){
                        Swal({
                            title: 'خطأ',
                            text: errors[0],
                            type: 'error',
                            customClass: 'animated tada',
                            showCancelButton: false,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#d33', 
                            confirmButtonText: 'إغلاق',
                        });
                    }else{
                        $.post(site_url+"/ajax.php", { 
                            action : 'ConfirmToAddOrderNew' , 
                            um1:um1 , 
                            um2:um2 , 
                            cty1:cty1 , 
                            cty2:cty2 , 
                            cty3:cty3 , 
                            cty4:cty4 , 
                            xm1:xm1 , 
                            xm2:xm2 , 
                            totalprice:totalprice , 
                            totalship:totalship , 
                            cartproduct:cartproduct,
                            im:im,
                            imus:imus,
                            coupon : JSON.stringify(mycoupon)
                        } ,function(data){ 
                             window.location = site_url+'/cart/ok';
                        });
                    }      
                }
            })
    }
    function RemoveAddress(index){
        Swal({
            title: 'حذف العنوان',
            text: "هل انت متأكد من حذف العنوان؟",
            type: 'info',
            customClass: 'animated tada',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'تأكيد',
            cancelButtonText: 'إلغاء',
            }).then((result) => {
                if (result.value) {
                    $.post(site_url+"/ajax.php", { action : 'RemoveAddress' , index:index} ,function(data){
                        window.location = site_url+'/cart/confirm';
                    });
                }
            })
    }
    //------------------------------------------------------------------------------------
    function modalAddress(){
        $('#add_name').val('');
        $('#add_phone').val('');
        $('#add_address').val('');
        $("#ModelAddress").modal("show");
    }
    //------------------------------------------------------------------------------------
    function AddAddress(){
        var add_name = $('#add_name').val();
        var add_phone = $('#add_phone').val();
        var add_address = $('#add_address').val();
        if(add_name.trim() != '' && add_phone.trim() != '' && add_address.trim() != ''){
            $("#ModelAddress").modal("hide");
            Swal({
            title: 'إضافة عنوان جديد',
            text: "هل انت متأكد من إضافة العنوان؟",
            type: 'info',
            customClass: 'animated tada',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'إضافة',
            cancelButtonText: 'إلغاء',
            }).then((result) => {
                if (result.value) {
                    $.post(site_url+"/ajax.php", { action : 'AddAddress' , add_name:add_name , add_phone:add_phone , add_address:add_address} ,function(data){
                        window.location = site_url+'/cart/confirm';
                    });
                }
            })
        }
    }
    //------------------------------------------------------------------------------------
    function calcCartPrice(){
        var allprice = 0;
        var allship = 0;
        var allcash = 0;
  
        $('tbody tr').each(function () {
            productname = $(this).find('.title').text().trim();
            productid = $(this).find('.prductid').val().trim();
            price = $(this).find('.price').val().trim();
            priceship = $(this).find('.priceship').val().trim();
            count = $(this).find('.form-control').val().trim();

            allprice += (price * count); 
            allship += (priceship * count); 
            allcash += (allprice + allship); 
        });
        $('#totalprice').text(allprice);
        $('#totalship').text($('input[name="optradio"]:checked').val());
        $('#totalcash').text(allcash); 
    }
    //------------------------------------------------------------------------------------
    function SaveCartOrder(){
        var cart = [];
        $('tbody tr').each(function () {
            productname = $(this).find('.title').text().trim();
            productid = $(this).find('.prductid').val().trim();
            price = $(this).find('.price').val().trim();
            priceship = $(this).find('.priceship').val().trim();
            count = $(this).find('.form-control').val().trim();
            itmfilter = $(this).find('.itmfilter').val().trim();
            itmcity = $(this).find('.itmcity').val().trim();
            var item = {
                'productid' : productid,
                'title' : productname,
                'ship' : priceship,
                'city' : itmcity,
                'filter' : itmfilter,
                'count' : count
            };
            cart.push(item);
        });
        $.post(site_url+"/ajax.php", { action : 'SaveCartOrder' , cart:cart} ,function(data){
            CountCart(); 
            window.location = site_url+'/cart/confirm';
        });
    }
    //------------------------------------------------------------------------------------
    function RemoveFromTCart(productid){
        $.post(site_url+"/ajax.php", { action : 'RemoveFromCart' , productid:productid} ,function(data){
            CountCart(); 
            location.reload();
        });
    }

    var city = JSON.parse('<?php echo $jcities ; ?>'); 
    function ChangeCity(){
        $('#cty2').html('<option value="0">إختر الحي</option>');
        $('#r').html('');
        var sel = $('#cty1').val();
        if(sel > 0){
            for (var i = 0; i <= city.length-1; i++) {
                if(city[i].parent == sel){
                    $('#cty2').append('<option value="'+city[i].id+'">'+city[i].name+'</option>');
                } 
            }
            for (var i = 0; i <= city.length-1; i++) {
                if(city[i].id == sel && city[i].cost > 0){
                    $('#r').html('<div class="radio"> <label class="srb"><input onchange="SetShip()" value="'+city[i].cost+'" type="radio" name="optradio">مندوب '+city[i].name+' - (1 -3 يوم عمل) <span>'+city[i].cost+' ريال</span></label> </div>');
                } 
            }
        }
    }

    function ShowOther(){
        if($('#TriSeaDefault').is(":checked")){
            $('#other').show(500);
        }else{
            $('#other').hide(500);
        }
    }

    function SetShip(){ 
        $('#totalship').text($('input[name="optradio"]:checked').val());
        $('#totalcash').text(($('#totalprice').text() * 1 + $('input[name="optradio"]:checked').val() * 1)); 
        $('#totalcash').text(parseFloat($('#totalcash').text() - mycoupon.discount ));
        if($('input[name="optradio"]:checked').parent().text().includes('مندوب')){
            var p = $('.iconss');
            for (let i = 0; i <= p.length-1; i++) {
                if($(p[i]).attr('data-id') == '2'){
                    $(p[i]).removeClass('dno');
                }
            }
        }else{
            var p = $('.iconss');
            for (let i = 0; i <= p.length-1; i++) {
                if($(p[i]).attr('data-id') == '2'){
                    $(p[i]).addClass('dno');
                }
            }
        }
    }

    var $profileImgDiv = document.getElementById("profile-img-div"),
  $profileImg = document.getElementById("profile-img"),
  $changePhoto = document.getElementById("change-photo"),
  $xPosition = document.getElementById("x-position"),
  $yPosition = document.getElementById("y-position"),
  $saveImg = document.getElementById("save-img"),
  $loader = document.getElementById("loader"),
  $cancelImg = document.getElementById("cancel-img"),
  $profileImgInput = document.getElementById("profile-img-input"),
  $profileImgConfirm = document.getElementById("profile-img-confirm"),
  $error = document.getElementById("error");

var currentProfileImg = "",
  profileImgDivW = getSizes($profileImgDiv).elW,
  NewImgNatWidth = 0,
  NewImgNatHeight = 0,
  NewImgNatRatio = 0,
  NewImgWidth = 0,
  NewImgHeight = 0,
  NewImgRatio = 0,
  xCut = 0,
  yCut = 0;

makeSquared($profileImgDiv);

$changePhoto.addEventListener("change", function () {
  currentProfileImg = $profileImg.src;
  showPreview(this, $profileImg);
  $loader.style.width = "100%";
  $profileImgInput.style.display = "none";
  $profileImgConfirm.style.display = "flex";
  $error.style.display = "none";
});

$xPosition.addEventListener("input", function () {
  $profileImg.style.left = -this.value + "px";
  xCut = this.value;
  yCut = 0;
});

$yPosition.addEventListener("input", function () {
  $profileImg.style.top = -this.value + "px";
  yCut = this.value;
  xCut = 0;
});

$saveImg.addEventListener("click", function () {
  cropImg($profileImg);
  resetAll(true);
});

$cancelImg.addEventListener("click", function () {
  resetAll(false);
});

window.addEventListener("resize", function () {
  makeSquared($profileImgDiv);
  profileImgDivW = getSizes($profileImgDiv).elW;
});

function makeSquared(el) {
  var elW = el.clientWidth;
  elW = elW - 100;
  el.style.height = elW + "px";
}

function showPreview(input, el) {
  var reader = new FileReader();
  reader.readAsDataURL(input.files[0]);
  if (input.files && input.files[0]) {
    reader.onload = function (e) {
      setTimeout(function () {
        el.src = e.target.result;
      }, 300);

      var poll = setInterval(function () {
        if (el.naturalWidth && el.src != currentProfileImg) {
          clearInterval(poll);
          setNewImgSizes(el);
          setTimeout(function () {
            $loader.style.width = "0%";
            $profileImg.style.opacity = "1";
          }, 1000);
        }
      }, 100);
    };
  } else {
    return;
  }
}

function setNewImgSizes(el) {
  if (getNatSizes(el).elR > 1) {
    el.style.width = "auto";
    el.style.height = "100%";
    newImgWidth = getSizes(el).elW;
    $xPosition.style.display = "block";
    $yPosition.style.display = "none";
    $xPosition.max = newImgWidth - profileImgDivW;
  } else if (getNatSizes(el).elR < 1) {
    el.style.width = "100%";
    el.style.height = "auto";
    newImgHeight = getSizes(el).elH;
    $xPosition.style.display = "none";
    $yPosition.style.display = "block";
    $yPosition.max = newImgHeight - profileImgDivW;
  } else if (getNatSizes(el).elR == 1) {
    el.style.width = "100%";
    el.style.height = "100%";
    $xPosition.style.display = "none";
    $yPosition.style.display = "none";
  }
}

function getNatSizes(el) {
  var elW = el.naturalWidth,
    elH = el.naturalHeight,
    elR = elW / elH;
  return {
    elW: elW,
    elH: elH,
    elR: elR
  };
}

function getSizes(el) {
  var elW = el.clientWidth,
    elH = el.clientHeight,
    elR = elW / elH;
  return {
    elW: elW,
    elH: elH,
    elR: elR
  };
}

function cropImg(el) {
  var natClientImgRatio = getNatSizes(el).elW / getSizes(el).elW;
  (myCanvas = document.getElementById("croppedPhoto")),
    (ctx = myCanvas.getContext("2d"));
  ctx.fillStyle = "#ffffff";
  ctx.fillRect(0, 0, 400, 400);
  ctx.drawImage(
    el,
    xCut * natClientImgRatio,
    yCut * natClientImgRatio,
    profileImgDivW * natClientImgRatio,
    profileImgDivW * natClientImgRatio,
    0,
    0,
    400,
    400
  );
  var newProfileImgUrl = myCanvas.toDataURL("image/jpeg");
  $profileImg.src = newProfileImgUrl;
}

function resetAll(confirm) {
  if (!confirm) {
    $profileImg.src = currentProfileImg;
  }
  $changePhoto.value = "";
  $profileImgInput.style.display = "block";
  $profileImgConfirm.style.display = "none";
  $profileImg.style.left = "0";
  $profileImg.style.top = "0";
  $profileImg.style.width = "100%";
  $profileImg.style.height = "100%";
  $xPosition.style.display = "none";
  $yPosition.style.display = "none";
  $xPosition.value = "0";
  $yPosition.value = "0";
  xCut = "0";
  yCut = "0";
}

function checkMinSizes(el) {
  if (getNatSizes(el).elW > 400 && getNatSizes(el).elH > 400) {
    return true;
  } else {
    return false;
  }
}

$('.iconss').on('click' , function (){
    $('.iconss').removeClass('active');
    $(this).addClass('active');
    var id = $(this).attr('data-id');
    $('.iconss_data').hide(200);
    $('.iconss_data_'+id).show(200);
    if(id == 2){
        payment_bank = false;
    }else{
        payment_bank = true;
    }
});

</script>