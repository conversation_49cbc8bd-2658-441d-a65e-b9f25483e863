<?php
ob_start();
include('../webset.php'); 
include('../session.php');

 if ( is_dir ("../files/") === false ){
 mkdir("../files/");
}

function getExtensiont($str) 
{

         $i = strrpos($str,".");
         if (!$i) { return ""; } 

         $l = strlen($str) - $i;
         $ext = substr($str,$i+1,$l);
         return $ext;
 }
 
$uploadDir = '../files';

if (!empty($_FILES)) {
 $tmpFile = $_FILES['file']['tmp_name'];
 $name = $_FILES['file']['name'];
 $ext = getExtensiont($name);
 $filename = generate_key(10).'-'.time().'.'.$ext;
 if (move_uploaded_file($tmpFile, $uploadDir.'/'.$filename)){

 	$link = 'files/'.$filename;
 	

 	$stmt = $db->prepare("INSERT INTO photos ( link , datetime ) VALUES (:user_1 , :user_2 )");  
	$stmt->execute(array('user_1' => $link , 'user_2' => time()  )) ;
	$id = $db->lastInsertId();
	echo $id;
 }
 
}

?>