<?php
    if (!isset($_SESSION['userData'])){header("Location:".$Site_URL.'/admincp/login.php'); exit();}
    if ($User_Type == 0){header("Location:".$Site_URL.'/'); exit(); }
    if ($User_Type == 1){header("Location:".$Site_URL.'/merchant/'); exit(); }
    if ($User_Type != 2){header("Location:".$Site_URL.'/'); exit(); }
?>
<body>
    <!-- Static navbar -->
    <nav class="navbar navbar-default yamm navbar-fixed-top">
        <div class="container-fluid">
            <button type="button" class="navbar-minimalize minimalize-styl-2  pull-left "><i class="fa fa-bars"></i></button>
            <div class="navbar-header">
                <a class="navbar-brand" href="index.php">لوحة تحكم <?php echo $Site_Name;?></a>
            </div>
        </div><!--/.container-fluid -->
    </nav>
    <section class="page">

        <nav class="navbar-aside navbar-static-side" role="navigation">
            <div class="sidebar-collapse nano">
                <div class="nano-content">
                    <ul class="nav metismenu" id="side-menu">
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الرئيسية'){echo 'active';}?>">
                            <a href="index.php"><i class="fa fa-laptop"></i> <span class="nav-label">الرئيسية</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الأقسام'){echo 'active';}?>">
                            <a href="category.php"><i class="fa fa-th-large"></i> <span class="nav-label">الأقسام</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'المنتجات'){echo 'active';}?>">
                            <a href="products.php"><i class="fa fa-shopping-cart"></i> <span class="nav-label">المنتجات</span></a>
                        </li>
                        
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الأوردرات'){echo 'active';}?>">
                            <a href="orders.php"><i class="fa fa-cart-arrow-down"></i> <span class="nav-label">الأوردرات</span></a>
                        </li> 
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الكوبونات'){echo 'active';}?>">
                            <a href="coupon.php"><i class="fa fa-gift"></i> <span class="nav-label">الكوبونات</span></a>
                        </li> 

                        <li class="<?php if(isset($Title_page) && $Title_page == 'الفلاتر'){echo 'active';}?>">
                            <a href="filter.php"><i class="fa fa-filter"></i> <span class="nav-label">الفلاتر</span></a>
                        </li> 
                        <li class="<?php if(isset($Title_page) && $Title_page == 'المعلومات'){echo 'active';}?>">
                            <a href="info.php"><i class="fa fa-info-circle"></i> <span class="nav-label">المعلومات</span></a>
                        </li> 
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الأعضاء'){echo 'active';}?>">
                            <a href="users.php"><i class="fa fa-users"></i> <span class="nav-label">الأعضاء</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الإعدادات'){echo 'active';}?>">
                            <a href="settings.php"><i class="fa fa-cogs"></i> <span class="nav-label">الإعدادات</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'رسائل اتصل بنا'){echo 'active';}?>">
                            <a href="contactus.php"><i class="fa fa-comment-o"></i> <span class="nav-label">رسائل اتصل بنا</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'الرجوع الى الموقع'){echo 'active';}?>">
                            <a href="<?php echo $Site_URL;?>/"><i class="fa fa-retweet"></i> <span class="nav-label">الرجوع الى الموقع</span></a>
                        </li>
                        <li class="<?php if(isset($Title_page) && $Title_page == 'تسجيل الخروج'){echo 'active';}?>">
                            <a href="<?php echo $Site_URL;?>/logout.php"><i class="fa fa-sign-out"></i> <span class="nav-label">تسجيل الخروج</span></a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <div id="wrapper">
            <div class="content-wrapper container">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="page-title">
                            <h1><?php if (isset($Title_page)){echo $Title_page ; }else{echo getTitle();}?> <small></small></h1>
                            <ol class="breadcrumb">
                                <li><a href="index.php"><i class="fa fa-home"></i></a></li>
                                <li class="active"><?php if (isset($Title_page)){echo $Title_page ; }else{echo getTitle();}?></li>
                            </ol>
                        </div>
                        <hr>
                    </div>
                </div><!-- end .page title-->