<html>
<head>
<title>File Source for Salat.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file Salat.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---Salat.php.html">Salat.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Muslim&nbsp;Prayer&nbsp;Times</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;&nbsp;&nbsp;Salat.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;&nbsp;&nbsp;The&nbsp;five&nbsp;Islamic&nbsp;prayers&nbsp;are&nbsp;named&nbsp;Fajr,&nbsp;Zuhr,&nbsp;Asr,&nbsp;Maghrib</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;Isha.&nbsp;The&nbsp;timing&nbsp;of&nbsp;these&nbsp;five&nbsp;prayers&nbsp;varies&nbsp;from&nbsp;place</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;place&nbsp;and&nbsp;from&nbsp;day&nbsp;to&nbsp;day.&nbsp;It&nbsp;is&nbsp;obligatory&nbsp;for&nbsp;Muslims</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;to&nbsp;perform&nbsp;these&nbsp;prayers&nbsp;at&nbsp;the&nbsp;correct&nbsp;time.</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;Source:&nbsp;http://qasweb.org/qasforum/index.php?showtopic=177&amp;st=0</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*&nbsp;By:&nbsp;Mohamad&nbsp;Magdy&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;Muslim&nbsp;Prayer&nbsp;Times</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;Using&nbsp;this&nbsp;PHP&nbsp;Class&nbsp;you&nbsp;can&nbsp;calculate&nbsp;the&nbsp;time&nbsp;of&nbsp;Muslim&nbsp;prayer</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;according&nbsp;to&nbsp;the&nbsp;geographic&nbsp;location.</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;five&nbsp;Islamic&nbsp;prayers&nbsp;are&nbsp;named&nbsp;Fajr,&nbsp;Zuhr,&nbsp;Asr,&nbsp;Maghrib&nbsp;and&nbsp;Isha.&nbsp;The&nbsp;timing</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;these&nbsp;five&nbsp;prayers&nbsp;varies&nbsp;from&nbsp;place&nbsp;to&nbsp;place&nbsp;and&nbsp;from&nbsp;day&nbsp;to&nbsp;day.&nbsp;It&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*&nbsp;obligatory&nbsp;for&nbsp;Muslims&nbsp;to&nbsp;perform&nbsp;these&nbsp;prayers&nbsp;at&nbsp;the&nbsp;correct&nbsp;time.</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;prayer&nbsp;times&nbsp;for&nbsp;any&nbsp;given&nbsp;location&nbsp;on&nbsp;earth&nbsp;may&nbsp;be&nbsp;determined&nbsp;mathematically</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;if&nbsp;the&nbsp;latitude&nbsp;and&nbsp;longitude&nbsp;of&nbsp;the&nbsp;location&nbsp;are&nbsp;known.&nbsp;However,&nbsp;the&nbsp;theoretical</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;determination&nbsp;of&nbsp;prayer&nbsp;times&nbsp;is&nbsp;a&nbsp;lengthy&nbsp;process.&nbsp;Much&nbsp;of&nbsp;this&nbsp;tedium&nbsp;may&nbsp;be</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;alleviated&nbsp;by&nbsp;using&nbsp;computer&nbsp;programs.</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;Definition&nbsp;of&nbsp;prayer&nbsp;times</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a64"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;FAJR&nbsp;starts&nbsp;with&nbsp;the&nbsp;dawn&nbsp;or&nbsp;morning&nbsp;twilight.&nbsp;Fajr&nbsp;ends&nbsp;just&nbsp;before&nbsp;sunrise.</span></div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;ZUHR&nbsp;begins&nbsp;after&nbsp;midday&nbsp;when&nbsp;the&nbsp;trailing&nbsp;limb&nbsp;of&nbsp;the&nbsp;sun&nbsp;has&nbsp;passed&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;meridian.&nbsp;For&nbsp;convenience,&nbsp;many&nbsp;published&nbsp;prayer&nbsp;timetables&nbsp;add&nbsp;five&nbsp;minutes&nbsp;to</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;mid-day&nbsp;(zawal)&nbsp;to&nbsp;obtain&nbsp;the&nbsp;start&nbsp;of&nbsp;Zuhr.&nbsp;Zuhr&nbsp;ends&nbsp;at&nbsp;the&nbsp;start&nbsp;of&nbsp;Asr&nbsp;time.</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;The&nbsp;timing&nbsp;of&nbsp;ASR&nbsp;depends&nbsp;on&nbsp;the&nbsp;length&nbsp;of&nbsp;the&nbsp;shadow&nbsp;cast&nbsp;by&nbsp;an&nbsp;object.</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;According&nbsp;to&nbsp;the&nbsp;Shafi&nbsp;school&nbsp;of&nbsp;jurisprudence,&nbsp;Asr&nbsp;begins&nbsp;when&nbsp;the&nbsp;length&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;the&nbsp;shadow&nbsp;of&nbsp;an&nbsp;object&nbsp;exceeds&nbsp;the&nbsp;length&nbsp;of&nbsp;the&nbsp;object.&nbsp;According&nbsp;to&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;Hanafi&nbsp;school&nbsp;of&nbsp;jurisprudence,&nbsp;Asr&nbsp;begins&nbsp;when&nbsp;the&nbsp;length&nbsp;of&nbsp;the&nbsp;shadow</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;exceeds&nbsp;TWICE&nbsp;the&nbsp;length&nbsp;of&nbsp;the&nbsp;object.&nbsp;In&nbsp;both&nbsp;cases,&nbsp;the&nbsp;minimum&nbsp;length&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;shadow&nbsp;(which&nbsp;occurs&nbsp;when&nbsp;the&nbsp;sun&nbsp;passes&nbsp;the&nbsp;meridian)&nbsp;is&nbsp;subtracted&nbsp;from&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a74"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;length&nbsp;of&nbsp;the&nbsp;shadow&nbsp;before&nbsp;comparing&nbsp;it&nbsp;with&nbsp;the&nbsp;length&nbsp;of&nbsp;the&nbsp;object.</span></div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;MAGHRIB&nbsp;begins&nbsp;at&nbsp;sunset&nbsp;and&nbsp;ends&nbsp;at&nbsp;the&nbsp;start&nbsp;of&nbsp;isha.</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-doc">&nbsp;*&nbsp;-&nbsp;ISHA&nbsp;starts&nbsp;after&nbsp;dusk&nbsp;when&nbsp;the&nbsp;evening&nbsp;twilight&nbsp;disappears.</span></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a78"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;date_default_timezone_set('UTC');</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a83"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Salat');</span></div></li>
<li><div class="src-line"><a name="a84"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a85"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj-&gt;setLocation(33.513,36.292,2);</span></div></li>
<li><div class="src-line"><a name="a86"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj-&gt;setDate(date('j'),&nbsp;date('n'),&nbsp;date('Y'));</span></div></li>
<li><div class="src-line"><a name="a87"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a88"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$times&nbsp;=&nbsp;$obj-&gt;getPrayTime();</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;'&lt;b&gt;Damascus,&nbsp;Syria&lt;/b&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;date('l&nbsp;F&nbsp;j,&nbsp;Y').'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;';</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a93"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Imsak:&lt;/b&gt;&nbsp;{$times[8]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a94"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Fajr:&lt;/b&gt;&nbsp;{$times[0]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a95"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Sunrise:&lt;/b&gt;&nbsp;{$times[1]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a96"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Zuhr:&lt;/b&gt;&nbsp;{$times[2]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a97"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Asr:&lt;/b&gt;&nbsp;{$times[3]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a98"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Sunset:&lt;/b&gt;&nbsp;{$times[6]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a99"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Maghrib:&lt;/b&gt;&nbsp;{$times[4]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a100"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Isha:&lt;/b&gt;&nbsp;{$times[5]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a101"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&nbsp;class=hilight&gt;Midnight:&lt;/b&gt;&nbsp;{$times[7]}&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a102"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a103"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a104"></a><span class="src-doc">&nbsp;*&nbsp;Qibla&nbsp;Determination&nbsp;Methods&nbsp;-&nbsp;Basic&nbsp;Spherical&nbsp;Trigonometric&nbsp;Formula</span></div></li>
<li><div class="src-line"><a name="a105"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a106"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;problem&nbsp;of&nbsp;qibla&nbsp;determination&nbsp;has&nbsp;a&nbsp;simple&nbsp;formulation&nbsp;in&nbsp;spherical</span></div></li>
<li><div class="src-line"><a name="a107"></a><span class="src-doc">&nbsp;*&nbsp;trigonometry.&nbsp;A&nbsp;is&nbsp;a&nbsp;given&nbsp;location,&nbsp;K&nbsp;is&nbsp;the&nbsp;Ka'ba,&nbsp;and&nbsp;N&nbsp;is&nbsp;the&nbsp;North&nbsp;Pole.</span></div></li>
<li><div class="src-line"><a name="a108"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;great&nbsp;circle&nbsp;arcs&nbsp;AN&nbsp;and&nbsp;KN&nbsp;are&nbsp;along&nbsp;the&nbsp;meridians&nbsp;through&nbsp;A&nbsp;and&nbsp;K,</span></div></li>
<li><div class="src-line"><a name="a109"></a><span class="src-doc">&nbsp;*&nbsp;respectively,&nbsp;and&nbsp;both&nbsp;point&nbsp;to&nbsp;the&nbsp;north.&nbsp;The&nbsp;qibla&nbsp;is&nbsp;along&nbsp;the&nbsp;great&nbsp;circle</span></div></li>
<li><div class="src-line"><a name="a110"></a><span class="src-doc">&nbsp;*&nbsp;arc&nbsp;AK.&nbsp;The&nbsp;spherical&nbsp;angle&nbsp;q&nbsp;=&nbsp;NAK&nbsp;is&nbsp;the&nbsp;angle&nbsp;at&nbsp;A&nbsp;from&nbsp;the&nbsp;north&nbsp;direction</span></div></li>
<li><div class="src-line"><a name="a111"></a><span class="src-doc">&nbsp;*&nbsp;AN&nbsp;to&nbsp;the&nbsp;direction&nbsp;AK&nbsp;towards&nbsp;the&nbsp;Ka'ba,&nbsp;and&nbsp;so&nbsp;q&nbsp;is&nbsp;the&nbsp;qibla&nbsp;bearing&nbsp;to&nbsp;be</span></div></li>
<li><div class="src-line"><a name="a112"></a><span class="src-doc">&nbsp;*&nbsp;computed.&nbsp;Let&nbsp;F&nbsp;and&nbsp;L&nbsp;be&nbsp;the&nbsp;latitude&nbsp;and&nbsp;longitude&nbsp;of&nbsp;A,&nbsp;and&nbsp;FK&nbsp;and&nbsp;LK&nbsp;be</span></div></li>
<li><div class="src-line"><a name="a113"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;latitude&nbsp;and&nbsp;longitude&nbsp;of&nbsp;K&nbsp;(the&nbsp;Ka'ba).&nbsp;If&nbsp;all&nbsp;angles&nbsp;and&nbsp;arc&nbsp;lengths</span></div></li>
<li><div class="src-line"><a name="a114"></a><span class="src-doc">&nbsp;*&nbsp;are&nbsp;measured&nbsp;in&nbsp;degrees,&nbsp;then&nbsp;it&nbsp;is&nbsp;seen&nbsp;that&nbsp;the&nbsp;arcs&nbsp;AN&nbsp;and&nbsp;KN&nbsp;are&nbsp;of&nbsp;measure</span></div></li>
<li><div class="src-line"><a name="a115"></a><span class="src-doc">&nbsp;*&nbsp;90&nbsp;-&nbsp;F&nbsp;and&nbsp;90&nbsp;-&nbsp;FK,&nbsp;respectively.&nbsp;Also,&nbsp;the&nbsp;angle&nbsp;ANK&nbsp;between&nbsp;the&nbsp;meridians</span></div></li>
<li><div class="src-line"><a name="a116"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;K&nbsp;and&nbsp;A&nbsp;equals&nbsp;the&nbsp;difference&nbsp;between&nbsp;the&nbsp;longitudes&nbsp;of&nbsp;A&nbsp;and&nbsp;K,&nbsp;that&nbsp;is,</span></div></li>
<li><div class="src-line"><a name="a117"></a><span class="src-doc">&nbsp;*&nbsp;LK&nbsp;-&nbsp;L,&nbsp;no&nbsp;matter&nbsp;what&nbsp;the&nbsp;prime&nbsp;meridian&nbsp;is.&nbsp;Here&nbsp;we&nbsp;are&nbsp;given&nbsp;two&nbsp;sides&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a118"></a><span class="src-doc">&nbsp;*&nbsp;the&nbsp;included&nbsp;angle&nbsp;of&nbsp;a&nbsp;spherical&nbsp;triangle,&nbsp;and&nbsp;it&nbsp;is&nbsp;required&nbsp;to&nbsp;determine&nbsp;one</span></div></li>
<li><div class="src-line"><a name="a119"></a><span class="src-doc">&nbsp;*&nbsp;other&nbsp;angle.&nbsp;One&nbsp;of&nbsp;the&nbsp;simplest&nbsp;solutions&nbsp;is&nbsp;given&nbsp;by&nbsp;the&nbsp;formula:</span></div></li>
<li><div class="src-line"><a name="a120"></a><span class="src-doc">&nbsp;*&nbsp;&lt;pre&gt;</span></div></li>
<li><div class="src-line"><a name="a121"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sin(LK&nbsp;-&nbsp;L)</span></div></li>
<li><div class="src-line"><a name="a122"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;q&nbsp;=&nbsp;tan&nbsp;&nbsp;&nbsp;------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a123"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cos&nbsp;F&nbsp;tan&nbsp;FK&nbsp;-&nbsp;sin&nbsp;F&nbsp;cos(LK&nbsp;-&nbsp;L)</span></div></li>
<li><div class="src-line"><a name="a124"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/pre&gt;</span></div></li>
<li><div class="src-line"><a name="a125"></a><span class="src-doc">&nbsp;*&nbsp;In&nbsp;this&nbsp;Equation,&nbsp;the&nbsp;sign&nbsp;of&nbsp;the&nbsp;input&nbsp;quantities&nbsp;are&nbsp;assumed&nbsp;as&nbsp;follows:</span></div></li>
<li><div class="src-line"><a name="a126"></a><span class="src-doc">&nbsp;*&nbsp;latitudes&nbsp;are&nbsp;positive&nbsp;if&nbsp;north,&nbsp;negative&nbsp;if&nbsp;south;&nbsp;longitudes&nbsp;are&nbsp;positive</span></div></li>
<li><div class="src-line"><a name="a127"></a><span class="src-doc">&nbsp;*&nbsp;if&nbsp;east,&nbsp;negative&nbsp;if&nbsp;west.&nbsp;The&nbsp;quadrant&nbsp;of&nbsp;q&nbsp;is&nbsp;assumed&nbsp;to&nbsp;be&nbsp;so&nbsp;selected</span></div></li>
<li><div class="src-line"><a name="a128"></a><span class="src-doc">&nbsp;*&nbsp;that&nbsp;sin&nbsp;q&nbsp;and&nbsp;cos&nbsp;q&nbsp;have&nbsp;the&nbsp;same&nbsp;sign&nbsp;as&nbsp;the&nbsp;numerator&nbsp;and&nbsp;denominator&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a129"></a><span class="src-doc">&nbsp;*&nbsp;this&nbsp;Equation.&nbsp;With&nbsp;these&nbsp;conventions,&nbsp;q&nbsp;will&nbsp;be&nbsp;positive&nbsp;for&nbsp;bearings&nbsp;east</span></div></li>
<li><div class="src-line"><a name="a130"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;north,&nbsp;negative&nbsp;for&nbsp;bearings&nbsp;west&nbsp;of&nbsp;north.</span></div></li>
<li><div class="src-line"><a name="a131"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a132"></a><span class="src-doc">&nbsp;*&nbsp;Reference:</span></div></li>
<li><div class="src-line"><a name="a133"></a><span class="src-doc">&nbsp;*&nbsp;The&nbsp;Correct&nbsp;Qibla,&nbsp;S.&nbsp;Kamal&nbsp;Abdali&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a134"></a><span class="src-doc">&nbsp;*&nbsp;PDF&nbsp;version&nbsp;in&nbsp;http://www.patriot.net/users/abdali/ftp/qibla.pdf</span></div></li>
<li><div class="src-line"><a name="a135"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a136"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a137"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a138"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;date_default_timezone_set('UTC');</span></div></li>
<li><div class="src-line"><a name="a139"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a140"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a141"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Salat');</span></div></li>
<li><div class="src-line"><a name="a142"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a143"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$obj-&gt;setLocation(33.513,36.292,2);</span></div></li>
<li><div class="src-line"><a name="a144"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a145"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$direction&nbsp;=&nbsp;$obj-&gt;getQibla();</span></div></li>
<li><div class="src-line"><a name="a146"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;&lt;b&gt;Qibla&nbsp;Direction&nbsp;(from&nbsp;the&nbsp;north&nbsp;direction):&lt;/b&gt;&nbsp;$direction&lt;br&nbsp;/&gt;&quot;;</span></div></li>
<li><div class="src-line"><a name="a147"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a148"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a149"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a150"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a151"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a152"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a153"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a154"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a155"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a156"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a157"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a158"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a159"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;calculate&nbsp;the&nbsp;time&nbsp;of&nbsp;Muslim&nbsp;prayer&nbsp;according&nbsp;to&nbsp;the&nbsp;geographic</span></div></li>
<li><div class="src-line"><a name="a160"></a><span class="src-doc">&nbsp;*&nbsp;location.</span></div></li>
<li><div class="src-line"><a name="a161"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a162"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a163"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a164"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a165"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a166"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a167"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a168"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a169"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a170"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a></div></li>
<li><div class="src-line"><a name="a171"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a172"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a173"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;السنة</span></div></li>
<li><div class="src-line"><a name="a174"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a175"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a176"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$year&nbsp;</span>=&nbsp;<span class="src-num">1975</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a177"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a178"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a179"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الشهر</span></div></li>
<li><div class="src-line"><a name="a180"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a181"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a182"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$month&nbsp;</span>=&nbsp;<span class="src-num">8</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a183"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a184"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a185"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;اليوم</span></div></li>
<li><div class="src-line"><a name="a186"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a187"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a188"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$day&nbsp;</span>=&nbsp;<span class="src-num">2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a189"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a190"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a191"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;فرق&nbsp;التوقيت&nbsp;العالمى</span></div></li>
<li><div class="src-line"><a name="a192"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a193"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a194"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$zone&nbsp;</span>=&nbsp;<span class="src-num">2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a195"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a196"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a197"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;خط&nbsp;الطول&nbsp;الجغرافى&nbsp;للمكان</span></div></li>
<li><div class="src-line"><a name="a198"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a199"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a200"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$long&nbsp;</span>=&nbsp;<span class="src-num">37.15861</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a201"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a202"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a203"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;خط&nbsp;العرض&nbsp;الجغرافى</span></div></li>
<li><div class="src-line"><a name="a204"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a205"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a206"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$lat&nbsp;</span>=&nbsp;<span class="src-num">36.20278</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a207"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a208"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a209"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الارتفاع&nbsp;عن&nbsp;سطح&nbsp;البحر</span></div></li>
<li><div class="src-line"><a name="a210"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a211"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a212"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$elevation&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a213"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a214"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a215"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;زاوية&nbsp;الشروق&nbsp;والغروب</span></div></li>
<li><div class="src-line"><a name="a216"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a217"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a218"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$AB2&nbsp;</span>=&nbsp;-<span class="src-num">0.833333</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a219"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a220"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a221"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;زاوية&nbsp;العشاء</span></div></li>
<li><div class="src-line"><a name="a222"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a223"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a224"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$AG2&nbsp;</span>=&nbsp;-<span class="src-num">18</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a225"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a226"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a227"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;زاوية&nbsp;الفجر</span></div></li>
<li><div class="src-line"><a name="a228"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a229"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a230"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$AJ2&nbsp;</span>=&nbsp;-<span class="src-num">18</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a231"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a232"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a233"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;المذهب</span></div></li>
<li><div class="src-line"><a name="a234"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a235"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a236"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$school&nbsp;</span>=&nbsp;<span class="src-str">'Shafi'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a237"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a238"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a239"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;الطائفة</span></div></li>
<li><div class="src-line"><a name="a240"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a241"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a242"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">protected&nbsp;</span><span class="src-var">$view&nbsp;</span>=&nbsp;<span class="src-str">'Sunni'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a243"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a244"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a245"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a246"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a247"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a248"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a249"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><span class="src-id">__construct</span><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a250"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a251"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a252"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a253"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a254"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;date&nbsp;of&nbsp;day&nbsp;for&nbsp;Salat&nbsp;calculation</span></div></li>
<li><div class="src-line"><a name="a255"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a256"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$m&nbsp;</span><span class="src-doc">Month&nbsp;of&nbsp;date&nbsp;you&nbsp;want&nbsp;to&nbsp;calculate&nbsp;Salat&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a257"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$d&nbsp;</span><span class="src-doc">Day&nbsp;of&nbsp;date&nbsp;you&nbsp;want&nbsp;to&nbsp;calculate&nbsp;Salat&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a258"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$y&nbsp;</span><span class="src-doc">Year&nbsp;(four&nbsp;digits)&nbsp;of&nbsp;date&nbsp;you&nbsp;want&nbsp;to&nbsp;calculate&nbsp;Salat&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a259"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a260"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a261"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a262"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a263"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetDate">setDate</a><span class="src-sym">(</span><span class="src-var">$m&nbsp;</span>=&nbsp;<span class="src-num">8</span><span class="src-sym">,&nbsp;</span><span class="src-var">$d&nbsp;</span>=&nbsp;<span class="src-num">2</span><span class="src-sym">,&nbsp;</span><span class="src-var">$y&nbsp;</span>=&nbsp;<span class="src-num">1975</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a264"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a265"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$y</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$y&nbsp;</span>&gt;&nbsp;<span class="src-num">0&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$y&nbsp;</span>&lt;&nbsp;<span class="src-num">3000</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a266"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">year&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$y</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a267"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a268"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a269"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$m</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$m&nbsp;</span>&gt;=&nbsp;<span class="src-num">1&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$m&nbsp;</span>&lt;=&nbsp;<span class="src-num">12</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a270"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">month&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$m</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a271"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a272"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a273"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$d</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$d&nbsp;</span>&gt;=&nbsp;<span class="src-num">1&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$d&nbsp;</span>&lt;=&nbsp;<span class="src-num">31</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a274"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">day&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$d</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a275"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a276"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a277"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a278"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a279"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a280"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a281"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;location&nbsp;information&nbsp;for&nbsp;Salat&nbsp;calculation</span></div></li>
<li><div class="src-line"><a name="a282"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a283"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">decimal&nbsp;</span><span class="src-doc-var">$l1&nbsp;</span><span class="src-doc">Latitude&nbsp;of&nbsp;location&nbsp;you&nbsp;want&nbsp;to&nbsp;calculate&nbsp;Salat&nbsp;time&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a284"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">decimal&nbsp;</span><span class="src-doc-var">$l2&nbsp;</span><span class="src-doc">Longitude&nbsp;of&nbsp;location&nbsp;you&nbsp;want&nbsp;to&nbsp;calculate&nbsp;Salat&nbsp;time&nbsp;in</span></div></li>
<li><div class="src-line"><a name="a285"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$z&nbsp;</span><span class="src-doc">&nbsp;Time&nbsp;Zone,&nbsp;offset&nbsp;from&nbsp;UTC&nbsp;(see&nbsp;also&nbsp;Greenwich&nbsp;Mean&nbsp;Time)</span></div></li>
<li><div class="src-line"><a name="a286"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">integer&nbsp;</span><span class="src-doc-var">$e&nbsp;</span><span class="src-doc">&nbsp;Elevation,&nbsp;it&nbsp;is&nbsp;the&nbsp;observer's&nbsp;height&nbsp;in&nbsp;meters.</span></div></li>
<li><div class="src-line"><a name="a287"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a288"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a289"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a290"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a291"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetLocation">setLocation</a><span class="src-sym">(</span><span class="src-var">$l1&nbsp;</span>=&nbsp;<span class="src-num">36.20278</span><span class="src-sym">,&nbsp;</span><span class="src-var">$l2&nbsp;</span>=&nbsp;<span class="src-num">37.15861</span><span class="src-sym">,&nbsp;</span><span class="src-var">$z&nbsp;</span>=&nbsp;<span class="src-num">2</span><span class="src-sym">,&nbsp;</span><span class="src-var">$e&nbsp;</span>=&nbsp;<span class="src-num">0</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a292"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a293"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$l1</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$l1&nbsp;</span>&gt;=&nbsp;-<span class="src-num">180&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$l1&nbsp;</span>&lt;=&nbsp;<span class="src-num">180</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a294"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat&nbsp;</span>=&nbsp;<span class="src-var">$l1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a295"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a296"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a297"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$l2</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$l2&nbsp;</span>&gt;=&nbsp;-<span class="src-num">180&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$l2&nbsp;</span>&lt;=&nbsp;<span class="src-num">180</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a298"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">long&nbsp;</span>=&nbsp;<span class="src-var">$l2</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a299"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a300"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a301"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$z</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$z&nbsp;</span>&gt;=&nbsp;-<span class="src-num">12&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$z&nbsp;</span>&lt;=&nbsp;<span class="src-num">12</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a302"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">zone&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$z</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a303"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a304"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a305"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$e</span><span class="src-sym">))&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a306"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">elevation&nbsp;</span>=&nbsp;<span class="src-var">$e</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a307"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a308"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a309"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a310"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a311"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a312"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a313"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Setting&nbsp;rest&nbsp;of&nbsp;Salat&nbsp;calculation&nbsp;configuration</span></div></li>
<li><div class="src-line"><a name="a314"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a315"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convention&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fajr&nbsp;Angle&nbsp;&nbsp;Isha&nbsp;Angle</span></div></li>
<li><div class="src-line"><a name="a316"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a317"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Muslim&nbsp;World&nbsp;League&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-18&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-17</span></div></li>
<li><div class="src-line"><a name="a318"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a319"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Islamic&nbsp;Society&nbsp;of&nbsp;North&nbsp;America&nbsp;(ISNA)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-15</span></div></li>
<li><div class="src-line"><a name="a320"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a321"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Egyptian&nbsp;General&nbsp;Authority&nbsp;of&nbsp;Survey&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-19.5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-17.5</span></div></li>
<li><div class="src-line"><a name="a322"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a323"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Umm&nbsp;al-Qura&nbsp;University,&nbsp;Makkah&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-18.5</span></div></li>
<li><div class="src-line"><a name="a324"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Isha&nbsp;90&nbsp;&nbsp;min&nbsp;after&nbsp;Maghrib,&nbsp;120&nbsp;min&nbsp;during&nbsp;Ramadan</span></div></li>
<li><div class="src-line"><a name="a325"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a326"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;University&nbsp;of&nbsp;Islamic&nbsp;Sciences,&nbsp;Karachi&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-18&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-18</span></div></li>
<li><div class="src-line"><a name="a327"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a328"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Institute&nbsp;of&nbsp;Geophysics,&nbsp;University&nbsp;of&nbsp;Tehran&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-17.7&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-14(*)</span></div></li>
<li><div class="src-line"><a name="a329"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a330"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Shia&nbsp;Ithna&nbsp;Ashari,&nbsp;Leva&nbsp;Research&nbsp;Institute,&nbsp;Qum&nbsp;&nbsp;-16&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-14</span></div></li>
<li><div class="src-line"><a name="a331"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a332"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;(*)&nbsp;Isha&nbsp;angle&nbsp;is&nbsp;not&nbsp;explicitly&nbsp;defined&nbsp;in&nbsp;Tehran&nbsp;method</span></div></li>
<li><div class="src-line"><a name="a333"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Fajr&nbsp;Angle&nbsp;=&nbsp;$fajrArc,&nbsp;Isha&nbsp;Angle&nbsp;=&nbsp;$ishaArc</span></div></li>
<li><div class="src-line"><a name="a334"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a335"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;-&nbsp;حزب&nbsp;العلماء&nbsp;في&nbsp;لندن&nbsp;لدول</span></div></li>
<li><div class="src-line"><a name="a336"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;أوروبا&nbsp;في&nbsp;خطوط&nbsp;عرض&nbsp;تزيد&nbsp;على&nbsp;48</span></div></li>
<li><div class="src-line"><a name="a337"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a338"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$ishaArc&nbsp;=&nbsp;-17</span></div></li>
<li><div class="src-line"><a name="a339"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$fajrArc&nbsp;=&nbsp;-17</span></div></li>
<li><div class="src-line"><a name="a340"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a341"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">&nbsp;</span><span class="src-doc-var">$sch&nbsp;</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[Shafi|Hanafi]&nbsp;to&nbsp;define&nbsp;Muslims&nbsp;Salat</span></div></li>
<li><div class="src-line"><a name="a342"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;calculation&nbsp;method&nbsp;(affect&nbsp;Asr&nbsp;time)</span></div></li>
<li><div class="src-line"><a name="a343"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">decimal&nbsp;</span><span class="src-doc-var">$sunriseArc&nbsp;</span><span class="src-doc">Sun&nbsp;rise&nbsp;arc&nbsp;(default&nbsp;value&nbsp;is&nbsp;-0.833333)</span></div></li>
<li><div class="src-line"><a name="a344"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">decimal&nbsp;</span><span class="src-doc-var">$ishaArc&nbsp;</span><span class="src-doc">&nbsp;&nbsp;&nbsp;Isha&nbsp;arc&nbsp;(default&nbsp;value&nbsp;is&nbsp;-18)</span></div></li>
<li><div class="src-line"><a name="a345"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">decimal&nbsp;</span><span class="src-doc-var">$fajrArc&nbsp;</span><span class="src-doc">&nbsp;&nbsp;&nbsp;Fajr&nbsp;arc&nbsp;(default&nbsp;value&nbsp;is&nbsp;-18)</span></div></li>
<li><div class="src-line"><a name="a346"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc">&nbsp;</span><span class="src-doc-var">$view&nbsp;</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[Sunni|Shia]&nbsp;to&nbsp;define&nbsp;Muslims&nbsp;Salat&nbsp;calculation</span></div></li>
<li><div class="src-line"><a name="a347"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;method&nbsp;(affect&nbsp;Maghrib&nbsp;and&nbsp;Midnight&nbsp;time)</span></div></li>
<li><div class="src-line"><a name="a348"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a349"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">object&nbsp;</span><span class="src-doc">$this&nbsp;to&nbsp;build&nbsp;a&nbsp;fluent&nbsp;interface</span></div></li>
<li><div class="src-line"><a name="a350"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a351"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a352"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodsetConf">setConf</a><span class="src-sym">(</span></div></li>
<li><div class="src-line"><a name="a353"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sch&nbsp;</span>=&nbsp;<span class="src-str">'Shafi'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$sunriseArc&nbsp;</span>=&nbsp;-<span class="src-num">0.833333</span><span class="src-sym">,&nbsp;</span><span class="src-var">$ishaArc&nbsp;</span>=&nbsp;-<span class="src-num">17.5</span><span class="src-sym">,&nbsp;</span></div></li>
<li><div class="src-line"><a name="a354"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$fajrArc&nbsp;</span>=&nbsp;-<span class="src-num">19.5</span><span class="src-sym">,&nbsp;</span><span class="src-var">$view&nbsp;</span>=&nbsp;<span class="src-str">'Sunni'</span></div></li>
<li><div class="src-line"><a name="a355"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a356"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$sch&nbsp;</span>=&nbsp;<a href="http://www.php.net/ucfirst">ucfirst</a><span class="src-sym">(</span><span class="src-var">$sch</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a357"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a358"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$sch&nbsp;</span>==&nbsp;<span class="src-str">'Shafi'&nbsp;</span>||&nbsp;<span class="src-var">$sch&nbsp;</span>==&nbsp;<span class="src-str">'Hanafi'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a359"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">school&nbsp;</span>=&nbsp;<span class="src-var">$sch</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a360"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a361"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a362"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$sunriseArc</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$sunriseArc&nbsp;</span>&gt;=&nbsp;-<span class="src-num">180&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$sunriseArc&nbsp;</span>&lt;=&nbsp;<span class="src-num">180</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a363"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">AB2&nbsp;</span>=&nbsp;<span class="src-var">$sunriseArc</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a364"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a365"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a366"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$ishaArc</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$ishaArc&nbsp;</span>&gt;=&nbsp;-<span class="src-num">180&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$ishaArc&nbsp;</span>&lt;=&nbsp;<span class="src-num">180</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a367"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">AG2&nbsp;</span>=&nbsp;<span class="src-var">$ishaArc</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a368"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a369"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a370"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/is_numeric">is_numeric</a><span class="src-sym">(</span><span class="src-var">$fajrArc</span><span class="src-sym">)&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$fajrArc&nbsp;</span>&gt;=&nbsp;-<span class="src-num">180&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$fajrArc&nbsp;</span>&lt;=&nbsp;<span class="src-num">180</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a371"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">AJ2&nbsp;</span>=&nbsp;<span class="src-var">$fajrArc</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a372"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a373"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a374"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$view&nbsp;</span>==&nbsp;<span class="src-str">'Sunni'&nbsp;</span>||&nbsp;<span class="src-var">$view&nbsp;</span>==&nbsp;<span class="src-str">'Shia'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a375"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">view&nbsp;</span>=&nbsp;<span class="src-var">$view</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a376"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a377"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a378"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$this</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a379"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a380"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a381"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a382"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Alias&nbsp;for&nbsp;getPrayTime2&nbsp;method</span></div></li>
<li><div class="src-line"><a name="a383"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a384"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">of&nbsp;Salat&nbsp;times&nbsp;+&nbsp;sun&nbsp;rise&nbsp;in&nbsp;the&nbsp;following&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a385"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;hh:mm&nbsp;where&nbsp;hh&nbsp;is&nbsp;the&nbsp;hour&nbsp;in&nbsp;local&nbsp;format&nbsp;and&nbsp;24&nbsp;mode</span></div></li>
<li><div class="src-line"><a name="a386"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;mm&nbsp;is&nbsp;minutes&nbsp;with&nbsp;leading&nbsp;zero&nbsp;to&nbsp;be&nbsp;2&nbsp;digits&nbsp;always</span></div></li>
<li><div class="src-line"><a name="a387"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array&nbsp;items&nbsp;is&nbsp;[$Fajr,&nbsp;$Sunrise,&nbsp;$Dhuhr,&nbsp;$Asr,&nbsp;$Maghrib,</span></div></li>
<li><div class="src-line"><a name="a388"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$Isha,&nbsp;$Sunset,&nbsp;$Midnight,&nbsp;$Imsak,&nbsp;array&nbsp;$timestamps]</span></div></li>
<li><div class="src-line"><a name="a389"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a390"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Hamid&nbsp;Zarrabi-Zadeh&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a391"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-tag">@source</span><span class="src-doc">&nbsp;http://praytimes.org/calculation</span></div></li>
<li><div class="src-line"><a name="a392"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a393"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime">getPrayTime</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a394"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a395"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$prayTime&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime2">getPrayTime2</a><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a396"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a397"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$prayTime</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a398"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a399"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a400"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a401"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Calculate&nbsp;Salat&nbsp;times&nbsp;for&nbsp;the&nbsp;date&nbsp;set&nbsp;in&nbsp;setSalatDate&nbsp;methode,&nbsp;and</span></div></li>
<li><div class="src-line"><a name="a402"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;location&nbsp;set&nbsp;in&nbsp;setSalatLocation.</span></div></li>
<li><div class="src-line"><a name="a403"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a404"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">array&nbsp;</span><span class="src-doc">of&nbsp;Salat&nbsp;times&nbsp;+&nbsp;sun&nbsp;rise&nbsp;in&nbsp;the&nbsp;following&nbsp;format</span></div></li>
<li><div class="src-line"><a name="a405"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;hh:mm&nbsp;where&nbsp;hh&nbsp;is&nbsp;the&nbsp;hour&nbsp;in&nbsp;local&nbsp;format&nbsp;and&nbsp;24&nbsp;mode</span></div></li>
<li><div class="src-line"><a name="a406"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;mm&nbsp;is&nbsp;minutes&nbsp;with&nbsp;leading&nbsp;zero&nbsp;to&nbsp;be&nbsp;2&nbsp;digits&nbsp;always</span></div></li>
<li><div class="src-line"><a name="a407"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;array&nbsp;items&nbsp;is&nbsp;[$Fajr,&nbsp;$Sunrise,&nbsp;$Dhuhr,&nbsp;$Asr,&nbsp;$Maghrib,</span></div></li>
<li><div class="src-line"><a name="a408"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$Isha,&nbsp;$Sunset,&nbsp;$Midnight,&nbsp;$Imsak,&nbsp;array&nbsp;$timestamps]</span></div></li>
<li><div class="src-line"><a name="a409"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a410"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Hamid&nbsp;Zarrabi-Zadeh&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a411"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-tag">@source</span><span class="src-doc">&nbsp;http://praytimes.org/calculation</span></div></li>
<li><div class="src-line"><a name="a412"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a413"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetPrayTime2">getPrayTime2</a><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a414"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a415"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$unixtimestamp&nbsp;</span>=&nbsp;<a href="http://www.php.net/mktime">mktime</a><span class="src-sym">(</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-num">0</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">month</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">day</span><span class="src-sym">,&nbsp;</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">year</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a416"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a417"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Calculate&nbsp;Julian&nbsp;date</span></div></li>
<li><div class="src-line"><a name="a418"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">month&nbsp;</span>&lt;=&nbsp;<span class="src-num">2</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a419"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$year&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">year&nbsp;</span>-&nbsp;<span class="src-num">1</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a420"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$month&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">month&nbsp;</span>+&nbsp;<span class="src-num">12</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a421"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a422"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$year&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">year</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a423"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$month&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">month</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a424"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a425"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a426"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$A&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$year&nbsp;</span>/&nbsp;<span class="src-num">100</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a427"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$B&nbsp;</span>=&nbsp;<span class="src-num">2&nbsp;</span>-&nbsp;<span class="src-var">$A&nbsp;</span>+&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$A&nbsp;</span>/&nbsp;<span class="src-num">4</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a428"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a429"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$jd&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-num">365.25&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$year&nbsp;</span>+&nbsp;<span class="src-num">4716</span><span class="src-sym">))&nbsp;</span>+&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-num">30.6001&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$month&nbsp;</span>+&nbsp;<span class="src-num">1</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a430"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">day&nbsp;</span>+&nbsp;<span class="src-var">$B&nbsp;</span>-&nbsp;<span class="src-num">1524.5</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a431"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a432"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;following&nbsp;algorithm&nbsp;from&nbsp;U.S.&nbsp;Naval&nbsp;Observatory&nbsp;computes&nbsp;the&nbsp;</span></div></li>
<li><div class="src-line"><a name="a433"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Sun's&nbsp;angular&nbsp;coordinates&nbsp;to&nbsp;an&nbsp;accuracy&nbsp;of&nbsp;about&nbsp;1&nbsp;arcminute&nbsp;within&nbsp;</span></div></li>
<li><div class="src-line"><a name="a434"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;two&nbsp;centuries&nbsp;of&nbsp;2000.&nbsp;</span></div></li>
<li><div class="src-line"><a name="a435"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$d&nbsp;</span>=&nbsp;<span class="src-var">$jd&nbsp;</span>-&nbsp;<span class="src-num">2451545.0</span><span class="src-sym">;&nbsp;&nbsp;</span><span class="src-comm">//&nbsp;jd&nbsp;is&nbsp;the&nbsp;given&nbsp;Julian&nbsp;date&nbsp;</span></div></li>
<li><div class="src-line"><a name="a436"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a437"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;following&nbsp;algorithm&nbsp;from&nbsp;U.S.&nbsp;Naval&nbsp;Observatory&nbsp;computes&nbsp;the&nbsp;Sun's&nbsp;</span></div></li>
<li><div class="src-line"><a name="a438"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;angular&nbsp;coordinates&nbsp;to&nbsp;an&nbsp;accuracy&nbsp;of&nbsp;about&nbsp;1&nbsp;arcminute&nbsp;within&nbsp;two&nbsp;</span></div></li>
<li><div class="src-line"><a name="a439"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;centuries&nbsp;of&nbsp;2000</span></div></li>
<li><div class="src-line"><a name="a440"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;http://aa.usno.navy.mil/faq/docs/SunApprox.php</span></div></li>
<li><div class="src-line"><a name="a441"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Note:&nbsp;mod&nbsp;%&nbsp;in&nbsp;PHP&nbsp;ignore&nbsp;decimels!</span></div></li>
<li><div class="src-line"><a name="a442"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$g&nbsp;</span>=&nbsp;<span class="src-num">357.529&nbsp;</span>+&nbsp;<span class="src-num">0.98560028&nbsp;</span>*&nbsp;<span class="src-var">$d</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a443"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$g&nbsp;</span>=&nbsp;<span class="src-var">$g&nbsp;</span>%&nbsp;<span class="src-num">360&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-var">$g&nbsp;</span>-&nbsp;<a href="http://www.php.net/ceil">ceil</a><span class="src-sym">(</span><span class="src-var">$g</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a444"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a445"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$q&nbsp;</span>=&nbsp;<span class="src-num">280.459&nbsp;</span>+&nbsp;<span class="src-num">0.98564736&nbsp;</span>*&nbsp;<span class="src-var">$d</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a446"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$q&nbsp;</span>=&nbsp;<span class="src-var">$q&nbsp;</span>%&nbsp;<span class="src-num">360&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-var">$q&nbsp;</span>-&nbsp;<a href="http://www.php.net/ceil">ceil</a><span class="src-sym">(</span><span class="src-var">$q</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a447"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a448"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$L&nbsp;</span>=&nbsp;<span class="src-var">$q&nbsp;</span>+&nbsp;<span class="src-num">1.915&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$g</span><span class="src-sym">))&nbsp;</span>+&nbsp;<span class="src-num">0.020&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-num">2&nbsp;</span>*&nbsp;<span class="src-var">$g</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a449"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$L&nbsp;</span>=&nbsp;<span class="src-var">$L&nbsp;</span>%&nbsp;<span class="src-num">360&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-var">$L&nbsp;</span>-&nbsp;<a href="http://www.php.net/ceil">ceil</a><span class="src-sym">(</span><span class="src-var">$L</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-num">1</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a450"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a451"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$R&nbsp;</span>=&nbsp;<span class="src-num">1.00014&nbsp;</span>-&nbsp;<span class="src-num">0.01671&nbsp;</span>*&nbsp;<a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$g</span><span class="src-sym">))&nbsp;</span>-&nbsp;<span class="src-num">0.00014&nbsp;</span>*&nbsp;<a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-num">2&nbsp;</span>*&nbsp;<span class="src-var">$g</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a452"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$e&nbsp;</span>=&nbsp;<span class="src-num">23.439&nbsp;</span>-&nbsp;<span class="src-num">0.00000036&nbsp;</span>*&nbsp;<span class="src-var">$d</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a453"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a454"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$RA&nbsp;</span>=&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/atan2">atan2</a><span class="src-sym">(</span><a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$e</span><span class="src-sym">))</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$L</span><span class="src-sym">))</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$L</span><span class="src-sym">))))</span></div></li>
<li><div class="src-line"><a name="a455"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/&nbsp;<span class="src-num">15</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a456"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a457"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$RA&nbsp;</span>&lt;&nbsp;<span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a458"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$RA&nbsp;</span>=&nbsp;<span class="src-num">24&nbsp;</span>+&nbsp;<span class="src-var">$RA</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a459"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a460"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a461"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;declination&nbsp;of&nbsp;the&nbsp;Sun&nbsp;is&nbsp;the&nbsp;angle&nbsp;between&nbsp;the&nbsp;rays&nbsp;of&nbsp;the&nbsp;sun&nbsp;and&nbsp;</span></div></li>
<li><div class="src-line"><a name="a462"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;the&nbsp;plane&nbsp;of&nbsp;the&nbsp;earth&nbsp;equator.&nbsp;The&nbsp;declination&nbsp;of&nbsp;the&nbsp;Sun&nbsp;changes&nbsp;</span></div></li>
<li><div class="src-line"><a name="a463"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;continuously&nbsp;throughout&nbsp;the&nbsp;year.&nbsp;This&nbsp;is&nbsp;a&nbsp;consequence&nbsp;of&nbsp;the&nbsp;Earth's&nbsp;</span></div></li>
<li><div class="src-line"><a name="a464"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;tilt,&nbsp;i.e.&nbsp;the&nbsp;difference&nbsp;in&nbsp;its&nbsp;rotational&nbsp;and&nbsp;revolutionary&nbsp;axes.&nbsp;</span></div></li>
<li><div class="src-line"><a name="a465"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;declination&nbsp;of&nbsp;the&nbsp;Sun</span></div></li>
<li><div class="src-line"><a name="a466"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$D&nbsp;</span>=&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/asin">asin</a><span class="src-sym">(</span><a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$e</span><span class="src-sym">))</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$L</span><span class="src-sym">))))</span><span class="src-sym">;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a467"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a468"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;equation&nbsp;of&nbsp;time&nbsp;is&nbsp;the&nbsp;difference&nbsp;between&nbsp;time&nbsp;as&nbsp;read&nbsp;from&nbsp;sundial&nbsp;</span></div></li>
<li><div class="src-line"><a name="a469"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;and&nbsp;a&nbsp;clock.&nbsp;It&nbsp;results&nbsp;from&nbsp;an&nbsp;apparent&nbsp;irregular&nbsp;movement&nbsp;of&nbsp;the&nbsp;Sun&nbsp;</span></div></li>
<li><div class="src-line"><a name="a470"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;caused&nbsp;by&nbsp;a&nbsp;combination&nbsp;of&nbsp;the&nbsp;obliquity&nbsp;of&nbsp;the&nbsp;Earth's&nbsp;rotation&nbsp;axis&nbsp;</span></div></li>
<li><div class="src-line"><a name="a471"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;and&nbsp;the&nbsp;eccentricity&nbsp;of&nbsp;its&nbsp;orbit.&nbsp;The&nbsp;sundial&nbsp;can&nbsp;be&nbsp;ahead&nbsp;(fast)&nbsp;by&nbsp;</span></div></li>
<li><div class="src-line"><a name="a472"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;as&nbsp;much&nbsp;as&nbsp;16&nbsp;min&nbsp;33&nbsp;s&nbsp;(around&nbsp;November&nbsp;3)&nbsp;or&nbsp;fall&nbsp;behind&nbsp;by&nbsp;as&nbsp;much&nbsp;as&nbsp;</span></div></li>
<li><div class="src-line"><a name="a473"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;14&nbsp;min&nbsp;6&nbsp;s&nbsp;(around&nbsp;February&nbsp;12),&nbsp;as&nbsp;shown&nbsp;in&nbsp;the&nbsp;following&nbsp;graph:</span></div></li>
<li><div class="src-line"><a name="a474"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;http://en.wikipedia.org/wiki/File:Equation_of_time.png&nbsp;</span></div></li>
<li><div class="src-line"><a name="a475"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$EqT&nbsp;</span>=&nbsp;<span class="src-sym">(</span><span class="src-var">$q</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>-&nbsp;<span class="src-var">$RA</span><span class="src-sym">;&nbsp;&nbsp;</span><span class="src-comm">//&nbsp;equation&nbsp;of&nbsp;time</span></div></li>
<li><div class="src-line"><a name="a476"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a477"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Dhuhr</span></div></li>
<li><div class="src-line"><a name="a478"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;When&nbsp;the&nbsp;Sun&nbsp;begins&nbsp;to&nbsp;decline&nbsp;after&nbsp;reaching&nbsp;its&nbsp;highest&nbsp;point&nbsp;in&nbsp;the&nbsp;sky</span></div></li>
<li><div class="src-line"><a name="a479"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>=&nbsp;<span class="src-num">12&nbsp;</span>+&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">zone&nbsp;</span>-&nbsp;<span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">long</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>-&nbsp;<span class="src-var">$EqT</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a480"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$Dhuhr&nbsp;</span>&lt;&nbsp;<span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a481"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>=&nbsp;<span class="src-num">24&nbsp;</span>+&nbsp;<span class="src-var">$Dhuhr</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a482"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a483"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a484"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Sunrise&nbsp;&amp;&nbsp;Sunset&nbsp;</span></div></li>
<li><div class="src-line"><a name="a485"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;If&nbsp;the&nbsp;observer's&nbsp;location&nbsp;is&nbsp;higher&nbsp;than&nbsp;the&nbsp;surrounding&nbsp;terrain,&nbsp;we&nbsp;</span></div></li>
<li><div class="src-line"><a name="a486"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;can&nbsp;consider&nbsp;this&nbsp;elevation&nbsp;into&nbsp;consideration&nbsp;by&nbsp;increasing&nbsp;the&nbsp;above&nbsp;</span></div></li>
<li><div class="src-line"><a name="a487"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;constant&nbsp;0.833&nbsp;by&nbsp;0.0347&nbsp;×&nbsp;sqrt(elevation),&nbsp;where&nbsp;elevation&nbsp;is&nbsp;the&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a488"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;observer's&nbsp;height&nbsp;in&nbsp;meters.&nbsp;</span></div></li>
<li><div class="src-line"><a name="a489"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$alpha&nbsp;</span>=&nbsp;<span class="src-num">0.833&nbsp;</span>+&nbsp;<span class="src-num">0.0347&nbsp;</span>*&nbsp;<a href="http://www.php.net/sqrt">sqrt</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">elevation</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a490"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;</span>=&nbsp;-<span class="src-num">1&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$alpha</span><span class="src-sym">))&nbsp;</span>-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a491"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$d&nbsp;</span>=&nbsp;<a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a492"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a493"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;date_sun_info&nbsp;Returns&nbsp;an&nbsp;array&nbsp;with&nbsp;information&nbsp;about&nbsp;sunset/sunrise&nbsp;</span></div></li>
<li><div class="src-line"><a name="a494"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;and&nbsp;twilight&nbsp;begin/end</span></div></li>
<li><div class="src-line"><a name="a495"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Sunrise&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>-&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a496"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Sunset&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a497"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a498"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Fajr&nbsp;&amp;&nbsp;Isha</span></div></li>
<li><div class="src-line"><a name="a499"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Imsak:&nbsp;The&nbsp;time&nbsp;to&nbsp;stop&nbsp;eating&nbsp;Sahur&nbsp;(for&nbsp;fasting),&nbsp;slightly&nbsp;before&nbsp;Fajr.</span></div></li>
<li><div class="src-line"><a name="a500"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Fajr:&nbsp;&nbsp;When&nbsp;the&nbsp;sky&nbsp;begins&nbsp;to&nbsp;lighten&nbsp;(dawn).</span></div></li>
<li><div class="src-line"><a name="a501"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Isha:&nbsp;&nbsp;The&nbsp;time&nbsp;at&nbsp;which&nbsp;darkness&nbsp;falls&nbsp;and&nbsp;there&nbsp;is&nbsp;no&nbsp;scattered&nbsp;light&nbsp;</span></div></li>
<li><div class="src-line"><a name="a502"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;in&nbsp;the&nbsp;sky.&nbsp;</span></div></li>
<li><div class="src-line"><a name="a503"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;-<span class="src-num">1&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><a href="http://www.php.net/abs">abs</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">AJ2</span><span class="src-sym">)))&nbsp;</span>-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a504"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a505"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Fajr&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>-&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a506"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Imsak&nbsp;</span>=&nbsp;<span class="src-var">$Fajr&nbsp;</span>-&nbsp;<span class="src-sym">(</span><span class="src-num">10</span>/<span class="src-num">60</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a507"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a508"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;-<span class="src-num">1&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><a href="http://www.php.net/abs">abs</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">AG2</span><span class="src-sym">)))&nbsp;</span>-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a509"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a510"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Isha&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a511"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a512"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Asr</span></div></li>
<li><div class="src-line"><a name="a513"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;following&nbsp;formula&nbsp;computes&nbsp;the&nbsp;time&nbsp;difference&nbsp;between&nbsp;the&nbsp;mid-day&nbsp;</span></div></li>
<li><div class="src-line"><a name="a514"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;and&nbsp;the&nbsp;time&nbsp;at&nbsp;which&nbsp;the&nbsp;object's&nbsp;shadow&nbsp;equals&nbsp;t&nbsp;times&nbsp;the&nbsp;length&nbsp;of&nbsp;</span></div></li>
<li><div class="src-line"><a name="a515"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;the&nbsp;object&nbsp;itself&nbsp;plus&nbsp;the&nbsp;length&nbsp;of&nbsp;that&nbsp;object's&nbsp;shadow&nbsp;at&nbsp;noon</span></div></li>
<li><div class="src-line"><a name="a516"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">school&nbsp;</span>==&nbsp;<span class="src-str">'Shafi'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a517"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;</span>=&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/atan">atan</a><span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-sym">(</span><span class="src-num">1&nbsp;</span>+&nbsp;<a href="http://www.php.net/tan">tan</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat&nbsp;</span>-&nbsp;<span class="src-var">$D</span><span class="src-sym">)))))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a518"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a519"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a520"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;</span>=&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/atan">atan</a><span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-sym">(</span><span class="src-num">2&nbsp;</span>+&nbsp;<a href="http://www.php.net/tan">tan</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat&nbsp;</span>-&nbsp;<span class="src-var">$D</span><span class="src-sym">)))))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a521"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a522"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a523"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Asr&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a524"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a525"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Maghrib</span></div></li>
<li><div class="src-line"><a name="a526"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;In&nbsp;the&nbsp;Sunni's&nbsp;point&nbsp;of&nbsp;view,&nbsp;the&nbsp;time&nbsp;for&nbsp;Maghrib&nbsp;prayer&nbsp;begins&nbsp;once&nbsp;</span></div></li>
<li><div class="src-line"><a name="a527"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;the&nbsp;Sun&nbsp;has&nbsp;completely&nbsp;set&nbsp;beneath&nbsp;the&nbsp;horizon,&nbsp;that&nbsp;is,&nbsp;Maghrib&nbsp;=&nbsp;Sunset&nbsp;</span></div></li>
<li><div class="src-line"><a name="a528"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;(some&nbsp;calculators&nbsp;suggest&nbsp;1&nbsp;to&nbsp;3&nbsp;minutes&nbsp;after&nbsp;Sunset&nbsp;for&nbsp;precaution)</span></div></li>
<li><div class="src-line"><a name="a529"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MaghribSunni&nbsp;</span>=&nbsp;<span class="src-var">$Sunset&nbsp;</span>+&nbsp;<span class="src-num">2</span>/<span class="src-num">60</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a530"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a531"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;In&nbsp;the&nbsp;Shia's&nbsp;view,&nbsp;however,&nbsp;the&nbsp;dominant&nbsp;opinion&nbsp;is&nbsp;that&nbsp;as&nbsp;long&nbsp;as&nbsp;</span></div></li>
<li><div class="src-line"><a name="a532"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;the&nbsp;redness&nbsp;in&nbsp;the&nbsp;eastern&nbsp;sky&nbsp;appearing&nbsp;after&nbsp;sunset&nbsp;has&nbsp;not&nbsp;passed&nbsp;</span></div></li>
<li><div class="src-line"><a name="a533"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;overhead,&nbsp;Maghrib&nbsp;prayer&nbsp;should&nbsp;not&nbsp;be&nbsp;performed.</span></div></li>
<li><div class="src-line"><a name="a534"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$n&nbsp;</span>=&nbsp;-<span class="src-num">1&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-num">4</span><span class="src-sym">))&nbsp;</span>-&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$D</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a535"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MaghribShia&nbsp;</span>=&nbsp;<span class="src-var">$Dhuhr&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-num">1</span>/<span class="src-num">15</span><span class="src-sym">)&nbsp;</span>*&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><a href="http://www.php.net/acos">acos</a><span class="src-sym">(</span><span class="src-var">$n&nbsp;</span>/&nbsp;<span class="src-var">$d</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a536"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a537"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">view&nbsp;</span>==&nbsp;<span class="src-str">'Sunni'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a538"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Maghrib&nbsp;</span>=&nbsp;<span class="src-var">$MaghribSunni</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a539"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a540"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Maghrib&nbsp;</span>=&nbsp;<span class="src-var">$MaghribShia</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a541"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a542"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a543"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Midnight</span></div></li>
<li><div class="src-line"><a name="a544"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Midnight&nbsp;is&nbsp;generally&nbsp;calculated&nbsp;as&nbsp;the&nbsp;mean&nbsp;time&nbsp;from&nbsp;Sunset&nbsp;to&nbsp;Sunrise</span></div></li>
<li><div class="src-line"><a name="a545"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MidnightSunni&nbsp;</span>=&nbsp;<span class="src-var">$Sunset&nbsp;</span>+&nbsp;<span class="src-num">0.5&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$Sunrise&nbsp;</span>-&nbsp;<span class="src-var">$Sunset</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a546"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$MidnightSunni&nbsp;</span>&gt;&nbsp;<span class="src-num">12</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a547"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MidnightSunni&nbsp;</span>=&nbsp;<span class="src-var">$MidnightSunni&nbsp;</span>-&nbsp;<span class="src-num">12</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a548"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a549"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a550"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;In&nbsp;Shia&nbsp;point&nbsp;of&nbsp;view,&nbsp;the&nbsp;juridical&nbsp;midnight&nbsp;(the&nbsp;ending&nbsp;time&nbsp;for&nbsp;</span></div></li>
<li><div class="src-line"><a name="a551"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;performing&nbsp;Isha&nbsp;prayer)&nbsp;is&nbsp;the&nbsp;mean&nbsp;time&nbsp;from&nbsp;Sunset&nbsp;to&nbsp;Fajr</span></div></li>
<li><div class="src-line"><a name="a552"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MidnightShia&nbsp;</span>=&nbsp;<span class="src-num">0.5&nbsp;</span>*&nbsp;<span class="src-sym">(</span><span class="src-var">$Fajr&nbsp;</span>-&nbsp;<span class="src-var">$Sunset</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a553"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$MidnightShia&nbsp;</span>&gt;&nbsp;<span class="src-num">12</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a554"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$MidnightShia&nbsp;</span>=&nbsp;<span class="src-var">$MidnightShia&nbsp;</span>-&nbsp;<span class="src-num">12</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a555"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a556"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a557"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">view&nbsp;</span>==&nbsp;<span class="src-str">'Sunni'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a558"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Midnight&nbsp;</span>=&nbsp;<span class="src-var">$MidnightSunni</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a559"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a560"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Midnight&nbsp;</span>=&nbsp;<span class="src-var">$MidnightShia</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a561"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a562"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a563"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Result.ThlthAkhir:=&nbsp;Result.Fajr-(24-Result.Maghrib&nbsp;+&nbsp;Result.Fajr)/3;</span></div></li>
<li><div class="src-line"><a name="a564"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Result.Doha&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:=&nbsp;Result.Sunrise+(15/60);</span></div></li>
<li><div class="src-line"><a name="a565"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;if&nbsp;isRamadan&nbsp;then&nbsp;(Um-Al-Qura&nbsp;calendar)</span></div></li>
<li><div class="src-line"><a name="a566"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Result.Isha&nbsp;:=&nbsp;Result.Maghrib+2&nbsp;</span></div></li>
<li><div class="src-line"><a name="a567"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;else&nbsp;Result.Isha&nbsp;:=&nbsp;Result.Maghrib+1.5;</span></div></li>
<li><div class="src-line"><a name="a568"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a569"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$times&nbsp;</span>=&nbsp;<span class="src-key">array</span><span class="src-sym">(</span><span class="src-var">$Fajr</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Sunrise</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Dhuhr</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Asr</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Maghrib</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Isha</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Sunset</span><span class="src-sym">,&nbsp;</span></div></li>
<li><div class="src-line"><a name="a570"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$Midnight</span><span class="src-sym">,&nbsp;</span><span class="src-var">$Imsak</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a571"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a572"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;Convert&nbsp;number&nbsp;after&nbsp;the&nbsp;decimal&nbsp;point&nbsp;into&nbsp;minutes&nbsp;</span></div></li>
<li><div class="src-line"><a name="a573"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">foreach&nbsp;</span><span class="src-sym">(</span><span class="src-var">$times&nbsp;</span><span class="src-key">as&nbsp;</span><span class="src-var">$index&nbsp;</span>=&gt;&nbsp;<span class="src-var">$time</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a574"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$hours&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/floor">floor</a><span class="src-sym">(</span><span class="src-var">$time</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a575"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$minutes&nbsp;</span>=&nbsp;<a href="http://www.php.net/round">round</a><span class="src-sym">((</span><span class="src-var">$time&nbsp;</span>-&nbsp;<span class="src-var">$hours</span><span class="src-sym">)&nbsp;</span>*&nbsp;<span class="src-num">60</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a576"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a577"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$minutes&nbsp;</span>&lt;&nbsp;<span class="src-num">10</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a578"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$minutes&nbsp;</span>=&nbsp;<span class="src-str">&quot;</span><span class="src-str">0<span class="src-var">$minutes</span></span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a579"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a580"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a581"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-var">$index</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-str">&quot;</span><span class="src-str"><span class="src-var">$hours</span>:<span class="src-var">$minutes</span></span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a582"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a583"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">9</span><span class="src-sym">]</span><span class="src-sym">[</span><span class="src-var">$index</span><span class="src-sym">]&nbsp;</span>=&nbsp;<span class="src-var">$unixtimestamp&nbsp;</span>+&nbsp;<span class="src-num">3600&nbsp;</span>*&nbsp;<span class="src-var">$hours&nbsp;</span>+&nbsp;<span class="src-num">60&nbsp;</span>*&nbsp;<span class="src-var">$minutes</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a584"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a585"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$index&nbsp;</span>==&nbsp;<span class="src-num">7&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$hours&nbsp;</span>&lt;&nbsp;<span class="src-num">6</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a586"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">9</span><span class="src-sym">]</span><span class="src-sym">[</span><span class="src-var">$index</span><span class="src-sym">]&nbsp;</span>+=&nbsp;<span class="src-num">24&nbsp;</span>*&nbsp;<span class="src-num">3600</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a587"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a588"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a589"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a590"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$times</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a591"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a592"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a593"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a594"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Determine&nbsp;Qibla&nbsp;direction&nbsp;using&nbsp;basic&nbsp;spherical&nbsp;trigonometric&nbsp;formula</span></div></li>
<li><div class="src-line"><a name="a595"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a596"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">float&nbsp;</span><span class="src-doc">Qibla&nbsp;Direction&nbsp;(from&nbsp;the&nbsp;north&nbsp;direction)&nbsp;in&nbsp;degrees</span></div></li>
<li><div class="src-line"><a name="a597"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a598"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;S.&nbsp;Kamal&nbsp;Abdali&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a599"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-tag">@source</span><span class="src-doc">&nbsp;http://www.patriot.net/users/abdali/ftp/qibla.pdf</span></div></li>
<li><div class="src-line"><a name="a600"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a601"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodgetQibla">getQibla</a>&nbsp;<span class="src-sym">(</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a602"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a603"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;The&nbsp;geographical&nbsp;coordinates&nbsp;of&nbsp;the&nbsp;Ka'ba</span></div></li>
<li><div class="src-line"><a name="a604"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$K_latitude&nbsp;&nbsp;</span>=&nbsp;<span class="src-num">21.423333</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a605"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$K_longitude&nbsp;</span>=&nbsp;<span class="src-num">39.823333</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a606"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a607"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$latitude&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a608"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$longitude&nbsp;</span>=&nbsp;<span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">long</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a609"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a610"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$numerator&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$K_longitude&nbsp;</span>-&nbsp;<span class="src-var">$longitude</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a611"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$denominator&nbsp;</span>=&nbsp;<span class="src-sym">(</span><a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$latitude</span><span class="src-sym">))&nbsp;</span>*&nbsp;<a href="http://www.php.net/tan">tan</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$K_latitude</span><span class="src-sym">)))&nbsp;</span>-</div></li>
<li><div class="src-line"><a name="a612"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">(</span><a href="http://www.php.net/sin">sin</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$latitude</span><span class="src-sym">))&nbsp;</span></div></li>
<li><div class="src-line"><a name="a613"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;<a href="http://www.php.net/cos">cos</a><span class="src-sym">(</span><a href="http://www.php.net/deg2rad">deg2rad</a><span class="src-sym">(</span><span class="src-var">$K_longitude&nbsp;</span>-&nbsp;<span class="src-var">$longitude</span><span class="src-sym">)))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a614"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a615"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$q&nbsp;</span>=&nbsp;<a href="http://www.php.net/atan">atan</a><span class="src-sym">(</span><span class="src-var">$numerator&nbsp;</span>/&nbsp;<span class="src-var">$denominator</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a616"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$q&nbsp;</span>=&nbsp;<a href="http://www.php.net/rad2deg">rad2deg</a><span class="src-sym">(</span><span class="src-var">$q</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a617"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a618"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$this</span><span class="src-sym">-&gt;</span><span class="src-var">lat&nbsp;</span>&gt;&nbsp;<span class="src-num">21.423333</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a619"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$q&nbsp;</span>+=&nbsp;<span class="src-num">180</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a620"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a621"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a622"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$q</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a623"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a624"></a>&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a625"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a626"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Convert&nbsp;coordinates&nbsp;presented&nbsp;in&nbsp;degrees,&nbsp;minutes&nbsp;and&nbsp;seconds</span></div></li>
<li><div class="src-line"><a name="a627"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;(i.e.&nbsp;12°34'56&quot;S&nbsp;formula)&nbsp;into&nbsp;usual&nbsp;float&nbsp;number&nbsp;in&nbsp;degree&nbsp;unit&nbsp;scale</span></div></li>
<li><div class="src-line"><a name="a628"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;(i.e.&nbsp;-12.5822&nbsp;value)</span></div></li>
<li><div class="src-line"><a name="a629"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a630"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$value&nbsp;</span><span class="src-doc">Coordinate&nbsp;presented&nbsp;in&nbsp;degrees,&nbsp;minutes&nbsp;and&nbsp;seconds</span></div></li>
<li><div class="src-line"><a name="a631"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(i.e.&nbsp;12°34'56&quot;S&nbsp;formula)</span></div></li>
<li><div class="src-line"><a name="a632"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a633"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">float&nbsp;</span><span class="src-doc">Equivalent&nbsp;float&nbsp;number&nbsp;in&nbsp;degree&nbsp;unit&nbsp;scale</span></div></li>
<li><div class="src-line"><a name="a634"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(i.e.&nbsp;-12.5822&nbsp;value)</span></div></li>
<li><div class="src-line"><a name="a635"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a636"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a637"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Salat.html#methodcoordinate2deg">coordinate2deg</a>&nbsp;<span class="src-sym">(</span><span class="src-var">$value</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a638"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a639"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$pattern&nbsp;</span>=&nbsp;<span class="src-str">&quot;/(\d{1,2})°((\d{1,2})')?((\d{1,2})\&quot;)?([NSEW])/i&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a640"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a641"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/preg_match">preg_match</a><span class="src-sym">(</span><span class="src-var">$pattern</span><span class="src-sym">,&nbsp;</span><span class="src-var">$value</span><span class="src-sym">,&nbsp;</span><span class="src-var">$matches</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a642"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a643"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$degree&nbsp;</span>=&nbsp;<span class="src-var">$matches</span><span class="src-sym">[</span><span class="src-num">1</span><span class="src-sym">]&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-var">$matches</span><span class="src-sym">[</span><span class="src-num">3</span><span class="src-sym">]&nbsp;</span>/&nbsp;<span class="src-num">60</span><span class="src-sym">)&nbsp;</span>+&nbsp;<span class="src-sym">(</span><span class="src-var">$matches</span><span class="src-sym">[</span><span class="src-num">5</span><span class="src-sym">]&nbsp;</span>/<span class="src-num">3600</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a644"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a645"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$direction&nbsp;</span>=&nbsp;<a href="http://www.php.net/strtoupper">strtoupper</a><span class="src-sym">(</span><span class="src-var">$matches</span><span class="src-sym">[</span><span class="src-num">6</span><span class="src-sym">]</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a646"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a647"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$direction&nbsp;</span>==&nbsp;<span class="src-str">'S'&nbsp;</span>||&nbsp;<span class="src-var">$direction&nbsp;</span>==&nbsp;<span class="src-str">'W'</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a648"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$degree&nbsp;</span>=&nbsp;-<span class="src-num">1&nbsp;</span>*&nbsp;<span class="src-var">$degree</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a649"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a650"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a651"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$degree</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a652"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a653"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:22 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>