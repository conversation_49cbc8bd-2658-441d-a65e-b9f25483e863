<?php
ob_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);
include('webset.php');
include('session.php'); 

if (isset($_GET['path'])){
	//echo $_GET['path'];
}

$catid = 0 ;
$productid= 0 ;
$company = 0;
$blogid = 0;

$after_riq = str_replace($Site_URL.'/', '' , $actual_link);
$after_riq = strip_fbclid($after_riq);
$after_riq = rtrim($after_riq, '/');
$urlparts = explode( "/",trim($after_riq) );


$CategoryData = array();

//$removefbid = explode("?fbclid=" , $urlparts);

if(count($urlparts) > 0){
	if ( $urlparts[0] == '' ){
		include('home.php');
	}elseif (strpos($urlparts[0],'?fbclid=') !== false){
		include('home.php');
	//---------------------------------	
	}elseif($urlparts[0] == 'category'){

		if(isset($urlparts[1]) && $urlparts[1] != ''){
			$check = getAllFrom('*' , 'category' , 'WHERE link = "'.urldecode(trim($urlparts[1])).'" ', 'AND status = 1');
			if(count($check) > 0){
				$CategoryData = $check[0];
				if (empty($CategoryData['title'])){
					$Title_page = $CategoryData['name'] ;    
				}else{
					$Title_page = $CategoryData['title'] ;
				}
				$Page_Description = mb_substr( strip_tags( $CategoryData['descr'] ), 0, 300,"utf-8" ) ;
				$Page_KeyWords = $CategoryData['keywords'];
				$CategoryFilter = '';

				if(isset($urlparts[2]) && $urlparts[2] != "" && strpos($urlparts[2], 'filter?') !== false){
					$CategoryFilter = trim(str_replace('filter?' , '' , urldecode(trim($urlparts[2]))));
				}
				include('category.php');
			}else{
				include('error404.php');
			}
		}else{
			include('error404.php');
		}
		/*
		if(count($urlparts) == 2){
			$check = getAllFrom('*' , 'category' , 'WHERE link = "'.urldecode(trim($urlparts[1])).'" ', 'AND status = 1');
			if(count($check) > 0){
				$catid = $check[0]['id'];
			}else{
				include('error404.php');
			}
		}elseif(count($urlparts) == 3){
			$check = getAllFrom('*' , 'category' , 'WHERE link = "'.urldecode(trim($urlparts[2])).'" ', 'AND status = 1');
			if(count($check) > 0){
				$catid = $check[0]['id'];
			}else{
				include('error404.php');
			}
		}elseif (count($urlparts) == 1 && $urlparts[0] == 'category'){
			include('home.php');
		}else{
			
			include('error404.php');
		}
		*/
	//---------------------------------	
	}elseif($urlparts[0] == 'company'){
		if(count($urlparts) == 2){
			$check = getAllFrom('*' , 'users' , 'WHERE cname = "'. urldecode($urlparts[1]).'" ', '');
			if(count($check) > 0){
				$company = $check[0]['id'];
			}else{
				include('error404.php');
			}
		}elseif(count($urlparts) == 3){
			$check = getAllFrom('*' , 'users' , 'WHERE cname = "'. urldecode($urlparts[2]).'" ', '');
			if(count($check) > 0){
				$company = $check[0]['id'];
			}else{
				include('error404.php');
			}
		}elseif (count($urlparts) == 1 && $urlparts[0] == 'company'){
			include('home.php');
		}else{
			
			include('error404.php');
		}	
	//---------------------------------	
	}elseif($urlparts[0] == 'cart'){
		//if (isset($_SESSION['userData'])){ 
			include('cart.php');
		//}else{
			//header('Location: '.$Site_URL.'/register.php'); exit();
		//}
	//--/-------------------------------	
	}elseif($urlparts[0] == 'account'){
		if (isset($_SESSION['userData'])){ 
			include('account.php');
		}else{
			header('Location: '.$Site_URL.'/login.php'); exit();
		}
	//---------------------------------	
	}else{
		$check = getAllFrom('*' , 'products_title' , 'WHERE link = "'.trim(urldecode($urlparts[0])).'" ', '');
        $check2 = getAllFrom('*' , 'blog' , 'WHERE link = "'.trim(urldecode($urlparts[0])).'" ', '');
		if(count($check) > 0){
			$productid = $check[0]['id'];
        }elseif(count($check2) > 0){
			$blogid = $check2[0]['id'];
		}else{
			include('error404.php');
		}
	//---------------------------------	
	}
}else{
	include('error404.php');
}
//---------------------------------------------------------------------------
// this if get category or product or tags
//---------------------------------------------------------------------------
if (isset($_GET['path'])){
/*
if ($catid != 0){
	$categoryx = getAllFrom('*' , 'category' , 'WHERE id = "'.$catid.'" ', 'AND status = 1');
	if (count($categoryx) > 0 ){
		if (empty($categoryx[0]['title'])){
		    $Title_page = $categoryx[0]['name'] ;    
		}else{
		    $Title_page = $categoryx[0]['title'] ;
		}
		$Page_Description = mb_substr( strip_tags( $categoryx[0]['descr'] ), 0, 300,"utf-8" ) ;
		$Page_KeyWords = $categoryx[0]['keywords'];
		//Get_Category_Page($categoryx[0]['id'] , $urlparts);
		if(isset ($urlparts[2])){
			echo $urlparts[2];
		}
		//include('category.php'); 
	}else{
		include('error404.php');	
	}
}
*/
if ($company != 0){
	$company = getAllFrom('*' , 'users' , 'WHERE id = "'.$company.'"  AND type > 0', '');
	if (count($company) > 0 ){
		$Title_page = $company[0]['cname'] ;
		 include('header.php'); 
		 include('navbar.php');
		 Get_company_Page($company[0]['id'] , $urlparts);
		 include('footer.php'); 
	}else{
		include('error404.php');	
	}
}

if ($productid != 0){
	$product = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$productid.'" ', '');
	if (count($product) > 0 ){
		$Title_page = $product[0]['title'] ;
		if (!empty($product[0]['photo'])){
			$cgd = getAllFrom('*' , 'photos' , 'WHERE id = "'.$product[0]['photo'].'" ', '' );
			if (count($cgd) > 0){
				$Page_images = $Site_URL.'/'.$cgd[0]['link'];
			}
		}
		
		$Page_Description = mb_substr( strip_tags( $product[0]['descr'] ), 0, 300,"utf-8" ) ;
		$Page_KeyWords = str_replace(' ', ',', $product[0]['title']) ;
		 include('header.php'); 
		 include('navbar.php'); 
		 if($product[0]['type'] == 1){
		     Get_Post_Content($product[0]['id']);
		 }else{
		    //Get_Product_Content($product[0]['id']);
			include('product.php'); 
		 }
		 include('footer.php'); 
	}else{
		include('error404.php');	
	}
}

  
if ($blogid != 0){
	$bolg = getAllFrom('*' , 'blog' , 'WHERE id = "'.$blogid.'" ', '');
	if (count($bolg) > 0 ){
		$Title_page = $bolg[0]['title'] ;
        $Page_images = $Site_URL.'/'.$bolg[0]['photo'];
		
		
		$Page_Description = mb_substr( strip_tags( $bolg[0]['descr'] ), 0, 300,"utf-8" ) ;
		$Page_KeyWords =  $bolg[0]['tags'] ;
		 include('header.php'); 
		 include('navbar.php'); 
		 Get_ThePost_Content($bolg[0]['id']);
		 include('footer.php'); 
	}else{
		include('error404.php');	
	}
}  
  


}

?>



<?php
//include('footer.php'); 
ob_end_flush();
?>

