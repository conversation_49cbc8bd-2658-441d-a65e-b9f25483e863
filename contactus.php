<?php
ob_start();
$Title_page = 'إتصل بنا' ;
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');
echo '<div class="container-fluid products-content">';

?>
<style>
  .contact-form li {
    list-style:none;
  }
</style>
<div class="container"> 
      <div class="contact">
        <div class="contact-form"> 
          <!-- FORM  -->
        <form role="form" id="contact_form" class="contact-form" method="post">
            <div class="row">
              <div class="col-md-8 col-md-offset-2"> 
              <div class="signal">
                <div class="heading text-center">
                  <h2>إذا كان لديك اى سؤال او استفسار لا تتردد فى التواصل معنا</h2><br>
              </div>
              <ul class="row">
                  <li class="col-sm-6">
                    <label>الإسم بالكامل
                      <input type="text" class="form-control" name="name" id="name" placeholder="">
                  </label>
                </li>
                <li class="col-sm-6">
                    <label>البريد الإلكترونى
                      <input type="text" class="form-control" name="email" id="email" placeholder="">
                  </label>
                </li>
                <li class="col-sm-12">
                    <label>الرسالة
                      <textarea class="form-control" name="message" id="message" rows="5" placeholder=""></textarea>
                  </label>
                </li>
                <li class="col-sm-12 no-margin">
                    <button type="submit" value="submit" name="contactus" class="btn btn-danger btn-md" id="btn_submit">إرسال الرسالة</button>
                </li>
                <li class="col-sm-12">
                  	<?php
                		if (isset($_POST['contactus'])){

  							$var1  = filter_var($_POST['name']  , FILTER_SANITIZE_STRING) ;
  							$var2  = filter_var($_POST['email']  , FILTER_SANITIZE_STRING) ;
  							$var3  = filter_var($_POST['message']  , FILTER_SANITIZE_STRING) ;
  							if (empty($var1) || empty($var2) || empty($var3) ){
  								echo  Show_Alert('danger' , 'يجب عليك ملئ كل الحقول.');
							}else{
  								$stmt = $db->prepare("INSERT INTO contactus ( name , email , mesg , ip , datee) VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 )");  
                $stmt->execute(array( 'user_1' => $var1  , 'user_2' => $var2 , 'user_3' => $var3 , 'user_4' => get_client_ip() , 'user_5' => time() )) ;
                echo '<br>';
								echo Show_Alert('success' , 'تم إرسال رسالتك الى الإداره.') ;
								echo Show_Alert('success' , 'سيتم الرد عليها خلال 24 ساعه.') ;
							}		
						}	
						?>
                </li>	
              </ul>
          </div>
                
                
                
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
<?php
echo ' </div>';
include('footer.php'); 
ob_end_flush();
?>
