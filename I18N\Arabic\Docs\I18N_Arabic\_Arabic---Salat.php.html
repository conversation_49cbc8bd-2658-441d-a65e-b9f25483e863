<html>
<head>
<title>Docs for page Salat.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../I18N_Arabic/_Arabic.php.html">		Arabic.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---AutoSummarize.php.html">		AutoSummarize.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CharsetD.php.html">		CharsetD.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---CompressStr.php.html">		CompressStr.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Date.php.html">		Date.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Gender.php.html">		Gender.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Glyphs.php.html">		Glyphs.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Hiero.php.html">		Hiero.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Identifier.php.html">		Identifier.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---KeySwap.php.html">		KeySwap.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Mktime.php.html">		Mktime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Normalise.php.html">		Normalise.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Numbers.php.html">		Numbers.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Query.php.html">		Query.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Salat.php.html">		Salat.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Soundex.php.html">		Soundex.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Standard.php.html">		Standard.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Stemmer.php.html">		Stemmer.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---StrToTime.php.html">		StrToTime.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---Transliteration.php.html">		Transliteration.php
		</a><br>
			<a href="../I18N_Arabic/_Arabic---WordTag.php.html">		WordTag.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../I18N_Arabic/ArabicException.html">ArabicException</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_AutoSummarize.html">I18N_Arabic_AutoSummarize</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CharsetD.html">I18N_Arabic_CharsetD</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_CompressStr.html">I18N_Arabic_CompressStr</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Date.html">I18N_Arabic_Date</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Glyphs.html">I18N_Arabic_Glyphs</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Hiero.html">I18N_Arabic_Hiero</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Identifier.html">I18N_Arabic_Identifier</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_KeySwap.html">I18N_Arabic_KeySwap</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Mktime.html">I18N_Arabic_Mktime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Normalise.html">I18N_Arabic_Normalise</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Numbers.html">I18N_Arabic_Numbers</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Query.html">I18N_Arabic_Query</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Soundex.html">I18N_Arabic_Soundex</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Standard.html">I18N_Arabic_Standard</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Stemmer.html">I18N_Arabic_Stemmer</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_StrToTime.html">I18N_Arabic_StrToTime</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_Transliteration.html">I18N_Arabic_Transliteration</a><br />
	    		<a href="../I18N_Arabic/I18N_Arabic_WordTag.html">I18N_Arabic_WordTag</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: Salat.php</h1>
Source Location: /Arabic/Salat.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../I18N_Arabic/I18N_Arabic_Salat.html">I18N_Arabic_Salat</a></dt>
	<dd>This PHP class calculate the time of Muslim prayer according to the geographic  location.</dd>
</div><br /><br />

<h2>Page Details:</h2>
----------------------------------------------------------------------<br /><br /><p>Copyright (c) 2006-2016 Khaled Al-Sham'aa</p><p>http://www.ar-php.org</p><p>PHP Version 5</p><p>----------------------------------------------------------------------</p><p>LICENSE</p><p>This program is open source product; you can redistribute it and/or  modify it under the terms of the GNU Lesser General Public License (LGPL)  as published by the Free Software Foundation; either version 3  of the License, or (at your option) any later version.</p><p>This program is distributed in the hope that it will be useful,  but WITHOUT ANY WARRANTY; without even the implied warranty of  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the  GNU Lesser General Public License for more details.</p><p>You should have received a copy of the GNU Lesser General Public License  along with this program.  If not, see &lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</p><p>----------------------------------------------------------------------</p><p>Class Name: Muslim Prayer Times</p><p>Filename:   Salat.php</p><p>Original    Author(s): Khaled Al-Sham'aa &lt;<EMAIL>&gt;</p><p>Purpose:    The five Islamic prayers are named Fajr, Zuhr, Asr, Maghrib              and Isha. The timing of these five prayers varies from place              to place and from day to day. It is obligatory for Muslims              to perform these prayers at the correct time.</p><p>----------------------------------------------------------------------</p><p>Source: http://qasweb.org/qasforum/index.php?showtopic=177&amp;st=0  By: Mohamad Magdy &lt;<EMAIL>&gt;</p><p>----------------------------------------------------------------------</p><p>Muslim Prayer Times</p><p>Using this PHP Class you can calculate the time of Muslim prayer  according to the geographic location.</p><p>The five Islamic prayers are named Fajr, Zuhr, Asr, Maghrib and Isha. The timing  of these five prayers varies from place to place and from day to day. It is  obligatory for Muslims to perform these prayers at the correct time.</p><p>The prayer times for any given location on earth may be determined mathematically  if the latitude and longitude of the location are known. However, the theoretical  determination of prayer times is a lengthy process. Much of this tedium may be  alleviated by using computer programs.</p><p>Definition of prayer times</p><p><ul><li>FAJR starts with the dawn or morning twilight. Fajr ends just before sunrise.</li><li>ZUHR begins after midday when the trailing limb of the sun has passed the
   meridian. For convenience, many published prayer timetables add five minutes to
   mid-day (zawal) to obtain the start of Zuhr. Zuhr ends at the start of Asr time.</li><li>The timing of ASR depends on the length of the shadow cast by an object.
   According to the Shafi school of jurisprudence, Asr begins when the length of
   the shadow of an object exceeds the length of the object. According to the
   Hanafi school of jurisprudence, Asr begins when the length of the shadow
   exceeds TWICE the length of the object. In both cases, the minimum length of
   shadow (which occurs when the sun passes the meridian) is subtracted from the
   length of the shadow before comparing it with the length of the object.</li><li>MAGHRIB begins at sunset and ends at the start of isha.</li><li>ISHA starts after dusk when the evening twilight disappears.</li></ul>  Example:  <ol><li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/date_default_timezone_set">date_default_timezone_set</a><span class="src-sym">(</span><span class="src-str">'UTC'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'Salat'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setLocation</span><span class="src-sym">(</span><span class="src-num">33.513</span><span class="src-sym">,</span><span class="src-num">36.292</span><span class="src-sym">,</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setDate</span><span class="src-sym">(</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'j'</span><span class="src-sym">)</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'n'</span><span class="src-sym">)</span><span class="src-sym">,&nbsp;</span><a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'Y'</span><span class="src-sym">))</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$times&nbsp;</span>=&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">getPrayTime</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">'&lt;b&gt;Damascus,&nbsp;Syria&lt;/b&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<a href="http://www.php.net/date">date</a><span class="src-sym">(</span><span class="src-str">'l&nbsp;F&nbsp;j,&nbsp;Y'</span><span class="src-sym">)</span>.<span class="src-str">'&lt;br&nbsp;/&gt;&lt;br&nbsp;/&gt;'</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Imsak:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">8</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Fajr:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">0</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Sunrise:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">1</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Zuhr:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">2</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Asr:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">3</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Sunset:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">6</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Maghrib:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">4</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Isha:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">5</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&nbsp;class=hilight&gt;Midnight:&lt;/b&gt;&nbsp;{<span class="src-var">$times</span><span class="src-sym">[</span><span class="src-num">7</span><span class="src-sym">]</span><span class="src-sym">}</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
</ol></p><p>Qibla Determination Methods - Basic Spherical Trigonometric Formula</p><p>The problem of qibla determination has a simple formulation in spherical  trigonometry. A is a given location, K is the Ka'ba, and N is the North Pole.  The great circle arcs AN and KN are along the meridians through A and K,  respectively, and both point to the north. The qibla is along the great circle  arc AK. The spherical angle q = NAK is the angle at A from the north direction  AN to the direction AK towards the Ka'ba, and so q is the qibla bearing to be  computed. Let F and L be the latitude and longitude of A, and FK and LK be  the latitude and longitude of K (the Ka'ba). If all angles and arc lengths  are measured in degrees, then it is seen that the arcs AN and KN are of measure  90 - F and 90 - FK, respectively. Also, the angle ANK between the meridians  of K and A equals the difference between the longitudes of A and K, that is,  LK - L, no matter what the prime meridian is. Here we are given two sides and  the included angle of a spherical triangle, and it is required to determine one  other angle. One of the simplest solutions is given by the formula:  <pre>                       -1              sin(LK - L)
                q = tan   ------------------------------------------
                              cos F tan FK - sin F cos(LK - L)</pre>  In this Equation, the sign of the input quantities are assumed as follows:  latitudes are positive if north, negative if south; longitudes are positive  if east, negative if west. The quadrant of q is assumed to be so selected  that sin q and cos q have the same sign as the numerator and denominator of  this Equation. With these conventions, q will be positive for bearings east  of north, negative for bearings west of north.</p><p>Reference:  The Correct Qibla, S. Kamal Abdali &lt;<EMAIL>&gt;  PDF version in http://www.patriot.net/users/abdali/ftp/qibla.pdf</p><p>Example:  <ol><li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="http://www.php.net/date_default_timezone_set">date_default_timezone_set</a><span class="src-sym">(</span><span class="src-str">'UTC'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-inc">include</span><span class="src-sym">(</span><span class="src-str">'./I18N/Arabic.php'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj&nbsp;</span>=&nbsp;<span class="src-key">new&nbsp;</span><span class="src-id"><a href="../I18N_Arabic/I18N_Arabic.html">I18N_Arabic</a></span><span class="src-sym">(</span><span class="src-str">'Salat'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">setLocation</span><span class="src-sym">(</span><span class="src-num">33.513</span><span class="src-sym">,</span><span class="src-num">36.292</span><span class="src-sym">,</span><span class="src-num">2</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;</div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$direction&nbsp;</span>=&nbsp;<span class="src-var">$obj</span><span class="src-sym">-&gt;</span><span class="src-id">getQibla</span><span class="src-sym">(</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;<span class="src-str">&quot;</span><span class="src-str">&lt;b&gt;Qibla&nbsp;Direction&nbsp;(from&nbsp;the&nbsp;north&nbsp;direction):&lt;/b&gt;&nbsp;<span class="src-var">$direction</span>&lt;br&nbsp;/&gt;</span><span class="src-str">&quot;</span><span class="src-sym">;</span></div></li>
</ol></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Khaled Al-Sham'aa &lt;<a href="mailto:<EMAIL>"><EMAIL></a>&gt;</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>2006-2016 Khaled Al-Sham'aa</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.ar-php.org">http://www.ar-php.org</a></td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_I18N_Arabic__ArabicSalat.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td>LGPL</td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:26:19 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
