<?php
ob_start();
$Title_page = 'الشركات' ;
include('../webset.php');
include('../session.php'); 
include('header.php'); 
include('navbar.php');

//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'block' && isset($_GET['id']) ){
	$ch = getAllFrom('*' , 'users' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		if ($ch[0]['active'] == 0){
			UpdateTable('users' , 'active' ,1 , 'WHERE id = '.$_GET['id'] );
		}else{
			UpdateTable('users' , 'active' ,0 , 'WHERE id = '.$_GET['id'] );
		}
	}
	redirect_home ('back' , 0);  exit();
}
//---------------------------------------------------
if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){
	$ch = getAllFrom('*' , 'users' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
		
			DeleteColum( 'users', 'WHERE id = '.$_GET['id'] );
			DeleteColum( 'products', 'WHERE userid = '.$_GET['id'] );
			
			header('Location: merchants.php');	exit();
	}else{
		redirect_home ('back' , 0); exit();
	} 
}
//---------------------------------------------------
if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'users' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
?>
<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body text-center">
		     
		     <h3>هل انت متأكد من انك تريد حذف <b>" <?php echo $ch[0]['fullname'];?> " </b> ؟</h3>
		     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>
		     <p>برجاء العلم انه سيتم  حذف جميع منتجات هذه  الشركة  .</p><br>

		     <center>
		     	<a class="btn btn-danger btn-lg" href="merchants.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>
		     	<a class="btn btn-success btn-lg" href="merchants.php">رجوع</a>
		     </center>
		 
	</div>	
	</div>
</div>
</div>
<?php
	}else{
		header('Location: merchants.php'); exit();
	} 
//---------------------------------------------------
}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة  شركة جديدة </h4>
				</div>
			</div>
			<br>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['add_new'])){
	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
						$var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;
						$var3  = filter_var($_POST['var3']   , FILTER_SANITIZE_STRING) ;
						$var4  = filter_var($_POST['var4']   , FILTER_SANITIZE_STRING) ;
						$var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;
						$var6  = filter_var($_POST['var6']   , FILTER_SANITIZE_STRING) ;
						$var7  = filter_var($_POST['var7']   , FILTER_SANITIZE_STRING) ;

						$ch1 = getAllFrom('*' , 'users' , 'WHERE phone = "'.$var5.'" ', '');
						if (count($ch1) > 0){
							echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'رقم الموبايل موجود من قبل. ');
					        echo '</div>';  
					    }elseif(empty($var1) || empty($var2) || empty($var3) || empty($var4) || empty($var5) || empty($var7) ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>';  
						}else{

							$stmt = $db->prepare("INSERT INTO users ( cname , password , fullname , cityid , phone , type , datetime , adress ) 
							 VALUES (:user_1 ,:user_2 ,:user_3 ,:user_4 ,:user_5 ,:user_6 ,:user_7 ,:user_8)");  
							$stmt->execute(array(
					          'user_1' => $var1  , 'user_2' => $var2 , 'user_3' => $var3 , 'user_4' => $var4 , 'user_5' => $var5 , 'user_6' => $var6 , 'user_7' => time()  , 'user_8' => $var7)) ;


					        echo '<div class="col-md-12">';
					        echo  Show_Alert('success' , 'تم إضافة  شركة جديدةبنجاح. ');
					        echo  Show_Alert('success' , 'سيتم تحويلك الى صفحة الشركات خلال 1 ثانيه. ');
					        echo '</div>';  		 	
					        header("refresh:1;url=merchants.php");
					        exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">أسم  الشركة</label>
		                      <input type="text" name="var1" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">كلمة المرور</label>
		                      <input type="text" name="var2" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم بالكامل</label>
		                      <input type="text" name="var3" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">المحافظه</label>
		                      <select name="var4" class="form-control">
		                      		<option value="0">إختر المحافظه</option>
		                      		<?php
		                      			$c = getAllFrom('*' , 'cities' , 'WHERE status = 1 ', 'ORDER BY orders DESC , id DESC');
										if (count($c) > 0){
											for ($i=0; $i <= count($c)-1 ; $i++) { 
												echo '<option value="'.$c[$i]['id'].'">'.$c[$i]['name'].'</option>';
											}
										}
		                      		?>
		                      </select>
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">رقم الموبايل</label>
		                      <input type="text" name="var5" class="form-control">
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">نوع العضويه</label>
		                      <select class="form-control" name="var6">
		                      	<option value="1">عضوية  شركة</option>
		                      	
		                      </select>
		                    </div>
	               	 	</div>
	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">العنوان </label>
		                      <input type="text" name="var7" class="form-control">
		                    </div>
	               	 	</div>
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>		
<?php	
//---------------------------------------------------	
}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){
	$ch = getAllFrom('*' , 'users' , 'WHERE id = "'.$_GET['id'].'" ', '');
	if (count($ch) > 0 ){
	?>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">
					<h4 class="cattitlel"> <i class="fa fa-hand-o-left"></i> التعديل على "<?php echo $ch[0]['cname'];?>"</h4>
				</div>
			</div><br>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 col-sm-12">
			<div class="card">
				<div class="card-body">				
					<?php
	                 if (isset($_POST['edit'])){
	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;
										$var2  = filter_var($_POST['var2']   , FILTER_SANITIZE_STRING) ;
										$var3  = filter_var($_POST['var3']   , FILTER_SANITIZE_STRING) ;
										$var4  = filter_var($_POST['var4']   , FILTER_SANITIZE_STRING) ;
										$var5  = filter_var($_POST['var5']   , FILTER_SANITIZE_STRING) ;
										$var6  = filter_var($_POST['var6']   , FILTER_SANITIZE_STRING) ;
										$var7  = filter_var($_POST['var7']   , FILTER_SANITIZE_STRING) ;
										$var8  = filter_var($_POST['var8']   , FILTER_SANITIZE_STRING) ;
										$var9  = filter_var($_POST['var9']   , FILTER_SANITIZE_STRING) ;
										$var0  = filter_var($_POST['var0']   , FILTER_SANITIZE_STRING) ;
						
						if(empty($var1) || empty($var2) || empty($var3) || empty($var4) || empty($var5) || empty($var8) ){
					    	echo '<div class="col-md-12">';
					        echo  Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول .');
					        echo '</div>'; 
					    }else{
						$stmt = $db->prepare("UPDATE users
						    SET cname = :var1  ,
						    	password = :var2  ,
						    	fullname = :var3  ,
						    	cityid = :var4  ,
						    	phone = :var5  ,
						    	type = :var6  ,
						    	ip = :var7 ,  
						    	adress = :var8 ,  
						    	certified = :var9   
						    	WHERE  id = :var0 ");  
				           $stmt->execute(array(
				            'var1' => $var1 ,
				            'var2' => $var2 ,
				            'var3' => $var3 ,
				            'var4' => $var4 ,
				            'var5' => $var5 ,
				            'var6' => $var6 ,
				            'var7' => $var7 ,
				            'var8' => $var8 ,
				            'var9' => $var9 ,
				            'var0' => $_GET['id']
				          )); 	
				        echo '<div class="col-md-12">';
				        echo  Show_Alert('success' , 'تم حفظ التعديلات بنجاح. ');
				        echo '</div>';  		 	

				        redirect_home ('back' , 1); exit();
				    	}
	                 }
	                 ?>
					<form method="post">
						<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">إسم  الشركة</label>
		                      <input type="text" name="var1" value="<?php echo $ch[0]['cname'];?>" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">كلمة المرور</label>
		                      <input type="text" name="var2" value="<?php echo $ch[0]['password'];?>" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الأسم بالكامل</label>
		                      <input type="text" name="var3" value="<?php echo $ch[0]['fullname'];?>" class="form-control">
		                    </div>
	               	 	</div>
	               	 	
	               	 	<div class="col-md-6">
		                    <div class="form-group">   
		                      <label class="form-control-label">المحافظه</label>    
		                      <select name="var4" class="form-control">
		                      		<option value="0">إختر المحافظه</option>
		                      		<?php
		                      			$c = getAllFrom('*' , 'cities' , 'WHERE status = 1 ', 'ORDER BY orders DESC , id DESC');
										if (count($c) > 0){
											for ($i=0; $i <= count($c)-1 ; $i++) { 
												$cls = '';
												if ( $ch[0]['cityid'] == $c[$i]['id']){ $cls = 'selected';}
												echo '<option value="'.$c[$i]['id'].'" '.$cls.'>'.$c[$i]['name'].'</option>';
											}
										}
		                      		?>
		                      </select>
		                    </div>
		                </div>

		                <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">الموبايل</label>
		                      <input type="text" name="var5" value="<?php echo $ch[0]['phone'];?>" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label">عنوان الأى بى</label>
		                      <input type="text" name="var7" value="<?php echo $ch[0]['ip'];?>" class="form-control">
		                    </div>
	               	 	</div>

	               	 	<div class="col-md-6">
		                    <div class="form-group">   
		                      <label class="form-control-label">نوع العضويه</label>    
		                      <select class="form-control" name="var6">
		                      	<option value="0" <?php if ($ch[0]['type'] == 0){echo 'selected' ;} ?>>عادية</option>
		                      	<option value="1" <?php if ($ch[0]['type'] == 1){echo 'selected' ;} ?>> شركة</option>
		                      	<option value="2" <?php if ($ch[0]['type'] == 2){echo 'selected' ;} ?>>أدمن</option>
		                  	  </select>
		                    </div>
		                </div>

		                <div class="col-md-6">
		                    <div class="form-group">
		                      <label class="form-control-label"> العنوان</label>
		                      <input type="text" name="var8" value="<?php echo $ch[0]['adress'];?>" class="form-control">
		                    </div>
											</div>
											
											<div class="col-md-6">
		                    <div class="form-group">   
		                      <label class="form-control-label">موزع معتمد</label>    
		                      <select class="form-control" name="var9">
		                      	<option value="0" <?php if ($ch[0]['certified'] == 0){echo 'selected' ;} ?>>غير معتمد</option>
		                      	<option value="1" <?php if ($ch[0]['certified'] == 1){echo 'selected' ;} ?>> معتمد</option>
		                  	  </select>
		                    </div>
		                </div>

		                <input type="hidden" name="var0" value="<?php echo $ch[0]['id'];?>">
	                    <div class="col-md-12">
		                    <div class="form-group">       
		                      <input type="submit" value="تعديل" name="edit" class="btn btn-lg btn-primary">
		                    </div>
		                </div>    
	                 </form>
	                 
				</div>
			</div>
		</div>
	</div>				
	<?php	
	}else{
		header('Location: merchants.php'); exit();
	} 
//---------------------------------------------------	
}else{
?>
<div class="row">
	<div class="col-md-12 col-sm-12">
		<div class="card">
			<div class="card-body">
				<a href="merchants.php?do=add_new" class="btn btn-lg btn-success">إضافة  شركة جديدة</a>
			</div>
		</div><hr>
	</div>			
</div>
<div class="row">
	<div class="col-md-12 col-lg-12">
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="datatables" class="table table-striped table-bordered text-nowrap w-100 dataTable no-footer text-center" >
						<thead>
							<tr role="row">
								<th class="all">إسم الشركة</th>
								<th class="mobile-p desktop">الأسم بالكامل</th>
								<th class="mobile-p desktop">رقم الموبايل</th>
								<th class="mobile-p desktop">المحافظه</th>
								<th class="mobile-p desktop">عدد الأوردرات</th>
								<th class="none"> العنوان</th>
								<th class="none">تاريخ الإنضمام</th>
                            	<th class="mobile-p desktop">معتمد</th>
                            	<th class="mobile-p desktop">الإجراء</th>
							</tr>
						</thead>
						<tbody id="DataTableAll"></tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

 
<?php
}
include('footer.php'); 
ob_end_flush();
?>
<script>
	function GetData(){
        $('#datatables').hide();
        $.post("ajax.php", { action : 'DataTableAll' , type : 'merchants'} ,function(data){ 
            if ( $.fn.DataTable.isDataTable('#datatables') ) {
              $('#datatables').DataTable().destroy();
            }
            $('#DataTableAll').html(data);  
              table = $('#datatables').DataTable({
                "pagingType": "full_numbers",
                "lengthMenu": [
                  [20, 50, 100, 200, 500, 1000, -1],
                  [20, 50, 100, 200, 500, 1000, "كل  الشركات"]
                ],
                'destroy': true,
                responsive:true,
                //"order": [[ 2, "asc" ]],
                language: {
                  search: "البحث",
                  searchPlaceholder: "البحث عن شركة",
                }
              });

            $('#datatables').show(200);  

        });
    }

    $(document).ready(function() {
      $('#datatables').hide();
      GetData();
    });
</script>