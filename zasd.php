<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

ob_start();
include('webset.php');

header('Content-Type: text/html; charset=utf-8');


$c =  getAllFrom('id,name,parent' , 'cities' , '', '');
$x = [];
for ($i=0; $i <= count($c)-1 ; $i++) {
    array_push($x , array(
        "id" => $c[$i]['id'],
        "name" => $c[$i]['name'],
        "en_name" => '',
        "parent" => $c[$i]['parent'],
    ));
}
echo json_encode($x , JSON_UNESCAPED_UNICODE);
/*
$all = [];

if(isset($_GET['action']) && $_GET['action'] == 'products'){
$product  = getAllFrom('*' , 'products_title' , '', '');
for ($i=0; $i <= count($product)-1 ; $i++) {

    $cats = array();
    $photos = array();


    //- category
    $cx = explode(',' , $product[$i]['catid']);
    for ($x=0; $x <= count($cx)-1 ; $x++) { 
        if(trim($cx[$x]) != ''){
            $ch  = getAllFrom('*' , 'category' , 'WHERE id = "'.$cx[$x].'"', '');
            if(count($ch) > 0){
                if(!in_array( $ch[0]['name'] , $cats)){
                    array_push($cats , $ch[0]['name']);
                }
            }
        }
    }

    //- photos
    $cx = explode('&&&' , $product[$i]['photo']);
    for ($x=0; $x <= count($cx)-1 ; $x++) { 
        if(trim($cx[$x]) != ''){
            $ch  = getAllFrom('*' , 'photos' , 'WHERE id = "'.$cx[$x].'"', '');
            if(count($ch) > 0){
                if(!in_array( $ch[0]['link'] , $photos)){
                    array_push($photos , $ch[0]['link']);
                }
            }
        }
    }

    $p = array(
        'title' => $product[$i]['title'],
        'descr' => $product[$i]['descr'],
        'cats' => array_reverse($cats),
        'photos' => $photos,
        'video' => $product[$i]['video'],
        'price' => $product[$i]['price'],
        'quantity' => $product[$i]['Quantity'] == '' ? 0 : $product[$i]['Quantity'],
    );

    if(count($cats) > 0 && count($photos) > 0){
        array_push($all , $p);
    }
    
}
}


if(isset($_GET['action']) && $_GET['action'] == 'category'){
    $category  = getAllFrom('*' , 'category' , '', '');
    for ($i=0; $i <= count($category)-1 ; $i++) { 
        $p = array(
            'id' => $category[$i]['id'],
            'name' => $category[$i]['name'],
            'title' => $category[$i]['title'], 
            'photo' => $category[$i]['photo'],
            'parent' => $category[$i]['parent'],
            'order' => $category[$i]['id'],
        );
    
        array_push($all , $p);
    }

}
//echo json_encode($all , JSON_UNESCAPED_UNICODE);
echo json_encode($all );

*/