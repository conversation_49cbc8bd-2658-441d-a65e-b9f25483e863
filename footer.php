</div>
<div class="footer-container ">
  


    <div class="footer">
            <div class="footer-middle">
                 <div class="container">
                <div class="row">
               <div class="col-sm-12">
                    
                    <div class="col-sm-3">
                        <p>
                            <span class="footer-logo">
                                <img style="width: 140px; " alt="<?php echo $Site_Name ;?>" src="/files/1744191736.png" />
                            </span>
                        </p>
                        <div class="footer-paragraph">
                            <p><?php echo GetTableSet ('FooterDesc');?></p>
                        </div>

                    </div> 
                    <div class="col-sm-1">
                        <div class="block">

                        </div>
                    </div>         

                    <div class="col-sm-6">
                        <div class="block">
                            <div class="block-title"><strong><span>تصنيفات المتجر</span></strong></div>
                            <div class="block-content">
                                <?php
                                for ($i=0; $i <= count($categoryx)-1 ; $i++) { 
                                ?>
                                <div class="col-xs-4" style="padding-right: 0;">
                                    <ul class="footer-links">
                                        <li><a href="<?php echo $Site_URL.'/category/'.$categoryx[$i]['link'] ;?>"><?php echo $categoryx[$i]['name'];?></a></li>
                                    </ul>
                                </div>

                                <?php
                                }
                                ?>
                                
                            </div>
                        </div>
                    </div>   
                    
                 
                                        
                                </div>
                 <div class="col-sm-12">
                        <div class="block">
                            <div class="block-title"><strong><span>صفحات هامه</span></strong></div>
                                <ul class="footer-page">
                <a href="/exchange-and-return-policy.php">سياسة الاستبدال والاسترجاع </a>
                <a href="/privacy.php">سياسة الخصوصية </a>
                <a href="/about.php">عن متجر شي بوكس </a>
                <a href="/contactus.php">اتصل بنا </a>

            </ul>
                        </div>
                    </div> 


            </div>
        </div>

        <div class="footer-bottom">
            <div class="container">                                
                <address> جميع الحقوق محفوظة © <a href="<?php echo $Site_URL;?>/"><?php echo $Site_Name ;?></a></address>
                <div class="social-links"> 
	          	<?php
	          		if (!empty(GetTableSet ('Social_Face'))){
	          			echo '<a class="col1" href="'.GetTableSet ('Social_Face').'"><i class="fa fa-facebook"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_twitter'))){
	          			echo '<a class="col2" href="'.GetTableSet ('Social_twitter').'"><i class="fa fa-twitter"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_insta'))){
	          			echo '<a class="col3" href="'.GetTableSet ('Social_insta').'"><i class="fa fa-instagram"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_google'))){
	          			echo '<a class="col4" href="'.GetTableSet ('Social_google').'"><i class="fa fa-google-plus"></i></a>';
	          		}
	          		if (!empty(GetTableSet ('Social_youtube'))){
	          			echo '<a class="col5" href="'.GetTableSet ('Social_youtube').'"><i class="fa fa-youtube"></i></a>';
	          		}
	          	?>
	          </div>                
            </div>
        </div>
    </div>
</div>

<script src="<?php echo $Site_URL;?>/layout/js/jquery-1.12.4.js"></script>
<script src="<?php echo $Site_URL;?>/layout/js/bootstrap.min.js"></script>
<script src="<?php echo $Site_URL;?>/layout/js/main.js"></script>
<!-- <script src="<?php echo $Site_URL;?>/layout/js/timer.js"></script> -->
<script src="https://kenwheeler.github.io/slick/slick/slick.js"></script>
<script src="<?php echo $Site_URL;?>/layout/js/jquery.desoslide.min.js"></script>

<script src="<?php echo $Site_URL;?>/layout/js/sweetalert2.js"></script>
<script src="<?php echo $Site_URL;?>/layout/js/js.js"></script>
<script>
    var site_url = "<?php echo $Site_URL;?>";
    $(document).on("ready", function(e) {
        CountCart();
        window.setTimeout(function() {CountCart();}, 5000);
    });
    
    function GetMoreProduct(lastproduct , catid){
        $('#btn-more').html('<center><i style="font-size: 50px;" class="fa fa-spinner fa-spin fa-font"></i></center>');
        $.post(site_url+"/ajax.php", { action : 'GetMoreProduct' , lastproduct:lastproduct , catid : catid } ,function(data){
            //console.log(data);
           $('#willdel').remove();
           $('#moreproducts').append(data);
        });
    }
  
  	function ShowContent(){
      $('.contentz').css('height' , 'auto');
      $('#showmorez').remove();
    }
    
    
    function calcPrice(){
        var allc = parseInt($('#countc').val());
        var price = 0 ;
        for (var i = 0; i <= allc-1; i++) {
            var pc = parseInt($('#productcount'+i).val());
            var pp = parseInt($('#price'+i).val());
            price = price + (pc * pp);
        }
        $('#totalprice').text(price);
    }
    function ShowCart(){
        $("#CartData").modal("show");
		$('#CartDataRes').html('<center><i style="font-size: 50px;" class="fa fa-spinner fa-spin fa-font"></i></center>');
        $.post(site_url+"/ajax.php", { action : 'ShowCart'} ,function(data){
            $('#CartDataRes').html(data);
        });
    }
    function RemoveFromCart(productid){
        $.post(site_url+"/ajax.php", { action : 'RemoveFromCart' , productid:productid} ,function(data){
            CountCart();
            ShowCart();
        });
    }
    function RemoveAllFromCart(){
        Swal({
        title: 'هل انت متأكد من انك تريد إفراغ السلة؟',
        text: "",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم',
        cancelButtonText: 'لا',
      }).then((result) => {
        if (result.value) {
            $.post(site_url+"/ajax.php", { action : 'RemoveAllFromCart'} ,function(data){
                CountCart();
                ShowCart();
            });
        }
      })
    }
    function CountCart(){
        $.post(site_url+"/ajax.php", { action : 'CountCart'} ,function(data){
            $('#ccrt').text(data);
            $('#cart_count').text(data);
            if (parseInt(data) > 0){
            $('#cart_count').show();
            }else{
            $('#cart_count').hide();
            }
        });
    }
    function AddToCart(productid){
        Swal({
        title: 'هل انت متأكد من إضافة المنتج للسلة؟',
        text: "",
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم',
        cancelButtonText: 'لا',
      }).then((result) => {
        if (result.value) {
            $.post(site_url+"/ajax.php", { action : 'AddToCart' , productid : productid } ,function(data){ 
                CountCart();  ShowCart();
            }); 
        }
      })
		
	}

    function ShowForm(){
        $('#btn1').hide();
        $('#btn2').show();
        $('#userdatacart').show();
    }
    function ConfirmBuy(){
        $('#btn2').hide();
        $('#fRes').html();
        var cv1 = $('#cv1').val();
        var cv2 = $('#cv2').val();
        var cv3 = $('#cv3').val();
        var cv4 = $('#cv4').val();
        if (cv1 == '' || cv2 == '' || cv3 == '' || cv4 == '' ){
            $('#btn2').show();
            $('#fRes').html('<div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك ملئ جميع الحقول.</div>');
        }else{
            var allc = parseInt($('#countc').val());
            for (var i = 0; i <= allc-1; i++) {
                var pc = parseInt($('#productcount'+i).val());
                var pi = parseInt($('#product'+i).val());
                $.post(site_url+"/ajax.php", { action : 'InsertOrder' , productid : pi , count: pc , cv1:cv1 , cv2:cv2 , cv3:cv3 , cv4:cv4 } ,function(data){});
            }
            $('#fRes').html('<div class="alert alert-success alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button> تم إضافة الأوردر بنجاح ، سيتم التواصل معك قريباً.</div>');

            window.setTimeout(function() {
                $.post(site_url+"/ajax.php", { action : 'RemoveAllFromCart'} ,function(data){
                    CountCart();
                    $("#CartData").modal("hide");
                });
            },2000);
        }
    }
    function ChangeShip(ship){
        $('#product_ship_city').val($("#shipselct option:selected").text());
        var s = ($('#shipselct').val()*ship);
        $('#product_ship').val(s);
        $('#ship').html(s + ' ريال سعودي');
    }

    function Like(elm ,productid){
        
        if($(elm).hasClass('active')){
            $(elm).removeClass('active');
            $(elm).find('span').text($(elm).find('span').text() *1 - 1);
            $.post(site_url+"/ajax.php", { action : 'Like' , type : 'remove' , productid : productid} ,function(data){  });
        }else{
            $(elm).addClass('active');
            $(elm).find('span').text($(elm).find('span').text()*1 + 1);
            $.post(site_url+"/ajax.php", { action : 'Like' , type : 'add' , productid : productid} ,function(data){  });
        }
        
    }

    function ShowDalel(){
        Swal({
        title: 'دليل المقاسات',
        html: '<img src="'+site_url+'/img/size2.webp">',
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: false,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'إغلاق',
      });
    }
  
      function ShowDalel2(){
        Swal({
        title: 'الشحن',
         html: '<img src="'+site_url+'/img/shipping.webp">',
        type: 'warning',
        customClass: 'animated tada',
        showCancelButton: false,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'إغلاق',
      });
    }
  
  </script>
  
  
  

  
</div>
  
</body>
</html>


<script type="application/ld+json">
    {
      "@context" : "http://schema.org",
      "@type" : "Organization",
      "name" : "SHEBOX",
     "url" : "https://shebox.co/",
     "sameAs" : [
       "https://twitter.com/shebox",
       "https://www.instagram.com/shebox/"
       ],
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Saudi Arabia St",
        "addressRegion": "Jeddah",
        "postalCode": "22230",
        "addressCountry": "KSA"
      }
    }
    </script>


      





