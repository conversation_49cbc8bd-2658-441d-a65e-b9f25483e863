<?php

ob_start();
//----------------------------------------------------------------------------------
function Get_The_Posts($blogid){
  global $Site_URL;
  $p = getAllFrom('*' , 'blog' , 'WHERE id = "'.$blogid.'" ', '');
  if(count($p) > 0){
 

  return ' <a style="
 margin: 5px 5px;
    background: #ffffff;
    padding: 5px 10px;
    color: #b373a5;
    display: inline-block;
    font-size: 15px;
    font-weight: 600;
    box-shadow: 0px 1px 1px 0px #dfdfdf;
    display: inline-block;" href="'.$Site_URL.'/'.$p[0]['link'].'" title="'.$p[0]['title'].'">'.$p[0]['title'].'</a> ';
  }
}
//----------------------------------------------------------------------------------
function Get_ThePost_Content($blogid){
  global $Site_URL;
  $p = getAllFrom('*' , 'blog' , 'WHERE id = "'.$blogid.'" ', '');
  if(count($p) > 0){
    $phone = '<a href="tel:'.GetTableSet('DefPhoneNumber').'">'. GetTableSet('DefPhoneNumber').'</a>';
    $st = "";
    
    $cat = getAllFrom('*' , 'category' , 'WHERE id = "'.$p[0]['catid'].'" ', '');
    if(count($cat) > 0){
    }
   
    
    echo '
    <div class="col-md-6">
    <div class="products-content">
      <h1>'.$p[0]['title'].'</h1><br>
      
        <div class="col-md-12">

      '.$p[0]['descr'].' '.$phone.$st.'
        </div>
    </div>
    </div>
    
    <div class="col-md-6">
          <h3>أقراء المزيد</h3><br>

      ';
    $m = getAllFrom('*' , 'blog' , 'WHERE catid = "'.$p[0]['catid'].'" AND id != "'.$blogid.'" ', 'ORDER BY rand() LIMIT 4');
    if(count($m) > 0){
      echo '<br><hr><br>';
    for($i=0; $i<=count($m)-1; $i++){
      echo Get_The_Posts($m[$i]['id']);
    }
    }
     echo '
    </div>
    ';
    
  }
}
//----------------------------------------------------------------------------------
function GetProductLikes($productData){
  global $_SESSION;

  if(isset($_SESSION['UserLikes'])){
    $userLikes = $_SESSION['UserLikes'];
  }else{
    $userLikes = array();
  }

  $active = '';
  if(in_array($productData['id'] , $userLikes)){
    $active = 'active';
  }
 
  return '
  <div class="fb-like">
    <button class="'.$active.'" onclick="Like(this , '.$productData['id'].')"><i class="fa fa-heart"></i> <span>'.$productData['likes'].'</span></button>
  </div>';

}
//-----------------------------------------------------------------------------------
function GetProduct($productid , $hide = 0){
  global $Site_URL,$Site_Name;
  $users = array("يهبل بجد " , "رائعه حقا" , "شكرا لكم من كل قلبي" , "كتير يجنن" , "مشاء الله تبارك الله" , "ممتاز" , "شكرا لكم منتج رائع" , "كتير حلو" , "حلوه مره ");

  $res = '';
  $p = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$productid.'" ', '');
  if (count($p) > 0){
    $img = ''; $img2 = '';
    if (empty($p[0]['photo'])){
      $img = GetTableSet ('DefaultImage');
    }else{
      $x =explode('&&&', $p[0]['photo']);
      if (count($x) > 1){
        $igid = getAllFrom('*' , 'photos' , 'WHERE id = "'.$x[0].'" ', '');
        if (count($igid) > 0){
          $img = $igid[0]['link'];
        }else{
          $img = GetTableSet ('DefaultImage');  
        }
        $igids = getAllFrom('*' , 'photos' , 'WHERE id = "'.$x[1].'" ', '');
        if (count($igids) > 0){
          $img2 = $igids[0]['link'];
        }else{
          $img2 = $img; 
        }
      }else{
        $img = GetTableSet ('DefaultImage');
      }
    }
    $cid = getAllFrom('*' , 'category' , 'WHERE id = "'.$p[0]['catid'].'" ', '');
    if (count($cid) > 0){
      $cname = $cid[0]['name'];
      $catlink = '<a href="'.$Site_URL.'/category/'.$cid[0]['link'].'">'.$cname.'</a>';
    }else{
      $cname = 'غير معروف';
      $catlink = '<a href="#">'.$cname.'</a>';
    }

    if ($hide > 0 && $hide >= GetTableSet ('IndexProductsMob')){
      $cls = 'hideonmob';
    }else{
      $cls = '';
    }

   $active = '';
if (isset($_SESSION['UserLikes']) && in_array($p[0]['id'], $_SESSION['UserLikes'])) {
    $active = 'active';
}

$likesBtn = '
<div class="fb-like">
  <button class="'.$active.'" onclick="Like(this , '.$p[0]['id'].')">
    <i class="fa fa-heart"></i> <span>'.$p[0]['likes'].'</span>
  </button>
</div>';

$res = '<div class="item fixedw '.$cls.'">
    <div class="item-area">';
    
if($p[0]['paid'] != '' && $p[0]['paid'] > 0){
    $res .= ' <div class="sale-discount"><img src="/img/discount.webp"> <b>عرض لفترة محدودة<b></div>';
}

$res .= '
    <div class="product-image-area">
        <div class="loader-container">
            <div class="loader">
                <i class="ajax-loader medium animate-spin"></i>
            </div>
        </div>
        <a href="'.$Site_URL.'/'.$p[0]['link'].'" title="'.$p[0]['title'].'" class="product-image">
            <img class="defaultImage lazyOwl" src="'.$Site_URL.'/'.$img.'" width="300"  alt="'.$p[0]['title'].'"/>
        </a>
    </div>

    <div class="details-area">
        <div class="ratings"><div class="rating-box"><div class="rating" style="width:100%"></div></div><span class="amount"><a href="#"></a></span></div>
        <div class="price-box">
            <p class="special-price">
                <span class="price" id="product-price-9218">'.number_format($p[0]['price']).'  ر.س <b style=" font-size: 11px;color: #000;">بدلا من 00 '.number_format($p[0]['oldprice']).' </b></span>
            </p>
            <h2 class="product-name">
                <a href="'.$Site_URL.'/'.$p[0]['link'].'" title="'.$p[0]['title'].'">'.$p[0]['title'].'</a>
            </h2>
        </div>

        <a href="'.$Site_URL.'/'.$p[0]['link'].'" title="'.$p[0]['title'].'"><p class="hd">'.$p[0]['tags'].'</p></a>

        <div class="actions">
            '.$likesBtn.'
            <div class="clearer"></div>
        </div>
    </div>
</div>
</div>';


      $res .= '
      <script type="application/ld+json">
            {
            "@context": "https://schema.org/", 
            "@type": "Product", 
            "name": '.json_encode($p[0]['title']).',
            "image": '.json_encode($Site_URL.'/'.$img).',
            "description": '.json_encode(strip_tags($p[0]['descr'])).',
            "brand": {
                "@type": "Brand",
               "name" :  "'.$cname.'"
            },
            "sku": '.json_encode('EA-DER'.$p[0]['id'].'SA').',
            "mpn": '.json_encode('#'.$p[0]['id']).',
            "offers": {
                "@type": "Offer",
                "url": '.json_encode($Site_URL .'/' .$p[0]['link'] ).',
                "priceCurrency": "SAR",
                "price": '.json_encode($p[0]['price']).',
                "priceValidUntil": "'.date("Y", strtotime('+3 year')).'-12-31",
                "availability": "https://schema.org/InStock",
                "itemCondition": "https://schema.org/NewCondition"
            },
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "10",
                "bestRating": "10",
                "worstRating": "1",
                "ratingCount": '.json_encode($p[0]['views']).'
            },
            "review": [
                ';
                $reviews = '';
                for ($i=0; $i <= count($users)-1 ; $i++) { 
                    $reviews = $reviews . '
                    {
                        "@type": "Review",
                        "name": '.json_encode('user_'.($i+1)).',
                        "reviewBody": '.json_encode($users[$i]).',
                        "reviewRating": {
                          "@type": "Rating",
                          "ratingValue": "10",
                          "bestRating": "10",
                          "worstRating": "1"
                        },
                        "datePublished": "'.date("Y-m-d" ,$p[0]['datee'] ).'",
                        "author": {"@type": "Person", "name": '.json_encode($Site_Name).'},
                        "publisher": {"@type": "Organization", "name": '.json_encode($Site_Name).' }
                    },';
                }
                $reviews = substr_replace($reviews ,"", -1);
                $res .=   $reviews .'
              ]
            }
        </script>
      ';                          
  }
  return $res;
}

//----------------------------------------------------------------------------------
function GetCountOffers($productid){
  $ch = getAllFrom('*' , 'products' , 'WHERE titleid = "'.$productid.'" AND status = 1', '');
  if (count($ch) > 0){
    if (count($ch) == 1){
      return 'عرض واحد';
    }elseif (count($ch) == 2){
      return 'عرضين';
    }elseif(count($ch) > 2 && count($ch) < 11){
      return count($ch).' عروض';
    }else{
      return count($ch).' عرض';
    }
  }else{
    return 'لا يوجد عروض';
  }
  
}
//--------------------------------------------------------
function Get_Category_Page($catid , $parts){
  global $Site_URL;
  $cid = getAllFrom('*' , 'category' , 'WHERE id = "'.$catid.'" AND status = 1', '');
  if (count($cid) > 0){
    $cname = $cid[0]['name'];
    $cname2 = $cid[0]['title'];
    $parent = $cid[0]['parent'];
    $descrr = $cid[0]['descr'];
        $contentz = $cid[0]['content'];
    $hidecontent = $cid[0]['hidecontent'];
    $imgurl = $Site_URL.'/'.$cid[0]['photo'];
  }else{
    $cname = 'غير معروف';
    $cname2 = 'غير معروف';
    $parent = 0;
    $descrr = '';
    $hidecontent = "";
        $contentz = "";
    $imgurl = "";
  }
  GetBreadcrumb(array($cname));
  
  
  


  


/*
  $ADSX = getAllFrom('*','ads' ,'WHERE type = "c" AND status = 1','ORDER BY orders DESC ,id DESC');
    if (count($ADSX)> 0){
       echo '<div class="col-md-12">';
        for ($i=0; $i <= count($ADSX)-1 ; $i++) { 
            echo $ADSX[$i]['code'];
        }
        echo '</div>';
    } 
*/
    $allproducts =array();
    $q = "  ";
    $sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
    if (count($sub) > 0){
      $q = " OR ";
      for ($i=0; $i <= count($sub)-1 ; $i++) { 
        if ($i == count($sub)-1){
          $q = $q .' catid = '.$sub[$i]['id'];
        }else{
          $q = $q .' catid = '.$sub[$i]['id'] . ' OR';  
        }
      }
    }
    //$products = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$catid.'" '.$q.' ', '  ORDER BY id DESC');
 
 

  echo '<div class="container-fluid products-content">';

  
  echo '<div class="col-md-12 padding0"><h1 class="filter-title"><span class="cat-title"><strong>'.$cname2.'</strong></span></h1>';

  if (count($sub) > 0){
    echo '<div class="subact"><ul>';
    for ($c=0; $c <= count($sub)-1 ; $c++) { 
      echo '<li><i class="fa fa-angle-double-left" aria-hidden="true"></i> <a href="'.$Site_URL.'/category/'.$sub[$c]['link'].'">'.$sub[$c]['name'].'</a></li>';
    }
    echo '</ul></div>';
  }
  //echo '<div class="newdiv">';
  for ($i=0; $i <= count($sub)-1 ; $i++) { 
    if($sub[$i]['showindex'] == 3){


        echo '<div class="col-md-2 col-sm-4 col-xs-4 padding5"><div class="index-box">';
            if (!empty($sub[$i]['photo'])){
                echo '<a href="'.$Site_URL.'/category/'.$sub[$i]['link'].'"><img alt="'.$sub[$i]['name'].'" src="'.$Site_URL.'/'. $sub[$i]['photo'].'" title="'.$sub[$i]['name'].'" /></a>';
                
            }else{
                echo '<a href="'.$Site_URL.'/category/'.$sub[$i]['link'].'"><img alt="'.$sub[$i]['name'].'" src="'.$Site_URL.'/'. GetTableSet ('DefaultImage').'" title="'.$sub[$i]['name'].'" /></a>';
            }
                echo '<p><a href="'.$Site_URL.'/category/'.$sub[$i]['link'].'">'.$sub[$i]['name'].'</a></p> </div></div>';
                
        
       
    }
  }

    echo '</div>';

  
    
    echo '<div class="col-md-9" id="moreproducts"><br>';
    if ($parent == 0){
        $sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$catid.'" AND status = 1', '');
    if (count($sub) > 0){
      $q = " OR ";
      for ($i=0; $i <= count($sub)-1 ; $i++) { 
        if ($i == count($sub)-1){
          $q = $q .' catid = '.$sub[$i]['id'];
        }else{
          $q = $q .' catid = '.$sub[$i]['id'] . ' OR';  
        }
      }
      $allproducts = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$catid.'" '.$q.' AND type = 0', '  ORDER BY price ASC,id DESC');
      $products = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$catid.'" '.$q.' AND type = 0', '  ORDER BY price ASC,id DESC LIMIT 10');
      
      if (count($products) > 0){
          $lastpid = 0;
          for($i=0; $i<= count($products)-1; $i++){
              GetProduct($products[$i]['id']);
              $lastpid = $products[$i]['id'];
          }
          if (count($allproducts) > count($products) ){
              echo '<div id="willdel" class="col-md-12"><br><div class="col-md-4 col-md-offset-4" id="btn-more"><a onclick="GetMoreProduct('.$lastpid.' , '.$catid.')" class="btn btn-md btn-danger btn-block">إظهار المزيد من المنتجات</a></div></div>';
          }
      }else{
          echo  '<br>'.Show_Alert('danger' , 'لا يوجد اى منتجات فى الوقت الحالي.');
      }

      
      
    }else{
        echo  '<br>'.Show_Alert('danger' , 'لا يوجد اى منتجات فى الوقت الحالي.');
    }
    
    }else{
        $allproducts = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$catid.'" AND type = 0', '  ORDER BY price ASC,id DESC');
        $products = getAllFrom('*' , 'products_title' , 'WHERE catid ="'.$catid.'" AND type = 0 ', '  ORDER BY price ASC,id DESC');
        $sub = getAllFrom('*' , 'category' , 'WHERE id = "'.$parent.'" AND status = 1', '');
        if (count($products) > 0){
    //echo '<center>';
    for ($i=0; $i <= count($products)-1 ; $i++) { 
      GetProduct($products[$i]['id']);
    }
    //echo '</center>';
      }else{
    
        echo  '<br>'.Show_Alert('danger' , 'لا يوجد اى منتجات فى الوقت الحالي.');
        }
        if(count($sub) > 0){
        echo '<div class="col-md-12"><p>شاهد جميع منتجات <a href="'.$Site_URL.'/category/'.$sub[0]['link'].'">'.$sub[0]['name'].'</a></p></div>';
        }
    }
    echo '</div>';



  if ($parent >= 0){
        if($contentz == ""){
            $contentz = $descrr;
        }
    echo '<div class="col-md-9 catdescr"><div class="descat">'.$contentz.'</div></div>';
  }
         
         
         
         
      if (count($allproducts) > 0){
          echo '<div class="col-md-12"><div class="pricetable">';
          echo '<div class="tabletitles"><h2><i class="fa fa-line-chart" aria-hidden="true"></i> قائمة أسعار '.$cname. ' '.date("Y").'<i class="fa fa-line-chart" aria-hidden="true"></i></h2><h4> آخر تحديث '.date("Y/m/d" , strtotime(' -2 day')).'</h4></div>';
          echo '
          <table class="table table-bordered table-hover table-striped">
                  <thead style="display:none">
                    <tr>
                      <th>أفضل أسعار '.$cname.'</th>
                      <th>القسم</th>
                      <th>السعر</th>
                    </tr>
                  </thead>
                  <tbody>';
                  for($i=0; $i <= count($allproducts)-1 ; $i++){
                      $cccc = getAllFrom('*' , 'category' , 'WHERE id = "'.$allproducts[$i]['catid'].'" AND status = 1', '');
                      if (count($cccc) > 0){
                        $ccname = $cccc[0]['name'];
                      }else{
                        $ccname = 'غير معروف';
                      }
                      
                      echo '<tr>
                              <td scope="row"><a href="'.$Site_URL.'/'.$allproducts[$i]['link'].'">سعر '.$allproducts[$i]['title'].' '.date("Y").'</a></th>
                              <td><a href="'.$Site_URL.'/'.$allproducts[$i]['link'].'">'.$ccname.'</a></td>
                              <td><a href="'.$Site_URL.'/'.$allproducts[$i]['link'].'"><i class="fa fa-tag" aria-hidden="true"></i> '.number_format($allproducts[$i]['price']).' جنيه </a> </td>
                            </tr>';
                      
                  }
                    
                    
                echo '</tbody></table></div></div>';
      }
    /*
  if (count($products) > 0){
    //echo '<center>';
    for ($i=0; $i <= count($products)-1 ; $i++) { 
      GetProduct($products[$i]['id']);
    }
    //echo '</center>';
  }else{

    echo  '<br>'.Show_Alert('danger' , 'لا يوجد اى منتجات فى الوقت الحالي.');
  }
    */
    
  echo '</div>';
  echo '</div>';
  
    if ($hidecontent != ""){
    echo '<div class="col-md-12 catdescr" style="display:none;">'.$hidecontent.'</div>';
  } 

if($parent == 0){
  $blogs = getAllFrom('*' , 'blog' , 'WHERE catid ="'.$catid.'" ', '  ORDER BY id DESC LIMIT 20');
  if(count($blogs) > 0){
    echo '<div class="col-md-12">';
    for($i=0; $i<= count($blogs)-1; $i++){
      echo Get_The_Posts($blogs[$i]['id']);
    }
    echo '</div>';
  }
  
  
  
  
$actual_link = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
for($i=1; $i<=3; $i++){
$shma = ""; $desc_shma = "";
if ($i==1){$shma = "".$cname." 1.5 حصان";}
if ($i==2){$shma = "".$cname." 2.25 حصان";}
if ($i==3){$shma = "".$cname." 3 حصان";}
$desc_shma =  'تعرف على اسعار ومميزات وعيوب '.$cname;
echo '
    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "SaleEvent",
        "name": '.json_encode($shma).',
        "description":'.json_encode($desc_shma).',
        "image": '.json_encode($imgurl).',
        "url": '.json_encode($actual_link).',
        "eventStatus": "https://schema.org/EventScheduled",
        "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
        "startDate": "'.date("Y").'-01-01",
        "endDate": "'.date("Y", strtotime('+3 year')).'-12-31",
        "offers": {
            "@type": "Offer",
            "url": '.json_encode($actual_link).',
            "price": "100",
            "priceCurrency": "SAR",
            "validFrom" : "'.date("Y", strtotime('+3 year')).'-12-31",
            "availability": "http://schema.org/InStock"
        },
        "performer": {
            "@type": "Person",
            "name": '.json_encode(''.$cname).'
        },
        "organizer": {
            "@type": "Person",
            "name": '.json_encode(''.$cname).',
            "url": '.json_encode($actual_link).'
        },
        "location": {
            "@type": "Place",
            "name": '.json_encode(''.$cname).',
            "url":  '.json_encode($actual_link).',
            "address": '.json_encode(''.$cname).'
        }
    }
    </script>
';
}
}
  
}
//----------------------------------------------------------------------------------
function GetBreadcrumb($parts){
  global $Site_URL;
  //echo '<div class="top-container"><div class="breadcrumb"><div class="container"><div class="row"><div class="mb-breadcrumbs"><div class="breadcrumbs"><ul><li style="display: inline-block;" typeof="v:Breadcrumb"><a href="'.$Site_URL.'">الرئيسية</a></li>';

  echo '<ol class="breadcrumb"><li style="display: inline-block;" typeof="v:Breadcrumb"><a href="'.$Site_URL.'">الرئيسية</a></li>';
  
  for ($i=0; $i <= count($parts)-1 ; $i++) { 
    $tit = str_replace('-', ' ', urldecode($parts[$i]));
    if ($tit != 'category' && $tit != 'seller'){
      if ($i == count($parts)-1){
        echo '<span>&nbsp;/&nbsp;&nbsp;</span><li style="display: inline-block;" typeof="v:Breadcrumb">'.$tit.'</li>';
      }else{
        echo '<span>&nbsp;/&nbsp;&nbsp;</span><li style="display: inline-block;" typeof="v:Breadcrumb" >'.$tit.'</li>';
      }
    }
  }
  echo ' </ol>';
}
//--------------------------------------------------------
function Get_company_Page($userid , $parts){
  global $Site_URL;
  $cid = getAllFrom('*' , 'users' , 'WHERE id = "'.$userid.'"', '');
  if (count($cid) > 0){
    $cname = $cid[0]['cname'];
    $userid = $cid[0]['id'];
  }else{
    $cname = 'غير معروف';
    $userid = 0;
  }
  GetBreadcrumb(array($cname));

  


  echo '<div class="main-container col1-layout"><div class="main container"><div class="col-main"><div id="loading-mask"><div class ="background-overlay"></div><p id="loading_mask_loader" class="loader"><i class="ajax-loader large animate-spin"></i></p></div><div class="std">';



  $ADSX = getAllFrom('*','ads' ,'WHERE type = "c" AND status = 1','ORDER BY orders DESC ,id DESC');
    if (count($ADSX)> 0){
       echo '<div class="row home-top-banner"><div class="col-md-12">';
        for ($i=0; $i <= count($ADSX)-1 ; $i++) { 
            echo $ADSX[$i]['code'];
        }
        echo '</div></div>';
    } 

  $products = getAllFrom('*' , 'products' , 'WHERE userid ="'.$userid.'"', ' ORDER BY id DESC');  
  



  echo '<div class="row product-slider"><h2 class="filter-title"><span class="content" style="color: #000000;font-size:17px;"><strong>جميع عروض '.$cname.'</strong></span></h2><br><div class="col-md-12"><div id="products_on_sale"><div class="filter-products"><div class="products owl-carousel">';

  
  if (count($products) > 0){
    //echo '<center>';
    for ($i=0; $i <= count($products)-1 ; $i++) { 
      GetProduct($products[$i]['titleid']);
    }
    //echo '</center>';
  }else{

    echo  '<br>'.Show_Alert('info' , 'لا يوجد اى منتجات فى الوقت الحالي.');
  }

  echo ' </div></div></div><div><h2 class="filter-title"><span class="content">&nbsp;</span></h2></div></div></div>';
  
}
//----------------------------------------------------------------------------------
function Get_Product_Content($productid){
  global $Site_URL; 
  global $db;
  $product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$productid.'" ', '');
  if (count($product) > 0){  

    $stmt = $db->prepare("UPDATE  products_title SET  views = :edt1  WHERE  id = :edt5 ");  
        $stmt->execute(array('edt1' => ($product[0]['views']+1), 'edt5' => $product[0]['id'] ));

    $photos = array();
    $def = GetTableSet ('DefaultImage');
    if (empty($product[0]['photo'])){
      $photos = array($def);
    }else{
      $p = explode('&&&', $product[0]['photo']);
      if(count($p) >0){
        for ($i=0; $i <= count($p)-1 ; $i++) { 
          if ($p[$i] != ""){
            $l = getAllFrom('*' , 'photos' , 'WHERE id ="'.$p[$i].'" ', '');
            if (count($l) >0){
              array_push($photos, $l[0]['link']);
            }
          }
        }
      }
    }

    $catname = '';
    $canm = getAllFrom('*' , 'category' , 'WHERE id ="'.$product[0]['catid'].'" ', '');
    if (count($canm) > 0){
      $catname = '<a href="'.$Site_URL.'/category/'.$canm[0]['link'].'">'.$canm[0]['name'].'</a>';
    }else{
      $catname = 'غير معروف';
    }

    $seller = $product[0]['seller'] != "" ? $product[0]['seller'] :  ('iBeex');

    $ship = getAllFrom('*' , 'ship' , '', '');
    echo '<section class="products-content">';
    GetBreadcrumb(array($product[0]['title']));
    //-----------------------------
    //---
    echo '<div class="container">';
    echo '<div class="col-md-4">';//photo section
    
    echo'</span></h2>';
    echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
      if (count($photos) > 0){
        for ($i=0; $i <=count($photos)-1 ; $i++) { 
          echo '<li><a href="'.$Site_URL.'/'.$photos[$i].'"><img src="'.$Site_URL.'/'.$photos[$i].'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
        }
      }else{
        echo '<li><a href="'.$Site_URL.'/'.$def.'"><img src="'.$Site_URL.'/'.$def.'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
        
      }
    echo '</ul></article></div>';
    echo '</div>';
    //---
    
    //---
    echo '<div class="col-md-8">
    
    <h1>'.$product[0]['title'].'</h1>
    <li class="catg">قسم المنتج : '.$catname.' <span class="sprr"></span></li> 
    <div class="product-info">
    <div class="product-info-wrp">
        <span class="label"><img src="img/seller-p.png"> <b>البائع<b>:</span>
        <span class="value">'.$seller.'</span>
    </div>
    <div class="product-info-wrp">
        <span class="label"><img src="img/ready-stock.png"> <b>المخزون</b></span>
        <span class="value"> متوفر '.number_format($product[0]['Quantity']).'</span>
    </div>
    
</div>

 <h3> 
    '.number_format($product[0]['price']).'<p class="pound">ريال</p></h3> <p class="oldprice-p"><b>السعر قبل الخصم : '.number_format($product[0]['oldprice']).'</br></p></p>

    <p class="offer-p"><b>مصاريف الشحن :  <strong id="ship">'.GetTableSet('Ship_Def').' </strong>      <select style="height: 40px;" id="shipselct" onchange="ChangeShip('.$product[0]['ship'].')" class="form-control">
    <option value="0000">إختر المحافظه</option>
    ';
     for ($i=0; $i <= count($ship)-1 ; $i++) { 
        echo '<option value="'.$ship[$i]['amount'].'">'.$ship[$i]['city'].'</option>';
     }
    echo '
 </select></br></p> 
    
    <div class="box-offers">
      <div class="col-md-12 pad0">
         
      <div class="col-md-8 text-center pad0">
        ';
          if($product[0]['Quantity'] != '' && $product[0]['Quantity'] > 0){
            echo '<a onclick="AddToCart('.$product[0]['id'].')" style="margin-top: 10px;" class="btn btn-block btn-danger btn-md"><i class="icon-cart"></i><span>&nbsp; شراء الان </span></a>';
          }else{
            echo '<a  style="margin-top: 10px;" class="btn btn-block btn-danger btn-md"><i class="icon-cart"></i><span>&nbsp; غير متوفر حاليا </span></a>';
          }

        echo '
      </div> 
      <div class="col-md-4 col-sm-12 col-xs-4 padding5"> 
      <div class="chat-p"> <img src="img/chat-p.png"> <b>عندك أستفسار ؟ </b>
     </div>
     </div> 
     <div class="col-md-12 pad0">

      <div class="col-md-4 col-sm-12 col-xs-4 padding5"> 
      <div class="icons-p"> <img src="img/payment-p.png"> <b>الدفع عند الاستلام </b>
     </div>
     </div> 
     <div class="col-md-4 col-sm-12 col-xs-4 padding5"> 

     ';
          if($product[0]['delivery'] != '' && $product[0]['delivery'] > 0){
            echo ' <div class="icons-p"> <img src="img/express-delivery.png"> <b>شحن خلال <b>'.number_format($product[0]['delivery']).'أيام </br>';
          }else{
           
            echo ' <div class="icons-p"> <img src="img/express-delivery.png"> <b>شحن خلال 3 أيام <b>';
          }

        echo '
    </div>
    </div> 

      </div> 
      </div> 
      </div>';
    
    echo '</div>';


    echo '<div class="col-md-12">';//desc section
    echo '<h3>مواصفات المنتج</h3><hr>';
    echo $product[0]['descr'];
    
    
    $information = array();
    if ($product[0]['infoid'] != ""){
            $inf = explode(',' , $product[0]['infoid']);
            if (count($inf) > 0){
                for ($u=0; $u <= count($inf)-1 ; $u++) { 
                    if ($inf[$u] != ""){
                        $chg = getAllFrom('*' , 'info' , 'WHERE id = "'.$inf[$u].'"', '');
                        if (count($chg) > 0){
                            array_push($information , array($chg[0]['name'] , $chg[0]['descr']) );
                        }
                    }
                }
            }
    }     
        
        if (count($information) > 0){
            for ($z=0; $z <= count($information)-1 ; $z++) { 
                echo '<div class="col-md-12 padding0">
                        <div class="acou">
                            <a class="infobtn" data-toggle="collapse" data-target="#info'.$z.'">'.$information[$z][0].' <i class="fa fa-angle-double-down" aria-hidden="true"></i></a>
                            <div id="info'.$z.'" class="collapse infodata">'.$information[$z][1].'</div>
                        </div>
                </div>';
            }
        }
        

    echo '</div>';


    //-----------------------------
    $tit = explode(' ', $product[0]['title']);
    if (count($tit) > 0){
      $q = ' WHERE ';
      $limit = 5;
      if ($limit > count($tit)) {$limit = count($tit);}
      for ($i=0; $i <= $limit-1 ; $i++) { 
        if ($i == $limit-1){
          $q = $q .' title LIKE "%'.$tit[$i].'%" ';
        }else{
          $q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
        }
      }
      
      $more = getAllFrom('*' , 'products_title' , $q , 'AND status = 1 AND id != "'.$product[0]['id'].'" AND type = 0 ORDER BY rand() LIMIT '.GetTableSet ('ProductMostViewedComp'));
      if (count($more) > 0){
        echo '<div class="col-md-12"><hr><h2 class="paid-title">المستخدمين شاهدوا أيضاً</h2>';//more product
        for ($i=0; $i <= count($more)-1 ; $i++) { 
          GetProduct($more[$i]['id']);
        }
        echo '</div>';
        echo '</div>';

      }
    }
    //-----------------------------
    echo '</section>';
  }else{
    header('Location: category.php'); exit();
  }
  
}
//--------------------------------------------------------
function Get_Post_Content($productid){
    echo '<style>.tags{padding: 0;margin: 0;} .tags li{list-style: none;display: inline-block;margin: 3px;background: #f3f3f3;padding: 3px 10px;border-radius: 5px;border: 1px solid #ccc;color: #000; }</style>';
    global $Site_URL; 
  global $db;
  $product  = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$productid.'" ', '');
  if (count($product) > 0){  
      $stmt = $db->prepare("UPDATE  products_title SET  views = :edt1  WHERE  id = :edt5 ");  
        $stmt->execute(array('edt1' => ($product[0]['views']+1), 'edt5' => $product[0]['id'] ));
      
      $photos = array();
    $def = GetTableSet ('DefaultImage');
    if (empty($product[0]['photo'])){
      $photos = array($def);
    }else{
      $p = explode('&&&', $product[0]['photo']);
      if(count($p) >0){
        for ($i=0; $i <= count($p)-1 ; $i++) { 
          if ($p[$i] != ""){
            $l = getAllFrom('*' , 'photos' , 'WHERE id ="'.$p[$i].'" ', '');
            if (count($l) >0){
              array_push($photos, $l[0]['link']);
            }
          }
        }
      }
    }

    $catname = '';
    $canm = getAllFrom('*' , 'category' , 'WHERE id ="'.$product[0]['catid'].'" ', '');
    if (count($canm) > 0){
      $catname = '<a href="'.$Site_URL.'/category/'.$canm[0]['link'].'">'.$canm[0]['name'].'</a>';
    }else{
      $catname = 'غير معروف';
    }
    
    echo '<section class="products-content">';
    GetBreadcrumb(array($product[0]['title']));
    
    echo '<div class="container">';
    //-----------------------------
    //---
    echo '<div class="col-md-12">';
    echo '<h1>'.$product[0]['title'].'</h1>';
    echo '<h2 class="catg">القسم : '.$catname.' <span class="sprr">';
    echo'</span></h2>';
    echo '</div>';
    
    echo '<div class="col-md-6">';//photo section
    
    echo '<div class="pslide"><div id="slideshow" class="col-lg-12 nopadding"></div><article class="col-lg-12 pading3 text-center"><ul id="slideshow_thumbs" class="desoslide-thumbs-horizontal list-inline ">';
      if (count($photos) > 0){
        for ($i=0; $i <=count($photos)-1 ; $i++) { 
          echo '<li><a href="'.$Site_URL.'/'.$photos[$i].'"><img src="'.$Site_URL.'/'.$photos[$i].'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
        }
      }else{
        echo '<li><a href="'.$Site_URL.'/'.$def.'"><img src="'.$Site_URL.'/'.$def.'" class="img-responsive" alt="'.$product[0]['title'].'" data-desoslide-caption-title="'.$product[0]['title'].'"></a></li>';
        
      }
    echo '</ul></article></div>';
    echo '</div>';
    //---
    echo '<div class="col-md-6" >';//desc section
    echo $product[0]['descr'];
    echo '</div>';
    
    if (!empty($product[0]['tags'])){
    echo '<div class="col-md-12" style="margin-top:30px;"><h3>الكلمات الدلالية</h3><ul class="tags">';
        $tags = explode(',', $product[0]['tags']);
        for($i=0; $i<= count($tags)-1; $i++){
            if ($tags[$i] != ""){
                echo '<li><a href="">'.$tags[$i].'</a></li>';
            }
        }
    
    echo '</ul></div>';
    }
    //-----------------------------
    /*
    $tit = explode(' ', $product[0]['title']);
    if (count($tit) > 0){
      $q = ' WHERE ';
      $limit = 5;
      if ($limit > count($tit)) {$limit = count($tit);}
      for ($i=0; $i <= $limit-1 ; $i++) { 
        if ($i == $limit-1){
          $q = $q .' title LIKE "%'.$tit[$i].'%" ';
        }else{
          $q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
        }
      }
      
      $more = getAllFrom('*' , 'products_title' , $q , 'AND status = 1 AND id != "'.$product[0]['id'].'" AND type = 0 ORDER BY rand() LIMIT '.GetTableSet ('ProductMostViewedComp'));
      if (count($more) > 0){
        echo '<div class="col-md-12"><hr><h2 class="paid-title">المستخدمين شاهدوا أيضاً</h2>';//more product
        for ($i=0; $i <= count($more)-1 ; $i++) { 
          GetProduct($more[$i]['id']);
        }
        echo '</div>';
      }
    }
    */
    //-----------------------------
    echo '</div></section>';
    
      
      
  }else{
    header('Location: category.php'); exit();
  }   
}
//--------------------------------------------------------
function GetProductMostViewed($master_catid , $product_id){
  global $Site_URL; 
  $sub = getAllFrom('*' , 'category' , 'WHERE parent = "'.$master_catid.'" AND status = 1', '');
    if (count($sub) > 0){
      $q = " OR ";
      for ($i=0; $i <= count($sub)-1 ; $i++) { 
        if ($i == count($sub)-1){
          $q = $q .' catid = '.$sub[$i]['id'];
        }else{
          $q = $q .' catid = '.$sub[$i]['id'] . ' OR';  
        }
      }
    }
  $products = getAllFrom('*' , 'products_title' , 'WHERE (catid ="'.$master_catid.'" '.$q.' ) AND id != "'.$product_id.'" AND type = 0', ' ORDER BY views DESC , rand() LIMIT '.GetTableSet ('ProductMostViewedComp'));

  if (count($products) > 0){
    echo '<div class="block block-related-product aw-arp-block aw-arp-block-11"><br><div class="block-title"><strong><span>الأكثر  مشاهدة</span></strong></div><div class="block-content aw-arp-block-content"><div class="row"><ul>';
    for ($i=0; $i <= count($products)-1 ; $i++) { 
      $img = '';
      $cls = '';
      if ($i >= GetTableSet ('ProductMostViewedMob')){
        $cls = 'hideonmob';
      }
      if (empty($products[$i]['photo'])){
        $img = GetTableSet ('DefaultImage');
      }else{
        $x =explode('&&&', $products[$i]['photo']);
        if (count($x) > 1){
          $igid = getAllFrom('*' , 'photos' , 'WHERE id = "'.$x[0].'" ', '');
          if (count($igid) > 0){
            $img = $igid[0]['link'];
          }else{
            $img = GetTableSet ('DefaultImage');  
          }
        }else{
          $img = GetTableSet ('DefaultImage');
        }
      }
      echo '<li style="list-style: none;" class="col-md-2 col-sm-3 col-xs-6 '.$cls.'"><div class="upsell-item"><a href="'.$Site_URL.'/'.$products[$i]['link'].'"><img src="'.$Site_URL.'/'.$img.'" alt="'.$products[$i]['title'].'"></a><div class="upsell-details"><h2 class="product-name"><a href="'.$Site_URL.'/'.$products[$i]['link'].'">'.$products[$i]['title'].'</a><p>'.mb_substr( strip_tags( $products[$i]['descr'] ), 0, 40,"utf-8" ).' ...</p></h2><div class="price-box"><span class="regular-price" id="product-price-6497-related"><span class="price">'.GetCountOffers($products[$i]['id']).'</span></span></div></div></div></li>';
    }
    echo '</ul></div></div></div>';
  }
  }
//--------------------------------------------------------
function GetPagination($current , $total){
  $range = pageRange($current , $total);


  $res = '<nav aria-label="Page navigation"><ul class="pagination pagination-lg">';
    if($current > 1){
      $res .= '<li><a onclick="setPage('.($current-1).')" aria-label="السابق"><span aria-hidden="true">&laquo;</span></a></li>';
    }else{
      $res .= '<li class="disabled"><a aria-label="السابق"><span aria-hidden="true">&laquo;</span></a></li>';
    }
    for ($i=$range['start']; $i <= $range['end'] ; $i++) { 
      if($current == $i){
          $res .= '<li class="active"><a>'.$i.'</a></li>';
      }else{
          $res .= '<li><a onclick="setPage('.$i.')">'.$i.'</a></li>';
      }
    }
    if($current >= $total){
      $res .= '<li class="disabled"><a aria-label="التالي"><span aria-hidden="true">&raquo;</span></a></li>';
    }else{
      $res .= '<li><a onclick="setPage('.($current+1).')" aria-label="التالي"><span aria-hidden="true">&raquo;</span></a></li>';
    }
    $res .= '</ul></nav>';
    return $res;
}
//--------------------------------------------------------------------------------------------------------
function pageRange($page,$pageCount){
  $start = $page-2;
  $end = $page+2;
  if($end>$pageCount){
      $start-=($end-$pageCount);
      $end=$pageCount;
  }
  if($start<=0){
      $end+=(($start-1)*(-1));
      $start=1;
  }
  $end = $end>$pageCount?$pageCount:$end;
  return array(
      'start' => $start ,
      'end' => $end 
  );
}
//--------------------------------------------------------------------------------------------------------
function GetMyOrderStatus($st){
    if($st == 1){
      return 'تم التوصيل';
    }else if($st == 2){
      return 'ملغي';
    }else{
      return 'فى الإنتظار';
    }
}