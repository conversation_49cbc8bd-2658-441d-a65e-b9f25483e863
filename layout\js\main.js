$(document).ready(function() { 
	var width =  $(window).width() ;
 	//open dropdown on hover
 	$(".dropdown-hover").hover(function(){
	    $(this).addClass("open");
	    $(this).addClass("active");
	    }, function(){
	    $(this).removeClass("open");
	    $(this).removeClass("active");
	});
	//rate
	var dothis = 0;
	if (dothis == 1){
	$(".cr").hover(function(){
	    $(this).removeClass("fa-star-o");
	    $(this).addClass("fa-star");
	    $(this).addClass("arate");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star-o");
	    pr.addClass("fa-star");
	    pr.addClass("arate");
	    }, function(){
	    $(this).removeClass("fa-star");
	    $(this).removeClass("arate");
	    $(this).addClass("fa-star-o");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star");
	    pr.removeClass("arate");
	    pr.addClass("fa-star-o");
	});
	}
	$(".cr").on("click", function(){
		dothis = 0 ;
	    $(this).removeClass("fa-star-o");
	    $(this).addClass("fa-star");
	    $(this).addClass("arate");
	    var pr = $(this).closest('i').prevAll('i');
	    pr.removeClass("fa-star-o");
	    pr.addClass("fa-star");
	    pr.addClass("arate");
	});
	//open submenu on hover
 	$(".dropdown-submenu").hover(function(){
	    $(this).addClass("open");
	    $(this).addClass("active");
	    }, function(){
	    $(this).removeClass("open");
	    $(this).removeClass("active");
	});
 	//submenu
 	$('.dropdown-submenu a.test').on("click", function(e){
		 $(this).next('ul').toggle();
		 e.stopPropagation();
		 e.preventDefault();
	});
 	//scroll to Top
	var offset = 300,
	offset_opacity = 1200,
	scroll_top_duration = 700,
	$back_to_top = $('.cd-top');

	$(window).scroll(function(){
		( $(this).scrollTop() > offset ) ? $back_to_top.addClass('cd-is-visible') : $back_to_top.removeClass('cd-is-visible cd-fade-out');
		if( $(this).scrollTop() > offset_opacity ) { 
			$back_to_top.addClass('cd-fade-out');
		}
	});
	$back_to_top.on('click', function(event){
		event.preventDefault();
		$('body,html').animate({
			scrollTop: 0 ,
		 	}, scroll_top_duration
	);
	});
/*
	//slider
	$(".Modern-Slider").slick({
      autoplay:true,
      autoplaySpeed:SliderMoving*1000,
      speed:1000,
      centerMode:true,
      rtl: true,
      slidesToShow:1,
      slidesToScroll:1,
      pauseOnHover:false,
      dots:false,
      pauseOnDotsHover:false,
      cssEase:'ease',
      fade:true,
      arrows:false,
      draggable:false,
      prevArrow:'<a class="PrevArrow"><i class="fa fa-chevron-circle-left" aria-hidden="true"></i></a>',
      nextArrow:'<a class="NextArrow"><i class="fa fa-chevron-circle-right" aria-hidden="true"></i></a>', 
    });

	//logos
	var slidesToShow = 6;
    if (width < 767){slidesToShow = 2;}
    $('.autoplay').slick({
	  slidesToShow: 6,
	  slidesToScroll: 1,
	  responsive: [
        {
            breakpoint: 980, // tablet breakpoint
            settings: {
                slidesToShow: 4,
                slidesToScroll: 1
            }
        },
        {
            breakpoint: 480, // mobile breakpoint
            settings: {
                slidesToShow: 2,
                slidesToScroll: 1
            }
        }
    ],
	  rtl: true,
	  prevArrow:'<a class="PrevArrow"><i class="fa fa-chevron-circle-left" aria-hidden="true"></i></a>',
      nextArrow:'<a class="NextArrow"><i class="fa fa-chevron-circle-right" aria-hidden="true"></i></a>', 
	  autoplay: true,
	  arrows:false,
	  autoplaySpeed: LogosMoving*1000,
	});
*/
	$('#slideshow').desoSlide({
	    thumbs: $('#slideshow_thumbs li > a'),
	    overlay: 'hover',
	    controls: {
	        show: false,
	        keys: true
	    }
	});

	$(function () {
	  $('[data-toggle="tooltip"]').tooltip()
	})
});

//--------------------------------------------
function SelectCategory(){
	var var1 = $('#var1').val();
	$('#res').html('');
	$('#var2').html('<option>إختر القسم الفرعي</option>');
	if (var1 == '0'){
		$('#var2').addClass('dis');
		//$('#var3').addClass('dis');
		//$('#var4').addClass('dis');
	}else{
		$.post(site_url+"/ajax.php", { action : 'SelectCategory' , id: var1 } , function(data){ 
			if (data != ""){
				$('#var2').html('');
				$('#var2').append(data);
				$('#var2').removeClass('dis');
				//$('#var3').removeClass('dis');
				CheckIfReady();
			}   
    	});
	}
	CheckIfReady();
}
//--------------------------------------------
function SelectCity(){
	var var3 = $('#var3').val();
	$('#res').html('');
	$('#var4').html('<option>إختر المنطقة</option>');
	if (var3 == '0'){
		$('#var4').addClass('dis');
	}else{
		$.post(site_url+"/ajax.php", { action : 'SelectCity' , id: var3 } , function(data){ 
			if (data != ""){
				$('#var4').html('');
				$('#var4').append(data);
				$('#var4').removeClass('dis');
				CheckIfReady();
			}   
    	});
	}
	CheckIfReady();
}
//--------------------------------------------
function CheckIfReady(){
	var var1 = $('#var1').val();
	var var2 = $('#var2').val();
	//var var3 = $('#var3').val();
	//var var4 = $('#var4').val();
	if (var1 != "0"){
		$('#res').html('<a class="search-btn" href="'+site_url+'/search.php?category='+var1+'&sub-category='+var2+'">نتيجة البحث</a>');
	}	
}
//--------------------------------------------
function Rate(rate,productid){
	if (User_ID == '0'){
		LoginForm();
	}else{
		dothis = 0;
		for (var i = rate ; i >= 1; i--) {
			$('#s'+i).removeClass("fa-star-o");
	    	$('#s'+i).addClass("fa-star");
	    	$('#s'+i).addClass("arate");
		}
		$.post(site_url+"/ajax.php", { action : 'Rate' , rate: rate , productid: productid } , function(data){ 
			if (data != ""){
				$('#res_rate').html(data);
				window.setTimeout(function() {$("#res_rate").text('')}, 3000);
			}   
		});
	}
}
//--------------------------------------------
function ShowPhone(productid){
	if (User_ID == '0'){
		LoginForm();
	}else{
		var vphone = $('#vphone').val();
		$('#phone-btn').html('<i class="fa fa-phone"></i> '+vphone+'<b id="show-phone-res">إتصل الآن<b>');
		$.post(site_url+"/ajax.php", { action : 'ShowPhone' , productid: productid } , function(data){ });
	}
}
//--------------------------------------------
function LoginForm(){
	$('#login_res').html('');
	$('#register_res').html('');
	$("#Form").modal("show");
}
//--------------------------------------------
function Login(){
	$('#login_res').html('');
	$('#register_res').html('');

	var log_var1 = $('#log_var1').val();
	var log_var2 = $('#log_var2').val();
	if (log_var1 == "" || log_var2 == ""){
		$('#login_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة إسم المستخدم وكلمة المرور.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Login' , log_var1: log_var1 , log_var2: log_var2 } , function(data){ 
		if (data != ""){
			$('#login_res').html(data);
		}   
	});
	}
}
//--------------------------------------------
function nLogin(){
	$('#nlogin_res').html('');

	var log_var1 = $('#nlog_var1').val();
	var log_var2 = $('#nlog_var2').val();
	if (log_var1 == "" || log_var2 == ""){
		$('#nlogin_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك كتابة إسم المستخدم وكلمة المرور.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Login' , log_var1: log_var1 , log_var2: log_var2 } , function(data){ 
		if (data != ""){
			$('#nlogin_res').html(data);
		}   
	});
	}
}

//--------------------------------------------
function Register(){
	$('#login_res').html('');
	$('#register_res').html('');

	var reg_var1 = $('#reg_var1').val();
	var reg_var2 = $('#reg_var2').val();
	var reg_var3 = $('#reg_var3').val();
	var reg_var4 = $('#reg_var4').val();
	if (reg_var1 == "" || reg_var2 == "" || reg_var3 == "" || reg_var4 == ""){
		$('#register_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك ملئ جميع الحقول.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Register' , reg_var1: reg_var1 , reg_var2: reg_var2 , reg_var3: reg_var3 , reg_var4: reg_var4} , function(data){ 
		if (data != ""){
			$('#register_res').html(data);
		}   
	});
	}
}
//--------------------------------------------
function nRegister(){
	$('#nregister_res').html('');

	var reg_var1 = $('#nreg_var1').val();
	var reg_var2 = $('#nreg_var2').val();
	var reg_var3 = $('#nreg_var3').val();
	var reg_var4 = $('#nreg_var4').val();
	if (reg_var1 == "" || reg_var2 == "" || reg_var3 == "" || reg_var4 == ""){
		$('#nregister_res').html('<div class="index_main"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>يجب عليك ملئ جميع الحقول.</div></div>');
	}else{
	$.post(site_url+"/ajax.php", { action : 'Register' , reg_var1: reg_var1 , reg_var2: reg_var2 , reg_var3: reg_var3 , reg_var4: reg_var4} , function(data){ 
		if (data != ""){
			$('#nregister_res').html(data);
		}   
	});
	}
}