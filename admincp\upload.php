<?php
ob_start();
include('../webset.php'); 
include('../session.php');

 if ( is_dir ("../files/") === false ){
 mkdir("../files/");
}

  $path = '../files/' ;


function getExtension($str) 
{

         $i = strrpos($str,".");
         if (!$i) { return ""; } 

         $l = strlen($str) - $i;
         $ext = substr($str,$i+1,$l);
         return $ext;
 }

  $valid_formats = array("jpg", "png", "gif", "bmp","jpeg","PNG","JPG","JPEG","webp","WEBP","GIF","BMP","MP4","mp4");
  if(isset($_POST) and $_SERVER['REQUEST_METHOD'] == "POST")
    {
      $name = $_FILES['photo']['name'];
      $size = $_FILES['photo']['size'];
    
  
      if(strlen($name))
        {
           $ext = getExtension($name);
          if(in_array($ext,$valid_formats))
          {
           
              $actual_image_name = time().".".$ext;
              $tmp = $_FILES['photo']['tmp_name'];
              if(move_uploaded_file($tmp, $path.$actual_image_name))
                {
                
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'LOGO' ){
                    Update( 'Logo' , 'files/'.$actual_image_name );
                  }
                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'DefaultImage' ){
                    Update( 'DefaultImage' , 'files/'.$actual_image_name );
                  }

                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'Slider' && isset($_POST['id']) ){
                    
                    $stmt = $db->prepare("UPDATE slider SET photo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'files/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }

                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'offers' && isset($_POST['id']) ){
                    
                    $stmt = $db->prepare("UPDATE offers SET photo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'files/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }


                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'Category' && isset($_POST['id']) ){
                    
                    $stmt = $db->prepare("UPDATE category SET photo = :var1 WHERE  id = :var0 ");  
                    $stmt->execute(array('var1' => 'files/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                  }

                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'products' && isset($_POST['id']) ){
                    $ch = getAllFrom('*' , 'products_title' , 'WHERE id = "'.$_POST['id'].'" ', '');

                    if (count($ch) > 0 ){
                      if($ext == 'mp4'){ 
                        $stmt = $db->prepare("UPDATE products_title SET video = :var1 WHERE  id = :var0 ");  
                        $stmt->execute(array('var1' =>  'files/'.$actual_image_name , 'var0' => $_POST['id'] )); 
                      }else{
                        $stmt = $db->prepare("INSERT INTO photos ( link , datetime ) VALUES (:user_1 , :user_2 )");  
                        $stmt->execute(array('user_1' => 'files/'.$actual_image_name , 'user_2' => time()  )) ;
                        $id = $db->lastInsertId();

                        $stmt = $db->prepare("UPDATE products_title SET photo = :var1 WHERE  id = :var0 ");  
                        $stmt->execute(array('var1' => $ch[0]['photo'].$id.'&&&' , 'var0' => $_POST['id'] )); 
                      }
                    }
                  }

                  if (isset($_POST['Image_For']) && $_POST['Image_For'] == 'Logos' ){
                      $stmt = $db->prepare("INSERT INTO logos ( photo , datetime  ) VALUES (:user_1 ,:user_2  )");  
                      $stmt->execute(array('user_1' => 'files/'.$actual_image_name , 'user_2' => time() )) ;
                      header("refresh:0;url=logos.php");
                      exit();
                  }

                }
              else
                echo "Fail upload folder with read access.";
              redirect_home ('back' , 0);
                  
            }
            else
          redirect_home ('back' , 0);
        }
        
      else
      redirect_home ('back' , 0);
        
      exit;


    }


//redirect_home ('back' , 0);

ob_end_flush();
?>
