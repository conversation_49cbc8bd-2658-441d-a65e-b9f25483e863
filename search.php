<?php
ob_start();
$Title_page = 'نتائج البحث' ;
include('webset.php');
include('session.php');
include('header.php'); 
include('navbar.php');

//-------------------------------------------------------------------------

if (isset($_GET['search-title']) ){
	$sh  = filter_var($_GET['search-title']   , FILTER_SANITIZE_STRING) ;	
	$tit = explode(' ', $sh);
	if (count($tit) > 0){
		$q = ' WHERE ';
		$limit = 5;
		if ($limit > count($tit)) {$limit = count($tit);}
		for ($i=0; $i <= $limit-1 ; $i++) { 
			if ($i == $limit-1){
				$q = $q .' title LIKE "%'.$tit[$i].'%" ';
			}else{
				$q = $q .' title LIKE "%'.$tit[$i].'%" OR ';
			}
		}

		GetBreadcrumb(array($sh));

		echo '<div class="container-fluid products-content">';

		echo '<div class="col-md-12 padding0"><h2 class="filter-title"><span class="cat-title"><strong>'.$Title_page. ' عن "'.$sh.'"</strong></span></h2>';

		$more = getAllFrom('*' , 'products_title' , $q , ' ORDER BY rand()');
		if (count($more) > 0){
			for ($i=0; $i <= count($more)-1 ; $i++) { 
				GetProduct($more[$i]['id']);
			}
		}else{
		echo '<br>'. Show_Alert('warning' , 'لا يوجد نتائج بحث عن '.$sh);
		}

		echo ' </div></div>';
	}
	
}else{
	redirect_home ('back' , 0); exit();
}

include('footer.php'); 
ob_end_flush();
?>
