<body>


    
<?php
$category = getAllFrom('*','category' ,'WHERE status = 1 AND parent = 0','ORDER BY orders DESC ,id DESC');
$categoryx = getAllFrom('*','category' ,'WHERE status = 1 AND parent >= 0','ORDER BY orders DESC ,id DESC');
$allcategory = getAllFrom('*','category' ,'WHERE status = 1 ','ORDER BY orders DESC ,id DESC');

$AllCoupon =  getAllFrom('*' , 'coupon' , '', '');
$AllCouponJson = base64_encode(json_encode($AllCoupon));

if(count($AllCoupon) > 0){
    $rand_coupon = $AllCoupon[rand(0 , count($AllCoupon)-1)];
    echo '
    <div class="coupon-nav">
    <i class="fa fa-gift" aria-hidden="true"></i>  '.$rand_coupon['name'].' : <b>'.$rand_coupon['code'].'</b> <i class="fa fa-gift" aria-hidden="true"></i>
    </div>
    ';
}
?> 
<div class="top-nav-bar">
  
  
  
      
  
  
  
    <!-- <div class="row"> -->
        <div class="col-md-2 col-sm-12 text-center">
                <div class="logo">
                    <a href="<?php echo $Site_URL;?>"><img class="logo" src="<?php echo $Site_URL.'/'. GetTableSet ('Logo') ;?>" alt="<?php echo $Site_Name ;?>"></a>
                </div>
        </div>
  
        <div class="col-md-4 col-sm-12">
  
                    
                    <div class="left-menu clearfix hideonmob">
                        <div class="menu-users">
                        <?php
                        if (isset($_SESSION['userData'])){
                            if ($User_Type >= 1){
                                echo'<div class="usbox">
                                        <a title="الإدارة" href="'.$Site_URL.'/admincp/"><i class="fa fa-user-secret"></i><span>الإدارة</span></a>	
                                    </div>
                                    <div class="usbox">
                                        <a title="الخروج" href="'.$Site_URL.'/logout.php"><i class="fa fa-sign-out"></i><span>الخروج</span></a>
                                    </div>';
                            }else{
                                echo'<div class="usbox">
                                        <a title="حسابي" href="'.$Site_URL.'/account"><i class="fa fa-user-secret"></i><span>حسابي</span></a>	
                                    </div>
                                    <div class="usbox">
                                        <a title="الخروج" href="'.$Site_URL.'/logout.php"><i class="fa fa-sign-out"></i><span>الخروج</span></a>
                                    </div>';
                            }
                        }else{
                            echo'<div class="usbox">
                                    <a href="'.$Site_URL.'/login.php"><i class="fa fa-user"></i><span>دخول</span></a>
                                </div>
                                <div class="usbox">
                                    <a href="'.$Site_URL.'/register.php"><i class="fa fa-user-plus"></i><span>تسجيل</span></a>
                                </div>';
                        }
                        ?>
                        </div>
                        <div class="cart-computer">
                            <a href="<?php echo $Site_URL.'/cart'?>" onclick="//ShowCart()"><p> <span id="ccrt"><?php echo count($_SESSION['UserCart']) ;?></span> السلة <i class="fa fa-shopping-basket"></i></p></a>
                        </div>
                </div>
                <div class="navbarlink">
                    <a href="https://shebox.co/category/evening-dresses/" >جميع الفساتين</a>
                    <a href="https://shebox.co/category/%D8%AA%D8%B3%D9%88%D9%82%D9%8A-%D8%AD%D8%B3%D8%A8-%D8%A7%D9%84%D9%84%D9%88%D9%86" >تسوقي حسب اللون</a>

                </div>

                <div class="col-md padding0 margintop20 hideonmob">
                
                <div class="menu <?php if (isset($thisIsHomePage)){echo '0' ;}?>">
                        <p> <i class="fa fa-th-list" aria-hidden="true"></i>  تسوقي الان </p>
                        <ul class="catul-menu">
                            <?php
                            if (count($category) > 0){
                                for ($i=0; $i <= count($category)-1 ; $i++) { 
                            ?>
                                <li class="cat-menu-title"><a href="<?php echo $Site_URL.'/category/'.$category[$i]['link'] ;?>"><?php echo $category[$i]['name'];?></a><i class="fa fa-caret-left"></i>
                                        <ol class="sub-cat-menu">
                                                <?php
                                                    if (!empty($category[$i]['photo'])){
                                                ?>
                                                    <img alt="<?php echo $category[$i]['name'];?>" src="<?php echo $Site_URL.'/'. $category[$i]['photo']; ?>" title="<?php echo $category[$i]['name'];?>" />
                                                <?php
                                                    }else{
                                                ?>
                                                    <img alt="<?php echo $category[$i]['name'];?>" src="<?php echo $Site_URL.'/'. GetTableSet ('DefaultImage'); ?>" title="<?php echo $category[$i]['name'];?>" /> 
                                                <?php
                                                    }
                                                    $sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
                                                    if (count($sub) > 0){
                                                        for ($x=0; $x <= count($sub)-1 ; $x++) { 
                                                            echo '<div><a href="'.$Site_URL.'/category/'.$sub[$x]['link'].'" class="sub-menu-link">'.$sub[$x]['name'].'</a></div>';
                                                        }
                                                    }
                                                ?>			
                                        </ol>
                                </li>
                            <?php
                                }
                            }
                            ?>	
                            
                        </ul>
                </div>
                
                <div class="left-menu clearfix">
                  
                       
                </div>
                <div class="clearfix"></div>
        </div>
                    
                    </div>

                     

   
        <!-- </div> -->
        <a class="showmobmenu" id="showmobmenu" onclick="ShowMobMenu()"><i class="fa fa-th-list"></i></a>
        <a class="shopping-cart" href="<?php echo $Site_URL.'/cart'?>" onclick="//ShowCart()"><i class="fa fa-shopping-basket"></i> <span id="cart_count"><?php echo count($_SESSION['UserCart']) ;?></span></a> 
</div>


<div class="mobile-menu" id="mobile-menu">
        <div class="mobile-menu-top">
                <p>القائمة</p>
                <a onclick="CloseMobMenu()"><i class="fa fa-times"></i></a>					
        </div>
        <ul>
                <?php
                    for ($i=0; $i <= count($category)-1 ; $i++) { 
                        echo '<li class="cat-mob-menu-title"><i class="fa fa-plus-circle"></i><a href="'.$Site_URL.'/category/'.$category[$i]['link'].'">'.$category[$i]['name'].'</a>';
                            $sub = getAllFrom('*' , 'category' , 'WHERE status = 1 AND parent = "'.$category[$i]['id'].'" ', 'ORDER BY orders DESC, id DESC');
                            if (count($sub) > 0){
                                echo '<ol class="sub-cat-mob-menu">';
                                for ($x=0; $x <= count($sub)-1 ; $x++) { 
                                    echo '<div><a href="'.$Site_URL.'/category/'.$sub[$x]['link'].'" class="sub-mob-menu-link">'.$sub[$x]['name'].'</a></div>';
                                }   
                                echo '</ol>';

                            }
                        echo '</li>';
                    }
                ?>	
        </ul>
</div>
<div class="clearfix"></div>

<div id="CartData" class="modal fade" data-backdrop="static" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-body">
          <div class="CartDataClose">
            <h2>سلة المشتريات</h2>
            <a data-dismiss="modal"><i class="fa fa-window-close" aria-hidden="true"></i></a>
          </div>
          <div id="CartDataRes"></div>  
      </div>
    </div>
  </div>
</div>
  
 
  
<div class="min-content">
  
  
  

   