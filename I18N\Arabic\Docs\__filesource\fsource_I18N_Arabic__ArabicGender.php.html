<html>
<head>
<title>File Source for Gender.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">I18N_Arabic</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_I18N_Arabic.html" class="menu">class tree: I18N_Arabic</a> ]
		  [ <a href="../elementindex_I18N_Arabic.html" class="menu">index: I18N_Arabic</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_I18N_Arabic.html">I18N_Arabic</a><br />
            <br /><br />
                  
      
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1 align="center">Source for file Gender.php</h1>
<p>Documentation is available at <a href="../I18N_Arabic/_Arabic---Gender.php.html">Gender.php</a></p>
<div class="src-code">
<ol><li><div class="src-line"><a name="a1"></a><span class="src-php">&lt;?php</span></div></li>
<li><div class="src-line"><a name="a2"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a3"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a4"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a5"></a><span class="src-doc">&nbsp;*&nbsp;Copyright&nbsp;(c)&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa.</span></div></li>
<li><div class="src-line"><a name="a6"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a7"></a><span class="src-doc">&nbsp;*&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a8"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a9"></a><span class="src-doc">&nbsp;*&nbsp;PHP&nbsp;Version&nbsp;5</span></div></li>
<li><div class="src-line"><a name="a10"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a11"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a12"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a13"></a><span class="src-doc">&nbsp;*&nbsp;LICENSE</span></div></li>
<li><div class="src-line"><a name="a14"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a15"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;open&nbsp;source&nbsp;product;&nbsp;you&nbsp;can&nbsp;redistribute&nbsp;it&nbsp;and/or</span></div></li>
<li><div class="src-line"><a name="a16"></a><span class="src-doc">&nbsp;*&nbsp;modify&nbsp;it&nbsp;under&nbsp;the&nbsp;terms&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;(LGPL)</span></div></li>
<li><div class="src-line"><a name="a17"></a><span class="src-doc">&nbsp;*&nbsp;as&nbsp;published&nbsp;by&nbsp;the&nbsp;Free&nbsp;Software&nbsp;Foundation;&nbsp;either&nbsp;version&nbsp;3</span></div></li>
<li><div class="src-line"><a name="a18"></a><span class="src-doc">&nbsp;*&nbsp;of&nbsp;the&nbsp;License,&nbsp;or&nbsp;(at&nbsp;your&nbsp;option)&nbsp;any&nbsp;later&nbsp;version.</span></div></li>
<li><div class="src-line"><a name="a19"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a20"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;program&nbsp;is&nbsp;distributed&nbsp;in&nbsp;the&nbsp;hope&nbsp;that&nbsp;it&nbsp;will&nbsp;be&nbsp;useful,</span></div></li>
<li><div class="src-line"><a name="a21"></a><span class="src-doc">&nbsp;*&nbsp;but&nbsp;WITHOUT&nbsp;ANY&nbsp;WARRANTY;&nbsp;without&nbsp;even&nbsp;the&nbsp;implied&nbsp;warranty&nbsp;of</span></div></li>
<li><div class="src-line"><a name="a22"></a><span class="src-doc">&nbsp;*&nbsp;MERCHANTABILITY&nbsp;or&nbsp;FITNESS&nbsp;FOR&nbsp;A&nbsp;PARTICULAR&nbsp;PURPOSE.&nbsp;&nbsp;See&nbsp;the</span></div></li>
<li><div class="src-line"><a name="a23"></a><span class="src-doc">&nbsp;*&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License&nbsp;for&nbsp;more&nbsp;details.</span></div></li>
<li><div class="src-line"><a name="a24"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a25"></a><span class="src-doc">&nbsp;*&nbsp;You&nbsp;should&nbsp;have&nbsp;received&nbsp;a&nbsp;copy&nbsp;of&nbsp;the&nbsp;GNU&nbsp;Lesser&nbsp;General&nbsp;Public&nbsp;License</span></div></li>
<li><div class="src-line"><a name="a26"></a><span class="src-doc">&nbsp;*&nbsp;along&nbsp;with&nbsp;this&nbsp;program.&nbsp;&nbsp;If&nbsp;not,&nbsp;see&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;.</span></div></li>
<li><div class="src-line"><a name="a27"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a28"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a29"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a30"></a><span class="src-doc">&nbsp;*&nbsp;Class&nbsp;Name:&nbsp;Arabic&nbsp;Gender&nbsp;Guesser</span></div></li>
<li><div class="src-line"><a name="a31"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a32"></a><span class="src-doc">&nbsp;*&nbsp;Filename:&nbsp;&nbsp;&nbsp;Gender.php</span></div></li>
<li><div class="src-line"><a name="a33"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a34"></a><span class="src-doc">&nbsp;*&nbsp;Original&nbsp;&nbsp;&nbsp;&nbsp;Author(s):&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a35"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a36"></a><span class="src-doc">&nbsp;*&nbsp;Purpose:&nbsp;&nbsp;&nbsp;&nbsp;This&nbsp;class&nbsp;attempts&nbsp;to&nbsp;guess&nbsp;the&nbsp;gender&nbsp;of&nbsp;Arabic&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a37"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a38"></a><span class="src-doc">&nbsp;*&nbsp;----------------------------------------------------------------------</span></div></li>
<li><div class="src-line"><a name="a39"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a40"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;Gender&nbsp;Guesser</span></div></li>
<li><div class="src-line"><a name="a41"></a><span class="src-doc">&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a42"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;attempts&nbsp;to&nbsp;guess&nbsp;the&nbsp;gender&nbsp;of&nbsp;Arabic&nbsp;names.</span></div></li>
<li><div class="src-line"><a name="a43"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a44"></a><span class="src-doc">&nbsp;*&nbsp;Arabic&nbsp;nouns&nbsp;are&nbsp;either&nbsp;masculine&nbsp;or&nbsp;feminine.&nbsp;Usually&nbsp;when&nbsp;referring&nbsp;to&nbsp;a&nbsp;male,</span></div></li>
<li><div class="src-line"><a name="a45"></a><span class="src-doc">&nbsp;*&nbsp;a&nbsp;masculine&nbsp;noun&nbsp;is&nbsp;usually&nbsp;used&nbsp;and&nbsp;when&nbsp;referring&nbsp;to&nbsp;a&nbsp;female,&nbsp;a&nbsp;feminine&nbsp;noun</span></div></li>
<li><div class="src-line"><a name="a46"></a><span class="src-doc">&nbsp;*&nbsp;is&nbsp;used.&nbsp;In&nbsp;most&nbsp;cases&nbsp;the&nbsp;feminine&nbsp;noun&nbsp;is&nbsp;formed&nbsp;by&nbsp;adding&nbsp;a&nbsp;special&nbsp;characters</span></div></li>
<li><div class="src-line"><a name="a47"></a><span class="src-doc">&nbsp;*&nbsp;to&nbsp;the&nbsp;end&nbsp;of&nbsp;the&nbsp;masculine&nbsp;noun.&nbsp;Its&nbsp;not&nbsp;just&nbsp;nouns&nbsp;referring&nbsp;to&nbsp;people&nbsp;that</span></div></li>
<li><div class="src-line"><a name="a48"></a><span class="src-doc">&nbsp;*&nbsp;have&nbsp;gender.&nbsp;Inanimate&nbsp;objects&nbsp;(doors,&nbsp;houses,&nbsp;cars,&nbsp;etc.)&nbsp;is&nbsp;either&nbsp;masculine&nbsp;or</span></div></li>
<li><div class="src-line"><a name="a49"></a><span class="src-doc">&nbsp;*&nbsp;feminine.&nbsp;Whether&nbsp;an&nbsp;inanimate&nbsp;noun&nbsp;is&nbsp;masculine&nbsp;or&nbsp;feminine&nbsp;is&nbsp;mostly</span></div></li>
<li><div class="src-line"><a name="a50"></a><span class="src-doc">&nbsp;*&nbsp;arbitrary.</span></div></li>
<li><div class="src-line"><a name="a51"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a52"></a><span class="src-doc">&nbsp;*&nbsp;Example:</span></div></li>
<li><div class="src-line"><a name="a53"></a><span class="src-doc">&nbsp;*&nbsp;&lt;code&gt;</span></div></li>
<li><div class="src-line"><a name="a54"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;include('./I18N/Arabic.php');</span></div></li>
<li><div class="src-line"><a name="a55"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;$obj&nbsp;=&nbsp;new&nbsp;I18N_Arabic('Gender');</span></div></li>
<li><div class="src-line"><a name="a56"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a57"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;echo&nbsp;&quot;$name&nbsp;&quot;;</span></div></li>
<li><div class="src-line"><a name="a58"></a><span class="src-doc">&nbsp;*&nbsp;</span></div></li>
<li><div class="src-line"><a name="a59"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;if&nbsp;($obj-&gt;isFemale($name)&nbsp;==&nbsp;true)&nbsp;{</span></div></li>
<li><div class="src-line"><a name="a60"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;'(Female)';</span></div></li>
<li><div class="src-line"><a name="a61"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;}else{</span></div></li>
<li><div class="src-line"><a name="a62"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;'(Male)';</span></div></li>
<li><div class="src-line"><a name="a63"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;}</span></div></li>
<li><div class="src-line"><a name="a64"></a><span class="src-doc">&nbsp;*&nbsp;&lt;/code&gt;</span></div></li>
<li><div class="src-line"><a name="a65"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a66"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a67"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a68"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a69"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a70"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a71"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a72"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a73"></a><span class="src-doc">&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a74"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a75"></a><span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a76"></a><span class="src-doc">&nbsp;*&nbsp;This&nbsp;PHP&nbsp;class&nbsp;attempts&nbsp;to&nbsp;guess&nbsp;the&nbsp;gender&nbsp;of&nbsp;Arabic&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a77"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a78"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@category</span><span class="src-doc">&nbsp;&nbsp;I18N</span></div></li>
<li><div class="src-line"><a name="a79"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@package</span><span class="src-doc">&nbsp;&nbsp;&nbsp;I18N_Arabic</span></div></li>
<li><div class="src-line"><a name="a80"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a81"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@copyright</span><span class="src-doc">&nbsp;2006-2016&nbsp;Khaled&nbsp;Al-Sham'aa</span></div></li>
<li><div class="src-line"><a name="a82"></a><span class="src-doc">&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a83"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@license</span><span class="src-doc">&nbsp;&nbsp;&nbsp;LGPL&nbsp;&lt;http://www.gnu.org/licenses/lgpl.txt&gt;</span></div></li>
<li><div class="src-line"><a name="a84"></a><span class="src-doc">&nbsp;*&nbsp;</span><span class="src-doc-coretag">@link</span><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://www.ar-php.org</span></div></li>
<li><div class="src-line"><a name="a85"></a><span class="src-doc">&nbsp;*/&nbsp;</span></div></li>
<li><div class="src-line"><a name="a86"></a><span class="src-key">class&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Gender.html">I18N_Arabic_Gender</a></div></li>
<li><div class="src-line"><a name="a87"></a><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a88"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a89"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Loads&nbsp;initialize&nbsp;values</span></div></li>
<li><div class="src-line"><a name="a90"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></div></li>
<li><div class="src-line"><a name="a91"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@ignore</span></div></li>
<li><div class="src-line"><a name="a92"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a93"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">function&nbsp;</span><span class="src-id">__construct</span><span class="src-sym">(</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a94"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a95"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a96"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a97"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-doc">/**</span></div></li>
<li><div class="src-line"><a name="a98"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;Arabic&nbsp;word&nbsp;is&nbsp;feminine</span></div></li>
<li><div class="src-line"><a name="a99"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a100"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@param&nbsp;</span><span class="src-doc-type">string&nbsp;</span><span class="src-doc-var">$str&nbsp;</span><span class="src-doc">Arabic&nbsp;word&nbsp;you&nbsp;would&nbsp;like&nbsp;to&nbsp;check&nbsp;if&nbsp;it&nbsp;is</span></div></li>
<li><div class="src-line"><a name="a101"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;feminine</span></div></li>
<li><div class="src-line"><a name="a102"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div></li>
<li><div class="src-line"><a name="a103"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@return&nbsp;</span><span class="src-doc-type">boolean&nbsp;</span><span class="src-doc">Return&nbsp;true&nbsp;if&nbsp;input&nbsp;Arabic&nbsp;word&nbsp;is&nbsp;feminine</span></div></li>
<li><div class="src-line"><a name="a104"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;</span><span class="src-doc-coretag">@author</span><span class="src-doc">&nbsp;Khaled&nbsp;Al-Sham'aa&nbsp;&lt;<EMAIL>&gt;</span></div></li>
<li><div class="src-line"><a name="a105"></a><span class="src-doc">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></div></li>
<li><div class="src-line"><a name="a106"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">public&nbsp;</span><span class="src-key">static&nbsp;</span><span class="src-key">function&nbsp;</span><a href="../I18N_Arabic/I18N_Arabic_Gender.html#methodisFemale">isFemale</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a107"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a108"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$female&nbsp;</span>=&nbsp;<span class="src-id">false</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a109"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div></li>
<li><div class="src-line"><a name="a110"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$words&nbsp;</span>=&nbsp;<a href="http://www.php.net/explode">explode</a><span class="src-sym">(</span><span class="src-str">'&nbsp;'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a111"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;&nbsp;&nbsp;</span>=&nbsp;<span class="src-var">$words</span><span class="src-sym">[</span><span class="src-num">0</span><span class="src-sym">]</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a112"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a113"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$str&nbsp;</span>=&nbsp;<a href="http://www.php.net/str_replace">str_replace</a><span class="src-sym">(</span><span class="src-key">array</span><span class="src-sym">(</span><span class="src-str">'أ'</span><span class="src-sym">,</span><span class="src-str">'إ'</span><span class="src-sym">,</span><span class="src-str">'آ'</span><span class="src-sym">)</span><span class="src-sym">,&nbsp;</span><span class="src-str">'ا'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a114"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a115"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$last&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">1</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">,&nbsp;</span><span class="src-str">'UTF-8'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a116"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$beforeLast&nbsp;</span>=&nbsp;<a href="http://www.php.net/mb_substr">mb_substr</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">,&nbsp;</span>-<span class="src-num">2</span><span class="src-sym">,&nbsp;</span><span class="src-num">1</span><span class="src-sym">,&nbsp;</span><span class="src-str">'UTF-8'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a117"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a118"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><span class="src-var">$last&nbsp;</span>==&nbsp;<span class="src-str">'ة'&nbsp;</span>||&nbsp;<span class="src-var">$last&nbsp;</span>==&nbsp;<span class="src-str">'ه'&nbsp;</span>||&nbsp;<span class="src-var">$last&nbsp;</span>==&nbsp;<span class="src-str">'ى'&nbsp;</span>||&nbsp;<span class="src-var">$last&nbsp;</span>==&nbsp;<span class="src-str">'ا'&nbsp;</span></div></li>
<li><div class="src-line"><a name="a119"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;||&nbsp;<span class="src-sym">(</span><span class="src-var">$last&nbsp;</span>==&nbsp;<span class="src-str">'ء'&nbsp;</span>&amp;&amp;&nbsp;<span class="src-var">$beforeLast&nbsp;</span>==&nbsp;<span class="src-str">'ا'</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a120"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a121"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a122"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$female&nbsp;</span>=&nbsp;<span class="src-id">true</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a123"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">elseif&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/preg_match">preg_match</a><span class="src-sym">(</span><span class="src-str">&quot;/^[اإ].{2}ا.$/u&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)&nbsp;</span></div></li>
<li><div class="src-line"><a name="a124"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;||&nbsp;<a href="http://www.php.net/preg_match">preg_match</a><span class="src-sym">(</span><span class="src-str">&quot;/^[إا].ت.ا.+$/u&quot;</span><span class="src-sym">,&nbsp;</span><span class="src-var">$str</span><span class="src-sym">)</span></div></li>
<li><div class="src-line"><a name="a125"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a126"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;الأسماء&nbsp;على&nbsp;وزن&nbsp;إفتعال&nbsp;و&nbsp;إفعال</span></div></li>
<li><div class="src-line"><a name="a127"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$female&nbsp;</span>=&nbsp;<span class="src-id">true</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a128"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}&nbsp;</span><span class="src-key">else&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a129"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-comm">//&nbsp;List&nbsp;of&nbsp;the&nbsp;most&nbsp;common&nbsp;irregular&nbsp;Arabic&nbsp;female&nbsp;names</span></div></li>
<li><div class="src-line"><a name="a130"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$names&nbsp;</span>=&nbsp;<a href="http://www.php.net/file">file</a><span class="src-sym">(</span><a href="http://www.php.net/dirname">dirname</a><span class="src-sym">(</span>__FILE__<span class="src-sym">)</span>.<span class="src-str">'/data/female.txt'</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a131"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$names&nbsp;</span>=&nbsp;<a href="http://www.php.net/array_map">array_map</a><span class="src-sym">(</span><span class="src-str">'trim'</span><span class="src-sym">,&nbsp;</span><span class="src-var">$names</span><span class="src-sym">)</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a132"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a133"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">if&nbsp;</span><span class="src-sym">(</span><a href="http://www.php.net/array_search">array_search</a><span class="src-sym">(</span><span class="src-var">$str</span><span class="src-sym">,&nbsp;</span><span class="src-var">$names</span><span class="src-sym">)&nbsp;</span>&gt;&nbsp;<span class="src-num">0</span><span class="src-sym">)&nbsp;</span><span class="src-sym">{</span></div></li>
<li><div class="src-line"><a name="a134"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-var">$female&nbsp;</span>=&nbsp;<span class="src-id">true</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a135"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a136"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a137"></a>&nbsp;</div></li>
<li><div class="src-line"><a name="a138"></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-key">return&nbsp;</span><span class="src-var">$female</span><span class="src-sym">;</span></div></li>
<li><div class="src-line"><a name="a139"></a>&nbsp;&nbsp;&nbsp;&nbsp;<span class="src-sym">}</span></div></li>
<li><div class="src-line"><a name="a140"></a><span class="src-sym">}</span></div></li>
</ol>
</div>
        <div class="credit">
		    <hr />
		    Documentation generated on Fri, 01 Jan 2016 10:25:59 +0200 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.0</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>