<?php
$Title_page = 'حسابي';
include('header.php'); 
include('navbar.php');

echo '<style>.form-control,.btn{margin-bottom: 10px;}</style>';
GetBreadcrumb(array($Title_page));
echo '
<div class="container-fluid">
    <div class="col-md-3">
        <h2 class="filter-title"><span class="cat-title"><strong>تعديل بيانات الحساب</strong></span></h2><hr>
        <form method="post">
            <div class="row">
                <div class="col-md-12">
                    <label>الإسم بالكامل</label>
                    <input type="text" class="form-control" name="var1" value="'.$FullName.'" placeholder="اكتب اسمك">
                </div>
                <div class="col-md-12">
                    <label>رقم الموبايل</label>
                    <input type="text" class="form-control" name="var2" value="'.$UserPhone.'" placeholder="رقم الموبايل">
                </div>
                
                <div class="col-md-12">
                    <button type="submit" name="edit1" class="btn btn-md btn-success">تعديل البيانات</button> 
                </div>
                <div class="col-md-12">
                ';
                if (isset($_POST['edit1'])){
                    $var1 = filter_var($_POST['var1']  , FILTER_SANITIZE_STRING) ;
                    $var2 = filter_var($_POST['var2']  , FILTER_SANITIZE_STRING) ;
                    if(trim($var1) == '' || trim($var2) == ''){
                        echo Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول');
                    }else{
                        $stmt = $db->prepare("UPDATE users SET fullname = :var1 , phone = :var2 WHERE  id = :var0 ");  
                        $stmt->execute(array('var1' => $var1 , 'var2' => $var2 , 'var0' => $User_ID )); 
                        header("Location:".$Site_URL.'/account'); exit();
                    } 
                }
                echo '  
                </div>
            </div>
        </form>
        <h2 class="filter-title"><span class="cat-title"><strong>تغير كلمة المرور</strong></span></h2><hr>
        <form method="post">
            <div class="row">
                <div class="col-md-12">
                    <label>كلمة المرور القديمه</label>
                    <input type="password" class="form-control" name="var1">
                </div>
                <div class="col-md-12">
                    <label>كلمة المرور الجديده</label>
                    <input type="password" class="form-control" name="var2">
                </div>
                <div class="col-md-12">
                    <label>تأكيد كلمة المرور الجديده</label>
                    <input type="password" class="form-control" name="var3">
                </div>
                
                <div class="col-md-12">
                    <button type="submit" name="edit2" class="btn btn-md btn-success">تغير كلمة المرور</button> 
                </div>
                <div class="col-md-12">
                ';
                if (isset($_POST['edit2'])){
                    $var1 = filter_var($_POST['var1']  , FILTER_SANITIZE_STRING) ;
                    $var2 = filter_var($_POST['var2']  , FILTER_SANITIZE_STRING) ;
                    $var3 = filter_var($_POST['var3']  , FILTER_SANITIZE_STRING) ;
                    if(trim($var1) == '' || trim($var2) == '' || trim($var2) == ''){
                        echo Show_Alert('danger' , 'يجب عليك ملئ جميع الحقول');
                    }elseif(trim($var1) != $UserPassword){
                        echo Show_Alert('danger' , 'كلمة المرور القديمه غير صحيحه');
                    }elseif(trim($var2) != trim($var3)){
                        echo Show_Alert('danger' , 'كلمات المرور الجديده غير متطابقه');
                    }else{
                        $stmt = $db->prepare("UPDATE users SET password = :var1 WHERE  id = :var0 ");  
                        $stmt->execute(array('var1' => $var3 ,  'var0' => $User_ID )); 
                        header("Location:".$Site_URL.'/account'); exit();
                    } 
                }
                echo '  
                </div>
            </div>
        </form>
    </div>
    <div class="col-md-9">
        <h2 class="filter-title"><span class="cat-title"><strong>الأوردرات</strong></span></h2>
        ';  
        $orders = getAllFrom('*' , 'orders' , 'WHERE userid = "'.$User_ID.'" ', 'ORDER BY id DESC LIMIT 20');
        if(count($orders) > 0){
            for($i=0; $i<=count($orders)-1; $i++){
                echo '
                <div class="panel address-box">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            أوردر رقم '.$orders[$i]['orderhash'].'
                            <span class="fz12">حالة الأوردر : '.GetMyOrderStatus($orders[$i]['status']).'</span>
                        </h3>
                    </div>
                    <div class="panel-body">
                        <table class="table">
                        <thead>
                        <tr>
                            <th scope="col">المنتج</th>
                            <th scope="col">السعر</th>
                            <th scope="col">الإضافات</th>
                            <th scope="col">الكمية</th>
                            <th scope="col">الشحن</th>
                        </tr>
                        </thead>
                        <tbody>
                        ';
                        $products = json_decode($orders[$i]['products']);
                        for($x=0; $x<=count($products)-1; $x++){
                            echo '
                            <tr>
                                <th class="title" scope="row">'.$products[$x]->title.'</th>
                                <th class="title" scope="row">'.$products[$x]->price.'</th>
                                <th class="title" scope="row">'.$products[$x]->filter.'</th>
                                <th class="title" scope="row">'.$products[$x]->count.'</th>
                                <th class="title" scope="row">'.$products[$x]->ship.'</th>
                                 
                            </tr>
                            ';
                        }
                    echo '</tbody>
                    </table>
                    </div>
                </div>
                ';
            }
        }else{
            echo '<br>'.Show_Alert('warning' , 'لا يوجد اى أوردرات حتى الآن');
        }
        echo'
    </div>
</div>
';

include('footer.php'); 