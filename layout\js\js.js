$(document).ready(function() { 
 
});

//----------------------------------------------
function Send_Alert(mes){
    $.amaran({
        'message'   :'<h6><i class="fa fa-window-close"></i></h6>'+mes ,
        'closeOnClick':true,
        'delay'     :30000,
        'cssanimationIn'    :'boundeInDown',
        'cssanimationOut'   :'zoomOutUp',
        'position'          :'top right',
    });
}
//----------------------------------------------
function Paly_Sound (){
      ion.sound({
        sounds: [
            {
                name: "notifications"
            }
        ],
        volume: 1.0,
        path: "sounds/",
        preload: true
   
    });
       ion.sound.play(name);
} 
//----------------------------------------------
$(".cat-menu-title").on({
    mouseenter: function () {
        $(this).find('ol').show(100);
    },
    mouseleave: function () {
        $('.sub-cat-menu').hide();
    }
});
function ShowAllCat(){
$('.catul').show();
}
function setCatVal(thisval){
  $('.catul').hide();
    $('#ddwonr').text($(thisval).text());
}

$("#ddwon").on("mouseover", function () {
    //$('.catul').show(100);
});

$(".menu").on("click", function () {
    $('.menu').toggleClass('open');
});

$(".cat-mob-menu-title").on("click", function () {
$(".cat-mob-menu-title i").attr("class","fa fa-plus-circle");
$(".sub-cat-mob-menu").hide();
$(this).find('i').attr("class","fa fa-minus-circle");
$(this).find('ol').show(100);
});

function CloseMobMenu(){
$('.mobile-menu').hide(200);
}
function ShowMobMenu(){
$('.mobile-menu').show(300);
}
$(window).click(function(e) {

if (e.target.id != 'ddwon' && $(e.target).parents('#ddwon').length == 0 ) {
    $('.catul').hide();
}

if (e.target.id != 'mobile-menu' && e.target.id != 'showmobmenu' && $(e.target).parents('#mobile-menu').length == 0 && $(e.target).parents('#showmobmenu').length == 0) {
    $('.mobile-menu').hide();
}
});
//----------------------------------------------