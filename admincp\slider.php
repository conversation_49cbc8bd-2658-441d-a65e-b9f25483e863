<?php

ob_start();

$Title_page = 'السلايدر';

include('../webset.php');

include('../session.php'); 

include('header.php'); 

include('navbar.php'); 



//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'show_hide' && isset($_GET['id']) ){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['status'] == 0){

			UpdateTable('slider' , 'status' ,1 , 'WHERE id = '.$_GET['id'] );

		}else{

			UpdateTable('slider' , 'status' ,0 , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'moveup' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] >= 0){

			UpdateTable('slider' , 'orders' ,($ch[0]['orders']+1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'movedn' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		if ($ch[0]['orders'] > 0){

			UpdateTable('slider' , 'orders' ,($ch[0]['orders']-1) , 'WHERE id = '.$_GET['id'] );

		}

	}

	redirect_home ('back' , 0);  exit();

}

//---------------------------------------------------

if (isset($_GET['action']) && $_GET['action'] == 'del' && isset($_GET['id']) && isset($_GET['confirm']) && $_GET['confirm'] == 1){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

		

			DeleteColum( 'slider', 'WHERE id = '.$_GET['id'] );

			

			header('Location: slider.php');	exit();

	}else{

		redirect_home ('back' , 0); exit();

	} 

}

//---------------------------------------------------

if (isset($_GET['do']) && $_GET['do'] == 'del' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

?>

<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body text-center">

		     

			     <h3>هل انت متأكد من انك تريد الحذف ؟</h3>

			     <p>برجاء العلم انه سيتم الحذف بشكل نهائى.</p>



			     <center>

			     	<a class="btn btn-danger btn-md" href="slider.php?action=del&confirm=1&id=<?php echo $ch[0]['id'] ;?>">تاكيد</a>

			     	<a class="btn btn-success btn-md" href="slider.php">رجوع</a>

			     </center>

		 

			</div>	

		</div>

	</div>

</div>

<?php

	}else{

		header('Location: slider.php'); exit();

	} 

//---------------------------------------------------

}elseif (isset($_GET['do']) && $_GET['do'] == 'edit' && isset($_GET['id'])){

	$ch = getAllFrom('*' , 'slider' , 'WHERE id = "'.$_GET['id'].'" ', '');

	if (count($ch) > 0 ){

	?>

	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">

					<h4 class="cattitlel">التعديل على  السلايدر</b> </h4>

				</div>

			</div>

		</div>			

	</div>		

	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">		
										<div class="col-md-6">	<br>
											<form class="m-t" role="form" method="post">
													<div class="form-group">
															<label>تعديل رابط السلايدر</label>
															<input type="text" value="<?php echo $ch[0]['link']; ?>" class="form-control ltr" name="varx1" placeholder="رابط السلايدر" required="">
													</div>
													
													<button type="submit" name="edits" class="btn btn-primary btn-lg ">تعديل الرابط</button>
													<?php
													if (isset($_POST['edits'])){
															$varx1 = filter_var($_POST['varx1']  , FILTER_SANITIZE_STRING) ;
															$stmt = $db->prepare("UPDATE slider SET link = :var1 WHERE  id = :var0 ");  
															$stmt->execute(array('var1' => $varx1 , 'var0' => $_GET['id'] )); 
															redirect_home ('back' , 0);  exit();	
													}

													?>
											</form>

										</div>

	                 <div class="col-md-6">

	                 	<label class="form-control-label" style="text-align: center; width: 100%;">صورة السلايدر</label>

					  <center>

					    <img style="margin: 20px 0px;width: 500px; max-width: 100%;" src="<?php echo $Site_URL.'/'.$ch[0]['photo'] ;?>">

					    <form id="uploadimage" action='upload.php' method="post" enctype="multipart/form-data" >

					         

					          <input type="file" name="photo" id="photo" required style="display: none;" />

					          <input type="submit" style="display: none;" id="Uploads" value="Uploads" class="submit" />

					          <input type="hidden" name="Image_For" value="Slider">

					          <input type="hidden" name="id" value="<?php echo $ch[0]['id'];?>">

					        </form>

					        <label for="photo" class="btn btn-primary btn-lg" ><i class="fa fa-camera"></i> إختر الصوره</label>

					        <label for="Uploads" class="btn btn-primary btn-lg" ><i class="fa fa-cloud-upload"></i> رفع الصوره</label>

					  </center>

	                 </div>	



				</div>

			</div>

		</div>

	</div>				

	<?php	

	}else{

		header('Location: slider.php'); exit();

	} 

//---------------------------------------------------

}elseif (isset($_GET['do']) && $_GET['do'] == 'add_new' ){

	?>

	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">

					<h4 class="cattitlel"><i class="fa fa-hand-o-left"></i> إضافة سلايدر جديد</h4>

					<p>يمكنك رفع صوره عند التعديل عليه.</p>
					<br>
				</div>

			</div>

		</div>

	</div>



	<div class="row">

		<div class="col-md-12 col-sm-12">

			<div class="card">

				<div class="card-body">				

					<?php

	                 if (isset($_POST['add_new'])){

	                 	$var1  = filter_var($_POST['var1']   , FILTER_SANITIZE_STRING) ;


						$stmt = $db->prepare("INSERT INTO slider ( photo , type , link ) 

						 VALUES (:user_1 ,:user_2 ,:user_3  )");  

						$stmt->execute(array('user_1' => GetTableSet ('DefaultImage') , 'user_2' => $var1 , 'user_3' => '#'  )) ;

				        echo '<div class="col-md-12">';

				        echo  Show_Alert('success' , 'تم إضافة سلايدر جديد بنجاح. ');

				        echo  Show_Alert('success' , 'سيتم تحويلك الى الصفحة السابقة خلال 1 ثانيه. ');

				        echo '</div>';  		 	

				        header("refresh:1;url=slider.php");

				        exit();

	                 }

	                 ?>

					<form method="post">

						<div class="col-md-6">

							<label class="form-control-label">العنوان الرئيسي</label>
							<select name="var1" class="form-control">
								<option value="0">سلايدر كمبيوتر</option>
								<option value="1">سلايدر موبايل</option>
							</select>
		                    <div class="form-group">       
		                    	<br>
		                      <input type="submit" value="إضافة" name="add_new" class="btn btn-lg btn-primary">

		                    </div>

	               	 	</div>

	                 </form>

	                 

				</div>

			</div>

		</div>

	</div>		

<?php	

//---------------------------------------------------	

}else{

?>

<div class="row">

	<div class="col-md-12 col-sm-12">

		<div class="card">

			<div class="card-body">

				<a href="slider.php?do=add_new" class="btn btn-lg btn-primary">إضافة صورة سلايدر جديده</a>

			</div>

		</div>
		<hr>
	</div>			

</div>





	<?php
	echo '<div class="row"><div class="col-md-6 col-sm-12"><h3 class="text-center">سلايدر الكمبيوتر</h3><p class="text-center">1000*393</p><br>';
		$check = getAllFrom('*' , 'slider' , 'WHERE type = 0', 'ORDER BY orders DESC ,id DESC');
		if(count($check) > 0){
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if ($check[$i]['status'] == 0 ){
				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
			}else{
				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
			}
			echo '<div class="card"><div class="card-body"><div class="divs"><hr><h4>سلايدر  '.$check[$i]['orders'].'</h4>';
			echo ' <a href="slider.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-info">تعديل</a> ';
			echo ' <a href="slider.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
			echo ' <a href="slider.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '<a href="slider.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-up"></i></a>   <a href="slider.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-down"></i></a>';
			echo '<center><img class="slidimg" src="'.$Site_URL.'/'.$check[$i]['photo'].'"></center>';
			echo '</div></div></div>';
		}
	}else{
		echo  Show_Alert('warning' , 'لا يوجد أى صور سلايدر. ');
	}	
	echo '</div>';

	echo '<div class="col-md-6 col-sm-12"><h3 class="text-center">سلايدر الموبايل</h3><p class="text-center">590*680</p><br>';
		$check = getAllFrom('*' , 'slider' , 'WHERE type = 1', 'ORDER BY orders DESC ,id DESC');
		if(count($check) > 0){
		for ($i=0; $i <= count($check)-1 ; $i++) { 
			if ($check[$i]['status'] == 0 ){
				$tit = 'مخفى' ; $cls = 'btn btn-sm btn-warning' ;
			}else{
				$tit = 'معروض' ; $cls = 'btn btn-sm btn-success' ;	
			}
			echo '<div class="card"><div class="card-body"><div class="divs"><hr><h4>سلايدر  '.$check[$i]['orders'].'</h4>';
			echo ' <a href="slider.php?do=edit&id='.$check[$i]['id'].'" class="btn btn-sm btn-info">تعديل</a> ';
			echo ' <a href="slider.php?do=show_hide&id='.$check[$i]['id'].'" class="'.$cls.'">'.$tit.'</a> ';
			echo ' <a href="slider.php?do=del&id='.$check[$i]['id'].'" class="btn btn-sm btn-danger">حذف</a> ';
			echo '<a href="slider.php?do=moveup&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-up"></i></a>   <a href="slider.php?do=movedn&id='.$check[$i]['id'].'" class="btn btn-sm btn-info smb"><i class="fa fa-arrow-down"></i></a>';
			echo '<center><img class="slidimg" src="'.$Site_URL.'/'.$check[$i]['photo'].'"></center>';
			echo '</div></div></div>';
		}
	}else{
		echo  Show_Alert('warning' , 'لا يوجد أى صور سلايدر. ');
	}	
	echo '</div></div>';
}
include('footer.php'); 
ob_end_flush();
?>